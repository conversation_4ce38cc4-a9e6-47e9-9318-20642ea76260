module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/localStorage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "getLocalStorage": (()=>getLocalStorage)
});
const STORAGE_KEYS = {
    ENTRIES: 'journal_entries',
    DRAFTS: 'journal_drafts',
    SETTINGS: 'journal_settings',
    TAGS: 'journal_tags'
};
class LocalStorageManager {
    // Settings management
    getSettings() {
        if ("TURBOPACK compile-time truthy", 1) {
            return this.getDefaultSettings();
        }
        "TURBOPACK unreachable";
    }
    saveSettings(settings) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    getDefaultSettings() {
        return {
            theme: 'system',
            fontSize: 16,
            fontFamily: 'Inter, system-ui, sans-serif',
            autoSave: true,
            autoSaveInterval: 2000,
            spellCheck: true
        };
    }
    // Draft management (for unsaved entries)
    saveDraft(draft) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    getDrafts() {
        if ("TURBOPACK compile-time truthy", 1) return [];
        "TURBOPACK unreachable";
    }
    getDraft(id) {
        const drafts = this.getDrafts();
        return drafts.find((d)=>d.id === id) || null;
    }
    deleteDraft(id) {
        try {
            const drafts = this.getDrafts().filter((d)=>d.id !== id);
            localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));
        } catch (error) {
            console.error('Error deleting draft from localStorage:', error);
        }
    }
    // Entry caching (for offline access)
    cacheEntries(entries) {
        try {
            const cached = {
                entries,
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
        } catch (error) {
            console.error('Error caching entries to localStorage:', error);
        }
    }
    getCachedEntries() {
        try {
            const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.error('Error loading cached entries from localStorage:', error);
            return null;
        }
    }
    cacheEntry(entry) {
        try {
            const cached = this.getCachedEntries();
            if (cached) {
                const existingIndex = cached.entries.findIndex((e)=>e.id === entry.id);
                if (existingIndex >= 0) {
                    cached.entries[existingIndex] = entry;
                } else {
                    cached.entries.unshift(entry);
                }
                cached.lastUpdated = new Date().toISOString();
                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
            } else {
                this.cacheEntries([
                    entry
                ]);
            }
        } catch (error) {
            console.error('Error caching entry to localStorage:', error);
        }
    }
    removeCachedEntry(id) {
        try {
            const cached = this.getCachedEntries();
            if (cached) {
                cached.entries = cached.entries.filter((e)=>e.id !== id);
                cached.lastUpdated = new Date().toISOString();
                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
            }
        } catch (error) {
            console.error('Error removing cached entry from localStorage:', error);
        }
    }
    // Tag caching
    cacheTags(tags) {
        try {
            const cached = {
                tags,
                lastUpdated: new Date().toISOString()
            };
            localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));
        } catch (error) {
            console.error('Error caching tags to localStorage:', error);
        }
    }
    getCachedTags() {
        try {
            const stored = localStorage.getItem(STORAGE_KEYS.TAGS);
            const cached = stored ? JSON.parse(stored) : null;
            return cached ? cached.tags : [];
        } catch (error) {
            console.error('Error loading cached tags from localStorage:', error);
            return [];
        }
    }
    // Utility methods
    clearAllData() {
        try {
            Object.values(STORAGE_KEYS).forEach((key)=>{
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing localStorage:', error);
        }
    }
    getStorageUsage() {
        try {
            let used = 0;
            for(let i = 0; i < localStorage.length; i++){
                const key = localStorage.key(i);
                if (key) {
                    used += localStorage.getItem(key)?.length || 0;
                }
            }
            // Estimate available space (most browsers have ~5-10MB limit)
            const available = 5 * 1024 * 1024 - used; // Assume 5MB limit
            return {
                used,
                available
            };
        } catch (error) {
            console.error('Error calculating storage usage:', error);
            return {
                used: 0,
                available: 0
            };
        }
    }
    // Auto-save functionality
    createAutoSaveTimer(callback, interval) {
        const settings = this.getSettings();
        const saveInterval = interval || settings.autoSaveInterval;
        return setInterval(callback, saveInterval);
    }
    // Export/Import functionality
    exportData() {
        try {
            const data = {
                entries: this.getCachedEntries(),
                drafts: this.getDrafts(),
                settings: this.getSettings(),
                tags: this.getCachedTags(),
                exportDate: new Date().toISOString()
            };
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('Error exporting data:', error);
            throw new Error('Failed to export data');
        }
    }
    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (data.entries) {
                localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));
            }
            if (data.drafts) {
                localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));
            }
            if (data.settings) {
                localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));
            }
            if (data.tags) {
                localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));
            }
        } catch (error) {
            console.error('Error importing data:', error);
            throw new Error('Failed to import data');
        }
    }
}
// Singleton instance
let storageInstance = null;
function getLocalStorage() {
    if (!storageInstance) {
        storageInstance = new LocalStorageManager();
    }
    return storageInstance;
}
const __TURBOPACK__default__export__ = LocalStorageManager;
}}),
"[project]/src/components/ThemeProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/localStorage.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useTheme() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
function ThemeProvider({ children }) {
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('system');
    const [resolvedTheme, setResolvedTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load theme from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const storage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLocalStorage"])();
        const settings = storage.getSettings();
        setThemeState(settings.theme);
        setMounted(true);
    }, []);
    // Update resolved theme when theme changes or system preference changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateResolvedTheme = ()=>{
            if (theme === 'system') {
                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                setResolvedTheme(systemTheme);
            } else {
                setResolvedTheme(theme);
            }
        };
        updateResolvedTheme();
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = ()=>{
            if (theme === 'system') {
                updateResolvedTheme();
            }
        };
        mediaQuery.addEventListener('change', handleChange);
        return ()=>mediaQuery.removeEventListener('change', handleChange);
    }, [
        theme
    ]);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!mounted) return;
        const root = document.documentElement;
        // Remove existing theme classes
        root.classList.remove('light', 'dark');
        // Add current theme class
        root.classList.add(resolvedTheme);
        // Update meta theme-color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');
        }
    }, [
        resolvedTheme,
        mounted
    ]);
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
        // Save to localStorage
        const storage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLocalStorage"])();
        storage.saveSettings({
            theme: newTheme
        });
    };
    // Prevent hydration mismatch by not rendering until mounted
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-white",
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ThemeProvider.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: {
            theme,
            setTheme,
            resolvedTheme
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeProvider.tsx",
        lineNumber: 103,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3d4616de._.js.map