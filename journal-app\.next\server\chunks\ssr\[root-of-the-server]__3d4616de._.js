module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/localStorage.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "LocalStorageManager": (()=>LocalStorageManager),
    "localStorageManager": (()=>localStorageManager)
});
// Local storage keys
const STORAGE_KEYS = {
    ENTRIES: 'journal_entries',
    TAGS: 'journal_tags',
    PREFERENCES: 'journal_preferences',
    DRAFT: 'journal_draft',
    PENDING_SYNC: 'journal_pending_sync',
    LAST_SYNC: 'journal_last_sync'
};
class LocalStorageManager {
    // Check if localStorage is available
    isLocalStorageAvailable() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch  {
            return false;
        }
    }
    // Generic storage methods
    setItem(key, value) {
        if (!this.isLocalStorageAvailable()) return;
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
        }
    }
    getItem(key) {
        if (!this.isLocalStorageAvailable()) return null;
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('Failed to read from localStorage:', error);
            return null;
        }
    }
    removeItem(key) {
        if (!this.isLocalStorageAvailable()) return;
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
        }
    }
    // Journal entries management
    saveEntries(entries) {
        this.setItem(STORAGE_KEYS.ENTRIES, entries);
    }
    getEntries() {
        const entries = this.getItem(STORAGE_KEYS.ENTRIES);
        // Ensure we always return an array
        if (Array.isArray(entries)) {
            return entries;
        }
        // If entries is not an array, return empty array and clear the corrupted data
        if (entries !== null) {
            console.warn('Corrupted entries data found, clearing localStorage');
            this.removeItem(STORAGE_KEYS.ENTRIES);
        }
        return [];
    }
    saveEntry(entry) {
        const entries = this.getEntries();
        // Ensure entries is always an array
        const entriesArray = Array.isArray(entries) ? entries : [];
        const existingIndex = entriesArray.findIndex((e)=>e.id === entry.id);
        if (existingIndex >= 0) {
            entriesArray[existingIndex] = entry;
        } else {
            entriesArray.unshift(entry); // Add new entries at the beginning
        }
        this.saveEntries(entriesArray);
        this.markForSync(entry.id);
    }
    deleteEntry(entryId) {
        const entries = this.getEntries();
        // Ensure entries is always an array
        const entriesArray = Array.isArray(entries) ? entries : [];
        const filteredEntries = entriesArray.filter((e)=>e.id !== entryId);
        this.saveEntries(filteredEntries);
        this.markForSync(entryId, 'delete');
    }
    getEntry(entryId) {
        const entries = this.getEntries();
        // Ensure entries is always an array
        const entriesArray = Array.isArray(entries) ? entries : [];
        return entriesArray.find((e)=>e.id === entryId) || null;
    }
    // Tags management
    saveTags(tags) {
        this.setItem(STORAGE_KEYS.TAGS, tags);
    }
    getTags() {
        const tags = this.getItem(STORAGE_KEYS.TAGS);
        // Ensure we always return an array
        if (Array.isArray(tags)) {
            return tags;
        }
        // If tags is not an array, return empty array and clear the corrupted data
        if (tags !== null) {
            console.warn('Corrupted tags data found, clearing localStorage');
            this.removeItem(STORAGE_KEYS.TAGS);
        }
        return [];
    }
    saveTag(tag) {
        const tags = this.getTags();
        const existingIndex = tags.findIndex((t)=>t.id === tag.id);
        if (existingIndex >= 0) {
            tags[existingIndex] = tag;
        } else {
            tags.push(tag);
        }
        this.saveTags(tags);
    }
    // User preferences management
    savePreferences(preferences) {
        this.setItem(STORAGE_KEYS.PREFERENCES, preferences);
    }
    getPreferences() {
        return this.getItem(STORAGE_KEYS.PREFERENCES);
    }
    // Draft management for auto-save
    saveDraft(entryId, content) {
        const drafts = this.getDrafts();
        drafts[entryId] = {
            content,
            timestamp: Date.now()
        };
        this.setItem(STORAGE_KEYS.DRAFT, drafts);
    }
    getDraft(entryId) {
        const drafts = this.getDrafts();
        return drafts[entryId] || null;
    }
    clearDraft(entryId) {
        const drafts = this.getDrafts();
        delete drafts[entryId];
        this.setItem(STORAGE_KEYS.DRAFT, drafts);
    }
    getDrafts() {
        return this.getItem(STORAGE_KEYS.DRAFT) || {};
    }
    // Sync management
    markForSync(entryId, action = 'update') {
        const pendingSync = this.getPendingSync();
        pendingSync[entryId] = {
            action,
            timestamp: Date.now()
        };
        this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
    }
    getPendingSync() {
        return this.getItem(STORAGE_KEYS.PENDING_SYNC) || {};
    }
    clearPendingSync(entryId) {
        const pendingSync = this.getPendingSync();
        delete pendingSync[entryId];
        this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
    }
    setLastSyncTime(timestamp) {
        this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
    }
    getLastSyncTime() {
        return this.getItem(STORAGE_KEYS.LAST_SYNC);
    }
    // Search functionality
    searchEntries(query, tags, filters) {
        const entries = this.getEntries();
        // Ensure entries is always an array
        const entriesArray = Array.isArray(entries) ? entries : [];
        const lowercaseQuery = query.toLowerCase();
        return entriesArray.filter((entry)=>{
            // Text search
            const titleMatch = entry.title.toLowerCase().includes(lowercaseQuery);
            const contentMatch = entry.content.toLowerCase().includes(lowercaseQuery);
            // Timestamp search
            let timestampMatch = false;
            if (filters?.searchInTimestamps && entry.editHistory) {
                timestampMatch = entry.editHistory.some((edit)=>{
                    const editDate = new Date(edit.timestamp);
                    const dateStr = editDate.toLocaleDateString().toLowerCase();
                    const timeStr = editDate.toLocaleTimeString().toLowerCase();
                    const fullStr = `${dateStr} ${timeStr}`;
                    return fullStr.includes(lowercaseQuery) || dateStr.includes(lowercaseQuery) || timeStr.includes(lowercaseQuery);
                });
            }
            const textMatch = titleMatch || contentMatch || timestampMatch;
            // Tag filter
            const tagMatch = !tags || tags.length === 0 || tags.every((tag)=>entry.tags.includes(tag));
            // Date range filter
            let dateMatch = true;
            if (filters?.dateFrom || filters?.dateTo) {
                const entryDate = new Date(entry.lastEditedAt || entry.updatedAt);
                if (filters.dateFrom) {
                    dateMatch = dateMatch && entryDate >= filters.dateFrom;
                }
                if (filters.dateTo) {
                    const endDate = new Date(filters.dateTo);
                    endDate.setHours(23, 59, 59, 999); // Include the entire end date
                    dateMatch = dateMatch && entryDate <= endDate;
                }
            }
            // Time range filter
            let timeMatch = true;
            if (filters?.timeFrom || filters?.timeTo) {
                const entryDate = new Date(entry.lastEditedAt || entry.updatedAt);
                const entryTime = entryDate.getHours() * 60 + entryDate.getMinutes();
                if (filters.timeFrom) {
                    const [hours, minutes] = filters.timeFrom.split(':').map(Number);
                    const fromTime = hours * 60 + minutes;
                    timeMatch = timeMatch && entryTime >= fromTime;
                }
                if (filters.timeTo) {
                    const [hours, minutes] = filters.timeTo.split(':').map(Number);
                    const toTime = hours * 60 + minutes;
                    timeMatch = timeMatch && entryTime <= toTime;
                }
            }
            return textMatch && tagMatch && dateMatch && timeMatch;
        }).sort((a, b)=>{
            const aDate = new Date(a.lastEditedAt || a.updatedAt);
            const bDate = new Date(b.lastEditedAt || b.updatedAt);
            return bDate.getTime() - aDate.getTime();
        });
    }
    // Utility methods
    getStorageUsage() {
        if (!this.isLocalStorageAvailable()) {
            return {
                used: 0,
                available: 0
            };
        }
        let used = 0;
        for(const key in localStorage){
            if (localStorage.hasOwnProperty(key)) {
                used += localStorage[key].length;
            }
        }
        // Estimate available space (most browsers have ~5-10MB limit)
        const estimated = 5 * 1024 * 1024; // 5MB
        return {
            used,
            available: Math.max(0, estimated - used)
        };
    }
    clearAllData() {
        Object.values(STORAGE_KEYS).forEach((key)=>{
            this.removeItem(key);
        });
    }
    // Export/Import functionality
    exportData() {
        return {
            entries: this.getEntries(),
            tags: this.getTags(),
            preferences: this.getPreferences(),
            exportDate: new Date().toISOString()
        };
    }
    importData(data) {
        if (data.entries) {
            this.saveEntries(data.entries);
        }
        if (data.tags) {
            this.saveTags(data.tags);
        }
        if (data.preferences) {
            this.savePreferences(data.preferences);
        }
    }
    // Auto-cleanup old drafts (older than 7 days)
    cleanupOldDrafts() {
        const drafts = this.getDrafts();
        const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
        let hasChanges = false;
        Object.keys(drafts).forEach((entryId)=>{
            if (drafts[entryId].timestamp < sevenDaysAgo) {
                delete drafts[entryId];
                hasChanges = true;
            }
        });
        if (hasChanges) {
            this.setItem(STORAGE_KEYS.DRAFT, drafts);
        }
    }
}
const localStorageManager = new LocalStorageManager();
// Auto-cleanup on initialization
localStorageManager.cleanupOldDrafts();
}}),
"[project]/src/components/ThemeProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "ThemeToggle": (()=>ThemeToggle),
    "usePrefersReducedMotion": (()=>usePrefersReducedMotion),
    "useSystemTheme": (()=>useSystemTheme),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/localStorage.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ThemeProvider({ children, defaultTheme = 'system' }) {
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultTheme);
    const [actualTheme, setActualTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    const [preferences, setPreferences] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load preferences from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedPreferences = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["localStorageManager"].getPreferences();
        if (savedPreferences) {
            setPreferences(savedPreferences);
            setThemeState(savedPreferences.theme);
        } else {
            // Create default preferences
            const defaultPreferences = {
                id: 'default',
                theme: 'system',
                fontSize: 16,
                fontFamily: 'Inter',
                spellCheckEnabled: true,
                autoSaveInterval: 2000
            };
            setPreferences(defaultPreferences);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(defaultPreferences);
        }
        setMounted(true);
    }, []);
    // Update actual theme based on theme setting and system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!mounted) return;
        const updateActualTheme = ()=>{
            if (theme === 'system') {
                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                setActualTheme(systemTheme);
            } else {
                setActualTheme(theme);
            }
        };
        updateActualTheme();
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = ()=>{
            if (theme === 'system') {
                updateActualTheme();
            }
        };
        mediaQuery.addEventListener('change', handleChange);
        return ()=>mediaQuery.removeEventListener('change', handleChange);
    }, [
        theme,
        mounted
    ]);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!mounted) return;
        const root = window.document.documentElement;
        // Remove previous theme classes
        root.classList.remove('light', 'dark');
        // Add current theme class
        root.classList.add(actualTheme);
        // Update meta theme-color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', actualTheme === 'dark' ? '#1f2937' : '#ffffff');
        }
    }, [
        actualTheme,
        mounted
    ]);
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
        // Update preferences
        if (preferences) {
            const updatedPreferences = {
                ...preferences,
                theme: newTheme
            };
            setPreferences(updatedPreferences);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(updatedPreferences);
        }
    };
    const updatePreferences = (updates)=>{
        if (!preferences) return;
        const updatedPreferences = {
            ...preferences,
            ...updates
        };
        setPreferences(updatedPreferences);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(updatedPreferences);
        // If theme was updated, update theme state
        if (updates.theme) {
            setThemeState(updates.theme);
        }
    };
    const value = {
        theme,
        actualTheme,
        setTheme,
        preferences,
        updatePreferences
    };
    // Don't render until mounted to avoid hydration mismatch
    if (!mounted) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeProvider.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
}
function useTheme() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
function ThemeToggle({ className = '' }) {
    const { theme, setTheme } = useTheme();
    const themes = [
        {
            value: 'light',
            label: 'Light',
            icon: '☀️'
        },
        {
            value: 'dark',
            label: 'Dark',
            icon: '🌙'
        },
        {
            value: 'system',
            label: 'System',
            icon: '💻'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center space-x-1 ${className}`,
        children: themes.map(({ value, label, icon })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setTheme(value),
                className: `
            flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${theme === value ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
          `,
                title: `Switch to ${label.toLowerCase()} theme`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: icon
                    }, void 0, false, {
                        fileName: "[project]/src/components/ThemeProvider.tsx",
                        lineNumber: 179,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "hidden sm:inline",
                        children: label
                    }, void 0, false, {
                        fileName: "[project]/src/components/ThemeProvider.tsx",
                        lineNumber: 180,
                        columnNumber: 11
                    }, this)
                ]
            }, value, true, {
                fileName: "[project]/src/components/ThemeProvider.tsx",
                lineNumber: 167,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeProvider.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, this);
}
function useSystemTheme() {
    const [systemTheme, setSystemTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const updateSystemTheme = ()=>{
            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
        };
        updateSystemTheme();
        mediaQuery.addEventListener('change', updateSystemTheme);
        return ()=>mediaQuery.removeEventListener('change', updateSystemTheme);
    }, []);
    return systemTheme;
}
function usePrefersReducedMotion() {
    const [prefersReducedMotion, setPrefersReducedMotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        const updatePreference = ()=>{
            setPrefersReducedMotion(mediaQuery.matches);
        };
        updatePreference();
        mediaQuery.addEventListener('change', updatePreference);
        return ()=>mediaQuery.removeEventListener('change', updatePreference);
    }, []);
    return prefersReducedMotion;
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3d4616de._.js.map