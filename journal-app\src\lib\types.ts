// TypeScript interfaces for the journal app

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  attachments?: string[];
  wordCount: number;
}

export interface Tag {
  id: string;
  name: string;
  color?: string;
  createdAt: Date;
  usageCount: number;
}

export interface UserPreferences {
  id: string;
  theme: 'light' | 'dark' | 'system';
  fontSize: number;
  fontFamily: string;
  spellCheckEnabled: boolean;
  autoSaveInterval: number; // in milliseconds
  lastSyncAt?: Date;
}

export interface SearchResult {
  entry: JournalEntry;
  matchType: 'title' | 'content' | 'tag';
  matchText: string;
  score: number;
}

export interface FileUpload {
  id: string;
  filename: string;
  content: string;
  uploadedAt: Date;
  processed: boolean;
}

// Database row interfaces (for SQLite)
export interface JournalEntryRow {
  id: string;
  title: string;
  content: string;
  tags: string; // JSON string
  created_at: string;
  updated_at: string;
  attachments: string | null; // JSON string
  word_count: number;
}

export interface TagRow {
  id: string;
  name: string;
  color: string | null;
  created_at: string;
  usage_count: number;
}

export interface UserPreferencesRow {
  id: string;
  theme: string;
  font_size: number;
  font_family: string;
  spell_check_enabled: number; // SQLite boolean as integer
  auto_save_interval: number;
  last_sync_at: string | null;
}

// API response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'wordCount';
  sortOrder?: 'asc' | 'desc';
}

// Component prop types
export interface EditorProps {
  entry: JournalEntry;
  onSave: (entry: JournalEntry) => void;
  onAutoSave: (content: string) => void;
  preferences: UserPreferences;
}

export interface TagManagerProps {
  tags: Tag[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  onCreateTag: (name: string, color?: string) => void;
}
