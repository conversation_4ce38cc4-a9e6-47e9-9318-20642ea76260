{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/lib/localStorage.ts"], "sourcesContent": ["import { JournalEntry } from './database';\n\nconst STORAGE_KEYS = {\n  ENTRIES: 'journal_entries',\n  DRAFTS: 'journal_drafts',\n  SETTINGS: 'journal_settings',\n  TAGS: 'journal_tags',\n} as const;\n\nexport interface UserSettings {\n  theme: 'light' | 'dark' | 'system';\n  fontSize: number;\n  fontFamily: string;\n  autoSave: boolean;\n  autoSaveInterval: number; // in milliseconds\n  spellCheck: boolean;\n}\n\nexport interface Draft {\n  id: string;\n  title: string;\n  content: string;\n  tags: string[];\n  lastModified: string;\n}\n\nclass LocalStorageManager {\n  // Settings management\n  getSettings(): UserSettings {\n    console.log('getSettings called');\n    if (typeof window === 'undefined') {\n      console.log('Window undefined, returning defaults');\n      return this.getDefaultSettings();\n    }\n\n    try {\n      console.log('Getting settings from localStorage...');\n      const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);\n      console.log('Stored settings:', stored);\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        const result = { ...this.getDefaultSettings(), ...parsed };\n        console.log('Merged settings:', result);\n        return result;\n      }\n    } catch (error) {\n      console.error('Error loading settings from localStorage:', error);\n    }\n    const defaults = this.getDefaultSettings();\n    console.log('Returning default settings:', defaults);\n    return defaults;\n  }\n\n  saveSettings(settings: Partial<UserSettings>): void {\n    if (typeof window === 'undefined') return;\n\n    try {\n      const current = this.getSettings();\n      const updated = { ...current, ...settings };\n      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updated));\n    } catch (error) {\n      console.error('Error saving settings to localStorage:', error);\n    }\n  }\n\n  private getDefaultSettings(): UserSettings {\n    return {\n      theme: 'system',\n      fontSize: 16,\n      fontFamily: 'Inter, system-ui, sans-serif',\n      autoSave: true,\n      autoSaveInterval: 2000, // 2 seconds\n      spellCheck: true,\n    };\n  }\n\n  // Draft management (for unsaved entries)\n  saveDraft(draft: Draft): void {\n    if (typeof window === 'undefined') return;\n\n    try {\n      const drafts = this.getDrafts();\n      const existingIndex = drafts.findIndex(d => d.id === draft.id);\n\n      if (existingIndex >= 0) {\n        drafts[existingIndex] = draft;\n      } else {\n        drafts.push(draft);\n      }\n\n      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n    } catch (error) {\n      console.error('Error saving draft to localStorage:', error);\n    }\n  }\n\n  getDrafts(): Draft[] {\n    if (typeof window === 'undefined') return [];\n\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.DRAFTS);\n      return stored ? JSON.parse(stored) : [];\n    } catch (error) {\n      console.error('Error loading drafts from localStorage:', error);\n      return [];\n    }\n  }\n\n  getDraft(id: string): Draft | null {\n    const drafts = this.getDrafts();\n    return drafts.find(d => d.id === id) || null;\n  }\n\n  deleteDraft(id: string): void {\n    try {\n      const drafts = this.getDrafts().filter(d => d.id !== id);\n      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n    } catch (error) {\n      console.error('Error deleting draft from localStorage:', error);\n    }\n  }\n\n  // Entry caching (for offline access)\n  cacheEntries(entries: JournalEntry[]): void {\n    try {\n      const cached = {\n        entries,\n        lastUpdated: new Date().toISOString(),\n      };\n      localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n    } catch (error) {\n      console.error('Error caching entries to localStorage:', error);\n    }\n  }\n\n  getCachedEntries(): { entries: JournalEntry[]; lastUpdated: string } | null {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);\n      return stored ? JSON.parse(stored) : null;\n    } catch (error) {\n      console.error('Error loading cached entries from localStorage:', error);\n      return null;\n    }\n  }\n\n  cacheEntry(entry: JournalEntry): void {\n    try {\n      const cached = this.getCachedEntries();\n      if (cached) {\n        const existingIndex = cached.entries.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          cached.entries[existingIndex] = entry;\n        } else {\n          cached.entries.unshift(entry);\n        }\n        cached.lastUpdated = new Date().toISOString();\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n      } else {\n        this.cacheEntries([entry]);\n      }\n    } catch (error) {\n      console.error('Error caching entry to localStorage:', error);\n    }\n  }\n\n  removeCachedEntry(id: string): void {\n    try {\n      const cached = this.getCachedEntries();\n      if (cached) {\n        cached.entries = cached.entries.filter(e => e.id !== id);\n        cached.lastUpdated = new Date().toISOString();\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n      }\n    } catch (error) {\n      console.error('Error removing cached entry from localStorage:', error);\n    }\n  }\n\n  // Tag caching\n  cacheTags(tags: string[]): void {\n    try {\n      const cached = {\n        tags,\n        lastUpdated: new Date().toISOString(),\n      };\n      localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));\n    } catch (error) {\n      console.error('Error caching tags to localStorage:', error);\n    }\n  }\n\n  getCachedTags(): string[] {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.TAGS);\n      const cached = stored ? JSON.parse(stored) : null;\n      return cached ? cached.tags : [];\n    } catch (error) {\n      console.error('Error loading cached tags from localStorage:', error);\n      return [];\n    }\n  }\n\n  // Utility methods\n  clearAllData(): void {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n    } catch (error) {\n      console.error('Error clearing localStorage:', error);\n    }\n  }\n\n  getStorageUsage(): { used: number; available: number } {\n    try {\n      let used = 0;\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          used += localStorage.getItem(key)?.length || 0;\n        }\n      }\n\n      // Estimate available space (most browsers have ~5-10MB limit)\n      const available = 5 * 1024 * 1024 - used; // Assume 5MB limit\n      \n      return { used, available };\n    } catch (error) {\n      console.error('Error calculating storage usage:', error);\n      return { used: 0, available: 0 };\n    }\n  }\n\n  // Auto-save functionality\n  createAutoSaveTimer(callback: () => void, interval?: number): NodeJS.Timeout {\n    const settings = this.getSettings();\n    const saveInterval = interval || settings.autoSaveInterval;\n    \n    return setInterval(callback, saveInterval);\n  }\n\n  // Export/Import functionality\n  exportData(): string {\n    try {\n      const data = {\n        entries: this.getCachedEntries(),\n        drafts: this.getDrafts(),\n        settings: this.getSettings(),\n        tags: this.getCachedTags(),\n        exportDate: new Date().toISOString(),\n      };\n      return JSON.stringify(data, null, 2);\n    } catch (error) {\n      console.error('Error exporting data:', error);\n      throw new Error('Failed to export data');\n    }\n  }\n\n  importData(jsonData: string): void {\n    try {\n      const data = JSON.parse(jsonData);\n      \n      if (data.entries) {\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));\n      }\n      \n      if (data.drafts) {\n        localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));\n      }\n      \n      if (data.settings) {\n        localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));\n      }\n      \n      if (data.tags) {\n        localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));\n      }\n    } catch (error) {\n      console.error('Error importing data:', error);\n      throw new Error('Failed to import data');\n    }\n  }\n}\n\n// Singleton instance\nlet storageInstance: LocalStorageManager | null = null;\n\nexport function getLocalStorage(): LocalStorageManager {\n  if (!storageInstance) {\n    storageInstance = new LocalStorageManager();\n  }\n  return storageInstance;\n}\n\nexport default LocalStorageManager;\n"], "names": [], "mappings": ";;;;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,QAAQ;IACR,UAAU;IACV,MAAM;AACR;AAmBA,MAAM;IACJ,sBAAsB;IACtB,cAA4B;QAC1B,QAAQ,GAAG,CAAC;QACZ,wCAAmC;YACjC,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,kBAAkB;QAChC;;QAeA,MAAM;IAGR;IAEA,aAAa,QAA+B,EAAQ;QAClD,wCAAmC;;IASrC;IAEQ,qBAAmC;QACzC,OAAO;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,kBAAkB;YAClB,YAAY;QACd;IACF;IAEA,yCAAyC;IACzC,UAAU,KAAY,EAAQ;QAC5B,wCAAmC;;IAgBrC;IAEA,YAAqB;QACnB,wCAAmC,OAAO,EAAE;;IAS9C;IAEA,SAAS,EAAU,EAAgB;QACjC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC1C;IAEA,YAAY,EAAU,EAAQ;QAC5B,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACrD,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA,qCAAqC;IACrC,aAAa,OAAuB,EAAQ;QAC1C,IAAI;YACF,MAAM,SAAS;gBACb;gBACA,aAAa,IAAI,OAAO,WAAW;YACrC;YACA,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,mBAA4E;QAC1E,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,OAAO;YACxD,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO;QACT;IACF;IAEA,WAAW,KAAmB,EAAQ;QACpC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB;YACpC,IAAI,QAAQ;gBACV,MAAM,gBAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;gBACrE,IAAI,iBAAiB,GAAG;oBACtB,OAAO,OAAO,CAAC,cAAc,GAAG;gBAClC,OAAO;oBACL,OAAO,OAAO,CAAC,OAAO,CAAC;gBACzB;gBACA,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;gBAC3C,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;YAC5D,OAAO;gBACL,IAAI,CAAC,YAAY,CAAC;oBAAC;iBAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,kBAAkB,EAAU,EAAQ;QAClC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB;YACpC,IAAI,QAAQ;gBACV,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACrD,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;gBAC3C,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;QAClE;IACF;IAEA,cAAc;IACd,UAAU,IAAc,EAAQ;QAC9B,IAAI;YACF,MAAM,SAAS;gBACb;gBACA,aAAa,IAAI,OAAO,WAAW;YACrC;YACA,aAAa,OAAO,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,gBAA0B;QACxB,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,IAAI;YACrD,MAAM,SAAS,SAAS,KAAK,KAAK,CAAC,UAAU;YAC7C,OAAO,SAAS,OAAO,IAAI,GAAG,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO,EAAE;QACX;IACF;IAEA,kBAAkB;IAClB,eAAqB;QACnB,IAAI;YACF,OAAO,MAAM,CAAC,cAAc,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,kBAAuD;QACrD,IAAI;YACF,IAAI,OAAO;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,KAAK;oBACP,QAAQ,aAAa,OAAO,CAAC,MAAM,UAAU;gBAC/C;YACF;YAEA,8DAA8D;YAC9D,MAAM,YAAY,IAAI,OAAO,OAAO,MAAM,mBAAmB;YAE7D,OAAO;gBAAE;gBAAM;YAAU;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,MAAM;gBAAG,WAAW;YAAE;QACjC;IACF;IAEA,0BAA0B;IAC1B,oBAAoB,QAAoB,EAAE,QAAiB,EAAkB;QAC3E,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,eAAe,YAAY,SAAS,gBAAgB;QAE1D,OAAO,YAAY,UAAU;IAC/B;IAEA,8BAA8B;IAC9B,aAAqB;QACnB,IAAI;YACF,MAAM,OAAO;gBACX,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,QAAQ,IAAI,CAAC,SAAS;gBACtB,UAAU,IAAI,CAAC,WAAW;gBAC1B,MAAM,IAAI,CAAC,aAAa;gBACxB,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW,QAAgB,EAAQ;QACjC,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YAExB,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC,KAAK,OAAO;YACxE;YAEA,IAAI,KAAK,MAAM,EAAE;gBACf,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC,KAAK,MAAM;YACtE;YAEA,IAAI,KAAK,QAAQ,EAAE;gBACjB,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC,KAAK,QAAQ;YAC1E;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,aAAa,OAAO,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA,qBAAqB;AACrB,IAAI,kBAA8C;AAE3C,SAAS;IACd,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,IAAI;IACxB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { getLocalStorage } from '@/lib/localStorage';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  resolvedTheme: 'light' | 'dark';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>('system');\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const storage = getLocalStorage();\n    const settings = storage.getSettings();\n    setThemeState(settings.theme);\n    setMounted(true);\n  }, []);\n\n  // Update resolved theme when theme changes or system preference changes\n  useEffect(() => {\n    const updateResolvedTheme = () => {\n      if (theme === 'system') {\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        setResolvedTheme(systemTheme);\n      } else {\n        setResolvedTheme(theme);\n      }\n    };\n\n    updateResolvedTheme();\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateResolvedTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n    \n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(resolvedTheme);\n    \n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');\n    }\n  }, [resolvedTheme, mounted]);\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    \n    // Save to localStorage\n    const storage = getLocalStorage();\n    storage.saveSettings({ theme: newTheme });\n  };\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-white\">\n        {children}\n      </div>\n    );\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;QAC9B,MAAM,WAAW,QAAQ,WAAW;QACpC,cAAc,SAAS,KAAK;QAC5B,WAAW;IACb,GAAG,EAAE;IAEL,wEAAwE;IACxE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;gBACzF,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;QAEA;QAEA,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,eAAe;YACnB,IAAI,UAAU,UAAU;gBACtB;YACF;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAM;IAEV,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,SAAS,eAAe;QAErC,gCAAgC;QAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;QAE/B,0BAA0B;QAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;QAEnB,8CAA8C;QAC9C,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,IAAI,gBAAgB;YAClB,eAAe,YAAY,CAAC,WAAW,kBAAkB,SAAS,YAAY;QAChF;IACF,GAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,WAAW,CAAC;QAChB,cAAc;QAEd,uBAAuB;QACvB,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;QAC9B,QAAQ,YAAY,CAAC;YAAE,OAAO;QAAS;IACzC;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAc;kBAC5D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}