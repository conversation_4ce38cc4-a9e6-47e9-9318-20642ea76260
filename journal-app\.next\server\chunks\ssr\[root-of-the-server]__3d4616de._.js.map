{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/lib/localStorage.ts"], "sourcesContent": ["import { JournalEntry, Tag, UserPreferences } from './types';\n\n// Local storage keys\nconst STORAGE_KEYS = {\n  ENTRIES: 'journal_entries',\n  TAGS: 'journal_tags',\n  PREFERENCES: 'journal_preferences',\n  DRAFT: 'journal_draft',\n  PENDING_SYNC: 'journal_pending_sync',\n  LAST_SYNC: 'journal_last_sync'\n} as const;\n\n// Local storage utilities for offline functionality\nexport class LocalStorageManager {\n  \n  // Check if localStorage is available\n  private isLocalStorageAvailable(): boolean {\n    try {\n      const test = '__localStorage_test__';\n      localStorage.setItem(test, test);\n      localStorage.removeItem(test);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  // Generic storage methods\n  private setItem<T>(key: string, value: T): void {\n    if (!this.isLocalStorageAvailable()) return;\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error);\n    }\n  }\n\n  private getItem<T>(key: string): T | null {\n    if (!this.isLocalStorageAvailable()) return null;\n    \n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Failed to read from localStorage:', error);\n      return null;\n    }\n  }\n\n  private removeItem(key: string): void {\n    if (!this.isLocalStorageAvailable()) return;\n    \n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Failed to remove from localStorage:', error);\n    }\n  }\n\n  // Journal entries management\n  saveEntries(entries: JournalEntry[]): void {\n    this.setItem(STORAGE_KEYS.ENTRIES, entries);\n  }\n\n  getEntries(): JournalEntry[] {\n    const entries = this.getItem<JournalEntry[]>(STORAGE_KEYS.ENTRIES);\n\n    // Ensure we always return an array\n    if (Array.isArray(entries)) {\n      return entries;\n    }\n\n    // If entries is not an array, return empty array and clear the corrupted data\n    if (entries !== null) {\n      console.warn('Corrupted entries data found, clearing localStorage');\n      this.removeItem(STORAGE_KEYS.ENTRIES);\n    }\n\n    return [];\n  }\n\n  saveEntry(entry: JournalEntry): void {\n    const entries = this.getEntries();\n\n    // Ensure entries is always an array\n    const entriesArray = Array.isArray(entries) ? entries : [];\n    const existingIndex = entriesArray.findIndex(e => e.id === entry.id);\n\n    if (existingIndex >= 0) {\n      entriesArray[existingIndex] = entry;\n    } else {\n      entriesArray.unshift(entry); // Add new entries at the beginning\n    }\n\n    this.saveEntries(entriesArray);\n    this.markForSync(entry.id);\n  }\n\n  deleteEntry(entryId: string): void {\n    const entries = this.getEntries();\n\n    // Ensure entries is always an array\n    const entriesArray = Array.isArray(entries) ? entries : [];\n    const filteredEntries = entriesArray.filter(e => e.id !== entryId);\n    this.saveEntries(filteredEntries);\n    this.markForSync(entryId, 'delete');\n  }\n\n  getEntry(entryId: string): JournalEntry | null {\n    const entries = this.getEntries();\n\n    // Ensure entries is always an array\n    const entriesArray = Array.isArray(entries) ? entries : [];\n    return entriesArray.find(e => e.id === entryId) || null;\n  }\n\n  // Tags management\n  saveTags(tags: Tag[]): void {\n    this.setItem(STORAGE_KEYS.TAGS, tags);\n  }\n\n  getTags(): Tag[] {\n    const tags = this.getItem<Tag[]>(STORAGE_KEYS.TAGS);\n\n    // Ensure we always return an array\n    if (Array.isArray(tags)) {\n      return tags;\n    }\n\n    // If tags is not an array, return empty array and clear the corrupted data\n    if (tags !== null) {\n      console.warn('Corrupted tags data found, clearing localStorage');\n      this.removeItem(STORAGE_KEYS.TAGS);\n    }\n\n    return [];\n  }\n\n  saveTag(tag: Tag): void {\n    const tags = this.getTags();\n    const existingIndex = tags.findIndex(t => t.id === tag.id);\n    \n    if (existingIndex >= 0) {\n      tags[existingIndex] = tag;\n    } else {\n      tags.push(tag);\n    }\n    \n    this.saveTags(tags);\n  }\n\n  // User preferences management\n  savePreferences(preferences: UserPreferences): void {\n    this.setItem(STORAGE_KEYS.PREFERENCES, preferences);\n  }\n\n  getPreferences(): UserPreferences | null {\n    return this.getItem<UserPreferences>(STORAGE_KEYS.PREFERENCES);\n  }\n\n  // Draft management for auto-save\n  saveDraft(entryId: string, content: string): void {\n    const drafts = this.getDrafts();\n    drafts[entryId] = {\n      content,\n      timestamp: Date.now()\n    };\n    this.setItem(STORAGE_KEYS.DRAFT, drafts);\n  }\n\n  getDraft(entryId: string): { content: string; timestamp: number } | null {\n    const drafts = this.getDrafts();\n    return drafts[entryId] || null;\n  }\n\n  clearDraft(entryId: string): void {\n    const drafts = this.getDrafts();\n    delete drafts[entryId];\n    this.setItem(STORAGE_KEYS.DRAFT, drafts);\n  }\n\n  private getDrafts(): Record<string, { content: string; timestamp: number }> {\n    return this.getItem<Record<string, { content: string; timestamp: number }>>(STORAGE_KEYS.DRAFT) || {};\n  }\n\n  // Sync management\n  markForSync(entryId: string, action: 'create' | 'update' | 'delete' = 'update'): void {\n    const pendingSync = this.getPendingSync();\n    pendingSync[entryId] = {\n      action,\n      timestamp: Date.now()\n    };\n    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);\n  }\n\n  getPendingSync(): Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }> {\n    return this.getItem<Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }>>(STORAGE_KEYS.PENDING_SYNC) || {};\n  }\n\n  clearPendingSync(entryId: string): void {\n    const pendingSync = this.getPendingSync();\n    delete pendingSync[entryId];\n    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);\n  }\n\n  setLastSyncTime(timestamp: number): void {\n    this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);\n  }\n\n  getLastSyncTime(): number | null {\n    return this.getItem<number>(STORAGE_KEYS.LAST_SYNC);\n  }\n\n  // Search functionality\n  searchEntries(query: string, tags?: string[]): JournalEntry[] {\n    const entries = this.getEntries();\n\n    // Ensure entries is always an array\n    const entriesArray = Array.isArray(entries) ? entries : [];\n    const lowercaseQuery = query.toLowerCase();\n\n    return entriesArray.filter(entry => {\n      // Text search\n      const titleMatch = entry.title.toLowerCase().includes(lowercaseQuery);\n      const contentMatch = entry.content.toLowerCase().includes(lowercaseQuery);\n      const textMatch = titleMatch || contentMatch;\n\n      // Tag filter\n      const tagMatch = !tags || tags.length === 0 || \n        tags.every(tag => entry.tags.includes(tag));\n\n      return textMatch && tagMatch;\n    }).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());\n  }\n\n  // Utility methods\n  getStorageUsage(): { used: number; available: number } {\n    if (!this.isLocalStorageAvailable()) {\n      return { used: 0, available: 0 };\n    }\n\n    let used = 0;\n    for (const key in localStorage) {\n      if (localStorage.hasOwnProperty(key)) {\n        used += localStorage[key].length;\n      }\n    }\n\n    // Estimate available space (most browsers have ~5-10MB limit)\n    const estimated = 5 * 1024 * 1024; // 5MB\n    return {\n      used,\n      available: Math.max(0, estimated - used)\n    };\n  }\n\n  clearAllData(): void {\n    Object.values(STORAGE_KEYS).forEach(key => {\n      this.removeItem(key);\n    });\n  }\n\n  // Export/Import functionality\n  exportData(): {\n    entries: JournalEntry[];\n    tags: Tag[];\n    preferences: UserPreferences | null;\n    exportDate: string;\n  } {\n    return {\n      entries: this.getEntries(),\n      tags: this.getTags(),\n      preferences: this.getPreferences(),\n      exportDate: new Date().toISOString()\n    };\n  }\n\n  importData(data: {\n    entries?: JournalEntry[];\n    tags?: Tag[];\n    preferences?: UserPreferences;\n  }): void {\n    if (data.entries) {\n      this.saveEntries(data.entries);\n    }\n    if (data.tags) {\n      this.saveTags(data.tags);\n    }\n    if (data.preferences) {\n      this.savePreferences(data.preferences);\n    }\n  }\n\n  // Auto-cleanup old drafts (older than 7 days)\n  cleanupOldDrafts(): void {\n    const drafts = this.getDrafts();\n    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);\n    \n    let hasChanges = false;\n    Object.keys(drafts).forEach(entryId => {\n      if (drafts[entryId].timestamp < sevenDaysAgo) {\n        delete drafts[entryId];\n        hasChanges = true;\n      }\n    });\n\n    if (hasChanges) {\n      this.setItem(STORAGE_KEYS.DRAFT, drafts);\n    }\n  }\n}\n\n// Create a singleton instance\nexport const localStorageManager = new LocalStorageManager();\n\n// Auto-cleanup on initialization\nlocalStorageManager.cleanupOldDrafts();\n"], "names": [], "mappings": ";;;;AAEA,qBAAqB;AACrB,MAAM,eAAe;IACnB,SAAS;IACT,MAAM;IACN,aAAa;IACb,OAAO;IACP,cAAc;IACd,WAAW;AACb;AAGO,MAAM;IAEX,qCAAqC;IAC7B,0BAAmC;QACzC,IAAI;YACF,MAAM,OAAO;YACb,aAAa,OAAO,CAAC,MAAM;YAC3B,aAAa,UAAU,CAAC;YACxB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,0BAA0B;IAClB,QAAW,GAAW,EAAE,KAAQ,EAAQ;QAC9C,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;QAErC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEQ,QAAW,GAAW,EAAY;QACxC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,OAAO;QAE5C,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEQ,WAAW,GAAW,EAAQ;QACpC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;QAErC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,6BAA6B;IAC7B,YAAY,OAAuB,EAAQ;QACzC,IAAI,CAAC,OAAO,CAAC,aAAa,OAAO,EAAE;IACrC;IAEA,aAA6B;QAC3B,MAAM,UAAU,IAAI,CAAC,OAAO,CAAiB,aAAa,OAAO;QAEjE,mCAAmC;QACnC,IAAI,MAAM,OAAO,CAAC,UAAU;YAC1B,OAAO;QACT;QAEA,8EAA8E;QAC9E,IAAI,YAAY,MAAM;YACpB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;QACtC;QAEA,OAAO,EAAE;IACX;IAEA,UAAU,KAAmB,EAAQ;QACnC,MAAM,UAAU,IAAI,CAAC,UAAU;QAE/B,oCAAoC;QACpC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAC1D,MAAM,gBAAgB,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAEnE,IAAI,iBAAiB,GAAG;YACtB,YAAY,CAAC,cAAc,GAAG;QAChC,OAAO;YACL,aAAa,OAAO,CAAC,QAAQ,mCAAmC;QAClE;QAEA,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;IAC3B;IAEA,YAAY,OAAe,EAAQ;QACjC,MAAM,UAAU,IAAI,CAAC,UAAU;QAE/B,oCAAoC;QACpC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAC1D,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC,SAAS;IAC5B;IAEA,SAAS,OAAe,EAAuB;QAC7C,MAAM,UAAU,IAAI,CAAC,UAAU;QAE/B,oCAAoC;QACpC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAC1D,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY;IACrD;IAEA,kBAAkB;IAClB,SAAS,IAAW,EAAQ;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,EAAE;IAClC;IAEA,UAAiB;QACf,MAAM,OAAO,IAAI,CAAC,OAAO,CAAQ,aAAa,IAAI;QAElD,mCAAmC;QACnC,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO;QACT;QAEA,2EAA2E;QAC3E,IAAI,SAAS,MAAM;YACjB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI;QACnC;QAEA,OAAO,EAAE;IACX;IAEA,QAAQ,GAAQ,EAAQ;QACtB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,MAAM,gBAAgB,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,EAAE;QAEzD,IAAI,iBAAiB,GAAG;YACtB,IAAI,CAAC,cAAc,GAAG;QACxB,OAAO;YACL,KAAK,IAAI,CAAC;QACZ;QAEA,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,8BAA8B;IAC9B,gBAAgB,WAA4B,EAAQ;QAClD,IAAI,CAAC,OAAO,CAAC,aAAa,WAAW,EAAE;IACzC;IAEA,iBAAyC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAkB,aAAa,WAAW;IAC/D;IAEA,iCAAiC;IACjC,UAAU,OAAe,EAAE,OAAe,EAAQ;QAChD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,CAAC,QAAQ,GAAG;YAChB;YACA,WAAW,KAAK,GAAG;QACrB;QACA,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;IACnC;IAEA,SAAS,OAAe,EAAiD;QACvE,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,MAAM,CAAC,QAAQ,IAAI;IAC5B;IAEA,WAAW,OAAe,EAAQ;QAChC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,MAAM,CAAC,QAAQ;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;IACnC;IAEQ,YAAoE;QAC1E,OAAO,IAAI,CAAC,OAAO,CAAyD,aAAa,KAAK,KAAK,CAAC;IACtG;IAEA,kBAAkB;IAClB,YAAY,OAAe,EAAE,SAAyC,QAAQ,EAAQ;QACpF,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,WAAW,CAAC,QAAQ,GAAG;YACrB;YACA,WAAW,KAAK,GAAG;QACrB;QACA,IAAI,CAAC,OAAO,CAAC,aAAa,YAAY,EAAE;IAC1C;IAEA,iBAAgG;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAgF,aAAa,YAAY,KAAK,CAAC;IACpI;IAEA,iBAAiB,OAAe,EAAQ;QACtC,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,WAAW,CAAC,QAAQ;QAC3B,IAAI,CAAC,OAAO,CAAC,aAAa,YAAY,EAAE;IAC1C;IAEA,gBAAgB,SAAiB,EAAQ;QACvC,IAAI,CAAC,OAAO,CAAC,aAAa,SAAS,EAAE;IACvC;IAEA,kBAAiC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAS,aAAa,SAAS;IACpD;IAEA,uBAAuB;IACvB,cAAc,KAAa,EAAE,IAAe,EAAkB;QAC5D,MAAM,UAAU,IAAI,CAAC,UAAU;QAE/B,oCAAoC;QACpC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAC1D,MAAM,iBAAiB,MAAM,WAAW;QAExC,OAAO,aAAa,MAAM,CAAC,CAAA;YACzB,cAAc;YACd,MAAM,aAAa,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YACtD,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;YAC1D,MAAM,YAAY,cAAc;YAEhC,aAAa;YACb,MAAM,WAAW,CAAC,QAAQ,KAAK,MAAM,KAAK,KACxC,KAAK,KAAK,CAAC,CAAA,MAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;YAExC,OAAO,aAAa;QACtB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAC/D;IAEA,kBAAkB;IAClB,kBAAuD;QACrD,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;YACnC,OAAO;gBAAE,MAAM;gBAAG,WAAW;YAAE;QACjC;QAEA,IAAI,OAAO;QACX,IAAK,MAAM,OAAO,aAAc;YAC9B,IAAI,aAAa,cAAc,CAAC,MAAM;gBACpC,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;YAClC;QACF;QAEA,8DAA8D;QAC9D,MAAM,YAAY,IAAI,OAAO,MAAM,MAAM;QACzC,OAAO;YACL;YACA,WAAW,KAAK,GAAG,CAAC,GAAG,YAAY;QACrC;IACF;IAEA,eAAqB;QACnB,OAAO,MAAM,CAAC,cAAc,OAAO,CAAC,CAAA;YAClC,IAAI,CAAC,UAAU,CAAC;QAClB;IACF;IAEA,8BAA8B;IAC9B,aAKE;QACA,OAAO;YACL,SAAS,IAAI,CAAC,UAAU;YACxB,MAAM,IAAI,CAAC,OAAO;YAClB,aAAa,IAAI,CAAC,cAAc;YAChC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,WAAW,IAIV,EAAQ;QACP,IAAI,KAAK,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,IAAI,EAAE;YACb,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;QACzB;QACA,IAAI,KAAK,WAAW,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC,KAAK,WAAW;QACvC;IACF;IAEA,8CAA8C;IAC9C,mBAAyB;QACvB,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,eAAe,KAAK,GAAG,KAAM,IAAI,KAAK,KAAK,KAAK;QAEtD,IAAI,aAAa;QACjB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,cAAc;gBAC5C,OAAO,MAAM,CAAC,QAAQ;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,YAAY;YACd,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;QACnC;IACF;AACF;AAGO,MAAM,sBAAsB,IAAI;AAEvC,iCAAiC;AACjC,oBAAoB,gBAAgB", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  actualTheme: 'light' | 'dark';\n  setTheme: (theme: Theme) => void;\n  preferences: UserPreferences | null;\n  updatePreferences: (updates: Partial<UserPreferences>) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  defaultTheme?: Theme;\n}\n\nexport function ThemeProvider({ children, defaultTheme = 'system' }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>(defaultTheme);\n  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');\n  const [preferences, setPreferences] = useState<UserPreferences | null>(null);\n  const [mounted, setMounted] = useState(false);\n\n  // Load preferences from localStorage on mount\n  useEffect(() => {\n    const savedPreferences = localStorageManager.getPreferences();\n    if (savedPreferences) {\n      setPreferences(savedPreferences);\n      setThemeState(savedPreferences.theme);\n    } else {\n      // Create default preferences\n      const defaultPreferences: UserPreferences = {\n        id: 'default',\n        theme: 'system',\n        fontSize: 16,\n        fontFamily: 'Inter',\n        spellCheckEnabled: true,\n        autoSaveInterval: 2000\n      };\n      setPreferences(defaultPreferences);\n      localStorageManager.savePreferences(defaultPreferences);\n    }\n    setMounted(true);\n  }, []);\n\n  // Update actual theme based on theme setting and system preference\n  useEffect(() => {\n    if (!mounted) return;\n\n    const updateActualTheme = () => {\n      if (theme === 'system') {\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        setActualTheme(systemTheme);\n      } else {\n        setActualTheme(theme);\n      }\n    };\n\n    updateActualTheme();\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateActualTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme, mounted]);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = window.document.documentElement;\n    \n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(actualTheme);\n    \n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      metaThemeColor.setAttribute('content', actualTheme === 'dark' ? '#1f2937' : '#ffffff');\n    }\n  }, [actualTheme, mounted]);\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    \n    // Update preferences\n    if (preferences) {\n      const updatedPreferences = { ...preferences, theme: newTheme };\n      setPreferences(updatedPreferences);\n      localStorageManager.savePreferences(updatedPreferences);\n    }\n  };\n\n  const updatePreferences = (updates: Partial<UserPreferences>) => {\n    if (!preferences) return;\n    \n    const updatedPreferences = { ...preferences, ...updates };\n    setPreferences(updatedPreferences);\n    localStorageManager.savePreferences(updatedPreferences);\n    \n    // If theme was updated, update theme state\n    if (updates.theme) {\n      setThemeState(updates.theme);\n    }\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    actualTheme,\n    setTheme,\n    preferences,\n    updatePreferences\n  };\n\n  // Don't render until mounted to avoid hydration mismatch\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\n// Theme toggle component\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nexport function ThemeToggle({ className = '' }: ThemeToggleProps) {\n  const { theme, setTheme } = useTheme();\n\n  const themes: { value: Theme; label: string; icon: string }[] = [\n    { value: 'light', label: 'Light', icon: '☀️' },\n    { value: 'dark', label: 'Dark', icon: '🌙' },\n    { value: 'system', label: 'System', icon: '💻' }\n  ];\n\n  return (\n    <div className={`flex items-center space-x-1 ${className}`}>\n      {themes.map(({ value, label, icon }) => (\n        <button\n          key={value}\n          onClick={() => setTheme(value)}\n          className={`\n            flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors\n            ${theme === value\n              ? 'bg-blue-600 text-white'\n              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n            }\n          `}\n          title={`Switch to ${label.toLowerCase()} theme`}\n        >\n          <span>{icon}</span>\n          <span className=\"hidden sm:inline\">{label}</span>\n        </button>\n      ))}\n    </div>\n  );\n}\n\n// Hook to get system theme preference\nexport function useSystemTheme() {\n  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const updateSystemTheme = () => {\n      setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n    };\n\n    updateSystemTheme();\n    mediaQuery.addEventListener('change', updateSystemTheme);\n    \n    return () => mediaQuery.removeEventListener('change', updateSystemTheme);\n  }, []);\n\n  return systemTheme;\n}\n\n// Hook to detect if user prefers reduced motion\nexport function usePrefersReducedMotion() {\n  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    \n    const updatePreference = () => {\n      setPrefersReducedMotion(mediaQuery.matches);\n    };\n\n    updatePreference();\n    mediaQuery.addEventListener('change', updatePreference);\n    \n    return () => mediaQuery.removeEventListener('change', updatePreference);\n  }, []);\n\n  return prefersReducedMotion;\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAO1D,SAAS,cAAc,EAAE,QAAQ,EAAE,eAAe,QAAQ,EAAsB;IACrF,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,0HAAA,CAAA,sBAAmB,CAAC,cAAc;QAC3D,IAAI,kBAAkB;YACpB,eAAe;YACf,cAAc,iBAAiB,KAAK;QACtC,OAAO;YACL,6BAA6B;YAC7B,MAAM,qBAAsC;gBAC1C,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,YAAY;gBACZ,mBAAmB;gBACnB,kBAAkB;YACpB;YACA,eAAe;YACf,0HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACtC;QACA,WAAW;IACb,GAAG,EAAE;IAEL,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,oBAAoB;YACxB,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;gBACzF,eAAe;YACjB,OAAO;gBACL,eAAe;YACjB;QACF;QAEA;QAEA,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,eAAe;YACnB,IAAI,UAAU,UAAU;gBACtB;YACF;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;QAAO;KAAQ;IAEnB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;QAEd,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;QAE5C,gCAAgC;QAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;QAE/B,0BAA0B;QAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;QAEnB,8CAA8C;QAC9C,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,IAAI,gBAAgB;YAClB,eAAe,YAAY,CAAC,WAAW,gBAAgB,SAAS,YAAY;QAC9E;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,WAAW,CAAC;QAChB,cAAc;QAEd,qBAAqB;QACrB,IAAI,aAAa;YACf,MAAM,qBAAqB;gBAAE,GAAG,WAAW;gBAAE,OAAO;YAAS;YAC7D,eAAe;YACf,0HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACtC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,aAAa;QAElB,MAAM,qBAAqB;YAAE,GAAG,WAAW;YAAE,GAAG,OAAO;QAAC;QACxD,eAAe;QACf,0HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QAEpC,2CAA2C;QAC3C,IAAI,QAAQ,KAAK,EAAE;YACjB,cAAc,QAAQ,KAAK;QAC7B;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;IACF;IAEA,yDAAyD;IACzD,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAOO,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;IAC9D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,SAA0D;QAC9D;YAAE,OAAO;YAAS,OAAO;YAAS,MAAM;QAAK;QAC7C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM;QAAK;QAC3C;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM;QAAK;KAChD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;kBACvD,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,iBACjC,8OAAC;gBAEC,SAAS,IAAM,SAAS;gBACxB,WAAW,CAAC;;YAEV,EAAE,UAAU,QACR,2BACA,4EACH;UACH,CAAC;gBACD,OAAO,CAAC,UAAU,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC;;kCAE/C,8OAAC;kCAAM;;;;;;kCACP,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;eAZ/B;;;;;;;;;;AAiBf;AAGO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QAErC,MAAM,oBAAoB;YACxB,eAAe,WAAW,OAAO,GAAG,SAAS;QAC/C;QAEA;QACA,WAAW,gBAAgB,CAAC,UAAU;QAEtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS;IACd,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QAErC,MAAM,mBAAmB;YACvB,wBAAwB,WAAW,OAAO;QAC5C;QAEA;QACA,WAAW,gBAAgB,CAAC,UAAU;QAEtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}