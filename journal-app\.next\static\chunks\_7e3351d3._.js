(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/localStorage.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LocalStorageManager": (()=>LocalStorageManager),
    "localStorageManager": (()=>localStorageManager)
});
// Local storage keys
const STORAGE_KEYS = {
    ENTRIES: 'journal_entries',
    TAGS: 'journal_tags',
    PREFERENCES: 'journal_preferences',
    DRAFT: 'journal_draft',
    PENDING_SYNC: 'journal_pending_sync',
    LAST_SYNC: 'journal_last_sync'
};
class LocalStorageManager {
    // Check if localStorage is available
    isLocalStorageAvailable() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch  {
            return false;
        }
    }
    // Generic storage methods
    setItem(key, value) {
        if (!this.isLocalStorageAvailable()) return;
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
        }
    }
    getItem(key) {
        if (!this.isLocalStorageAvailable()) return null;
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('Failed to read from localStorage:', error);
            return null;
        }
    }
    removeItem(key) {
        if (!this.isLocalStorageAvailable()) return;
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
        }
    }
    // Journal entries management
    saveEntries(entries) {
        this.setItem(STORAGE_KEYS.ENTRIES, entries);
    }
    getEntries() {
        const entries = this.getItem(STORAGE_KEYS.ENTRIES);
        return entries || [];
    }
    saveEntry(entry) {
        const entries = this.getEntries();
        const existingIndex = entries.findIndex((e)=>e.id === entry.id);
        if (existingIndex >= 0) {
            entries[existingIndex] = entry;
        } else {
            entries.unshift(entry); // Add new entries at the beginning
        }
        this.saveEntries(entries);
        this.markForSync(entry.id);
    }
    deleteEntry(entryId) {
        const entries = this.getEntries();
        const filteredEntries = entries.filter((e)=>e.id !== entryId);
        this.saveEntries(filteredEntries);
        this.markForSync(entryId, 'delete');
    }
    getEntry(entryId) {
        const entries = this.getEntries();
        return entries.find((e)=>e.id === entryId) || null;
    }
    // Tags management
    saveTags(tags) {
        this.setItem(STORAGE_KEYS.TAGS, tags);
    }
    getTags() {
        const tags = this.getItem(STORAGE_KEYS.TAGS);
        return tags || [];
    }
    saveTag(tag) {
        const tags = this.getTags();
        const existingIndex = tags.findIndex((t)=>t.id === tag.id);
        if (existingIndex >= 0) {
            tags[existingIndex] = tag;
        } else {
            tags.push(tag);
        }
        this.saveTags(tags);
    }
    // User preferences management
    savePreferences(preferences) {
        this.setItem(STORAGE_KEYS.PREFERENCES, preferences);
    }
    getPreferences() {
        return this.getItem(STORAGE_KEYS.PREFERENCES);
    }
    // Draft management for auto-save
    saveDraft(entryId, content) {
        const drafts = this.getDrafts();
        drafts[entryId] = {
            content,
            timestamp: Date.now()
        };
        this.setItem(STORAGE_KEYS.DRAFT, drafts);
    }
    getDraft(entryId) {
        const drafts = this.getDrafts();
        return drafts[entryId] || null;
    }
    clearDraft(entryId) {
        const drafts = this.getDrafts();
        delete drafts[entryId];
        this.setItem(STORAGE_KEYS.DRAFT, drafts);
    }
    getDrafts() {
        return this.getItem(STORAGE_KEYS.DRAFT) || {};
    }
    // Sync management
    markForSync(entryId, action = 'update') {
        const pendingSync = this.getPendingSync();
        pendingSync[entryId] = {
            action,
            timestamp: Date.now()
        };
        this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
    }
    getPendingSync() {
        return this.getItem(STORAGE_KEYS.PENDING_SYNC) || {};
    }
    clearPendingSync(entryId) {
        const pendingSync = this.getPendingSync();
        delete pendingSync[entryId];
        this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
    }
    setLastSyncTime(timestamp) {
        this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
    }
    getLastSyncTime() {
        return this.getItem(STORAGE_KEYS.LAST_SYNC);
    }
    // Search functionality
    searchEntries(query, tags) {
        const entries = this.getEntries();
        const lowercaseQuery = query.toLowerCase();
        return entries.filter((entry)=>{
            // Text search
            const titleMatch = entry.title.toLowerCase().includes(lowercaseQuery);
            const contentMatch = entry.content.toLowerCase().includes(lowercaseQuery);
            const textMatch = titleMatch || contentMatch;
            // Tag filter
            const tagMatch = !tags || tags.length === 0 || tags.every((tag)=>entry.tags.includes(tag));
            return textMatch && tagMatch;
        }).sort((a, b)=>b.updatedAt.getTime() - a.updatedAt.getTime());
    }
    // Utility methods
    getStorageUsage() {
        if (!this.isLocalStorageAvailable()) {
            return {
                used: 0,
                available: 0
            };
        }
        let used = 0;
        for(const key in localStorage){
            if (localStorage.hasOwnProperty(key)) {
                used += localStorage[key].length;
            }
        }
        // Estimate available space (most browsers have ~5-10MB limit)
        const estimated = 5 * 1024 * 1024; // 5MB
        return {
            used,
            available: Math.max(0, estimated - used)
        };
    }
    clearAllData() {
        Object.values(STORAGE_KEYS).forEach((key)=>{
            this.removeItem(key);
        });
    }
    // Export/Import functionality
    exportData() {
        return {
            entries: this.getEntries(),
            tags: this.getTags(),
            preferences: this.getPreferences(),
            exportDate: new Date().toISOString()
        };
    }
    importData(data) {
        if (data.entries) {
            this.saveEntries(data.entries);
        }
        if (data.tags) {
            this.saveTags(data.tags);
        }
        if (data.preferences) {
            this.savePreferences(data.preferences);
        }
    }
    // Auto-cleanup old drafts (older than 7 days)
    cleanupOldDrafts() {
        const drafts = this.getDrafts();
        const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
        let hasChanges = false;
        Object.keys(drafts).forEach((entryId)=>{
            if (drafts[entryId].timestamp < sevenDaysAgo) {
                delete drafts[entryId];
                hasChanges = true;
            }
        });
        if (hasChanges) {
            this.setItem(STORAGE_KEYS.DRAFT, drafts);
        }
    }
}
const localStorageManager = new LocalStorageManager();
// Auto-cleanup on initialization
localStorageManager.cleanupOldDrafts();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ThemeProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "ThemeToggle": (()=>ThemeToggle),
    "usePrefersReducedMotion": (()=>usePrefersReducedMotion),
    "useSystemTheme": (()=>useSystemTheme),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/localStorage.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ThemeProvider({ children, defaultTheme = 'system' }) {
    _s();
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultTheme);
    const [actualTheme, setActualTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    const [preferences, setPreferences] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load preferences from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            const savedPreferences = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorageManager"].getPreferences();
            if (savedPreferences) {
                setPreferences(savedPreferences);
                setThemeState(savedPreferences.theme);
            } else {
                // Create default preferences
                const defaultPreferences = {
                    id: 'default',
                    theme: 'system',
                    fontSize: 16,
                    fontFamily: 'Inter',
                    spellCheckEnabled: true,
                    autoSaveInterval: 2000
                };
                setPreferences(defaultPreferences);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(defaultPreferences);
            }
            setMounted(true);
        }
    }["ThemeProvider.useEffect"], []);
    // Update actual theme based on theme setting and system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const updateActualTheme = {
                "ThemeProvider.useEffect.updateActualTheme": ()=>{
                    if (theme === 'system') {
                        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                        setActualTheme(systemTheme);
                    } else {
                        setActualTheme(theme);
                    }
                }
            }["ThemeProvider.useEffect.updateActualTheme"];
            updateActualTheme();
            // Listen for system theme changes
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleChange = {
                "ThemeProvider.useEffect.handleChange": ()=>{
                    if (theme === 'system') {
                        updateActualTheme();
                    }
                }
            }["ThemeProvider.useEffect.handleChange"];
            mediaQuery.addEventListener('change', handleChange);
            return ({
                "ThemeProvider.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
            })["ThemeProvider.useEffect"];
        }
    }["ThemeProvider.useEffect"], [
        theme,
        mounted
    ]);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const root = window.document.documentElement;
            // Remove previous theme classes
            root.classList.remove('light', 'dark');
            // Add current theme class
            root.classList.add(actualTheme);
            // Update meta theme-color for mobile browsers
            const metaThemeColor = document.querySelector('meta[name="theme-color"]');
            if (metaThemeColor) {
                metaThemeColor.setAttribute('content', actualTheme === 'dark' ? '#1f2937' : '#ffffff');
            }
        }
    }["ThemeProvider.useEffect"], [
        actualTheme,
        mounted
    ]);
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
        // Update preferences
        if (preferences) {
            const updatedPreferences = {
                ...preferences,
                theme: newTheme
            };
            setPreferences(updatedPreferences);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(updatedPreferences);
        }
    };
    const updatePreferences = (updates)=>{
        if (!preferences) return;
        const updatedPreferences = {
            ...preferences,
            ...updates
        };
        setPreferences(updatedPreferences);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$localStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["localStorageManager"].savePreferences(updatedPreferences);
        // If theme was updated, update theme state
        if (updates.theme) {
            setThemeState(updates.theme);
        }
    };
    const value = {
        theme,
        actualTheme,
        setTheme,
        preferences,
        updatePreferences
    };
    // Don't render until mounted to avoid hydration mismatch
    if (!mounted) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeProvider.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
}
_s(ThemeProvider, "***************************=");
_c = ThemeProvider;
function useTheme() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
_s1(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function ThemeToggle({ className = '' }) {
    _s2();
    const { theme, setTheme } = useTheme();
    const themes = [
        {
            value: 'light',
            label: 'Light',
            icon: '☀️'
        },
        {
            value: 'dark',
            label: 'Dark',
            icon: '🌙'
        },
        {
            value: 'system',
            label: 'System',
            icon: '💻'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center space-x-1 ${className}`,
        children: themes.map(({ value, label, icon })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setTheme(value),
                className: `
            flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${theme === value ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}
          `,
                title: `Switch to ${label.toLowerCase()} theme`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: icon
                    }, void 0, false, {
                        fileName: "[project]/src/components/ThemeProvider.tsx",
                        lineNumber: 179,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "hidden sm:inline",
                        children: label
                    }, void 0, false, {
                        fileName: "[project]/src/components/ThemeProvider.tsx",
                        lineNumber: 180,
                        columnNumber: 11
                    }, this)
                ]
            }, value, true, {
                fileName: "[project]/src/components/ThemeProvider.tsx",
                lineNumber: 167,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/ThemeProvider.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, this);
}
_s2(ThemeToggle, "5ABGV54qnXKp6rHn7MS/8MjwRhQ=", false, function() {
    return [
        useTheme
    ];
});
_c1 = ThemeToggle;
function useSystemTheme() {
    _s3();
    const [systemTheme, setSystemTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSystemTheme.useEffect": ()=>{
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const updateSystemTheme = {
                "useSystemTheme.useEffect.updateSystemTheme": ()=>{
                    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
                }
            }["useSystemTheme.useEffect.updateSystemTheme"];
            updateSystemTheme();
            mediaQuery.addEventListener('change', updateSystemTheme);
            return ({
                "useSystemTheme.useEffect": ()=>mediaQuery.removeEventListener('change', updateSystemTheme)
            })["useSystemTheme.useEffect"];
        }
    }["useSystemTheme.useEffect"], []);
    return systemTheme;
}
_s3(useSystemTheme, "qhNRPuY27tvw2LahbgnRY0uG6pE=");
function usePrefersReducedMotion() {
    _s4();
    const [prefersReducedMotion, setPrefersReducedMotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePrefersReducedMotion.useEffect": ()=>{
            const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            const updatePreference = {
                "usePrefersReducedMotion.useEffect.updatePreference": ()=>{
                    setPrefersReducedMotion(mediaQuery.matches);
                }
            }["usePrefersReducedMotion.useEffect.updatePreference"];
            updatePreference();
            mediaQuery.addEventListener('change', updatePreference);
            return ({
                "usePrefersReducedMotion.useEffect": ()=>mediaQuery.removeEventListener('change', updatePreference)
            })["usePrefersReducedMotion.useEffect"];
        }
    }["usePrefersReducedMotion.useEffect"], []);
    return prefersReducedMotion;
}
_s4(usePrefersReducedMotion, "c2o+PeDo1dLruq/wbnW+Z6a6rIY=");
var _c, _c1;
__turbopack_context__.k.register(_c, "ThemeProvider");
__turbopack_context__.k.register(_c1, "ThemeToggle");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_7e3351d3._.js.map