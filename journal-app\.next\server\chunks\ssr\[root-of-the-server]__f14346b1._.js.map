{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport TextareaAutosize from 'react-textarea-autosize';\nimport { JournalEntry, UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Save, Clock, Type } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface TextEditorProps {\n  entry?: JournalEntry;\n  onSave: (entry: JournalEntry) => void;\n  onAutoSave?: (content: string) => void;\n  preferences: UserPreferences;\n  className?: string;\n}\n\nexport default function TextEditor({ \n  entry, \n  onSave, \n  onAutoSave, \n  preferences, \n  className = '' \n}: TextEditorProps) {\n  const [title, setTitle] = useState(entry?.title || '');\n  const [content, setContent] = useState(entry?.content || '');\n  const [tags, setTags] = useState<string[]>(entry?.tags || []);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(entry?.updatedAt || null);\n  const [wordCount, setWordCount] = useState(0);\n  \n  const titleRef = useRef<HTMLInputElement>(null);\n  const contentRef = useRef<HTMLTextAreaElement>(null);\n  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();\n  const entryIdRef = useRef(entry?.id || uuidv4());\n\n  // Calculate word count\n  const calculateWordCount = useCallback((text: string) => {\n    return text.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  }, []);\n\n  // Auto-save functionality\n  const triggerAutoSave = useCallback(() => {\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    autoSaveTimeoutRef.current = setTimeout(() => {\n      if (content.trim() || title.trim()) {\n        // Save draft to localStorage\n        localStorageManager.saveDraft(entryIdRef.current, content);\n        \n        // Trigger auto-save callback if provided\n        if (onAutoSave) {\n          onAutoSave(content);\n        }\n\n        setLastSaved(new Date());\n      }\n    }, preferences.autoSaveInterval);\n  }, [content, title, preferences.autoSaveInterval, onAutoSave]);\n\n  // Handle content changes\n  const handleContentChange = useCallback((value: string) => {\n    setContent(value);\n    setWordCount(calculateWordCount(value));\n    triggerAutoSave();\n  }, [calculateWordCount, triggerAutoSave]);\n\n  // Handle title changes\n  const handleTitleChange = useCallback((value: string) => {\n    setTitle(value);\n    triggerAutoSave();\n  }, [triggerAutoSave]);\n\n  // Manual save\n  const handleSave = useCallback(async () => {\n    if (!title.trim() && !content.trim()) return;\n\n    setIsSaving(true);\n    \n    try {\n      const now = new Date();\n      const savedEntry: JournalEntry = {\n        id: entryIdRef.current,\n        title: title.trim() || 'Untitled',\n        content: content,\n        tags: tags,\n        createdAt: entry?.createdAt || now,\n        updatedAt: now,\n        wordCount: calculateWordCount(content)\n      };\n\n      // Save to localStorage\n      localStorageManager.saveEntry(savedEntry);\n      \n      // Clear draft\n      localStorageManager.clearDraft(entryIdRef.current);\n      \n      // Call parent save handler\n      onSave(savedEntry);\n      \n      setLastSaved(now);\n    } catch (error) {\n      console.error('Failed to save entry:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  }, [title, content, tags, entry, calculateWordCount, onSave]);\n\n  // Load draft on mount\n  useEffect(() => {\n    if (!entry) {\n      const draft = localStorageManager.getDraft(entryIdRef.current);\n      if (draft) {\n        setContent(draft.content);\n        setWordCount(calculateWordCount(draft.content));\n      }\n    }\n  }, [entry, calculateWordCount]);\n\n  // Update word count when content changes\n  useEffect(() => {\n    setWordCount(calculateWordCount(content));\n  }, [content, calculateWordCount]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave]);\n\n  // Cleanup auto-save timeout\n  useEffect(() => {\n    return () => {\n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  const formatLastSaved = (date: Date | null) => {\n    if (!date) return 'Never';\n    \n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    \n    if (diff < 60000) return 'Just now';\n    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;\n    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;\n    \n    return date.toLocaleDateString();\n  };\n\n  return (\n    <div className={`flex flex-col h-full ${className}`}>\n      {/* Header with save status */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n          <Clock className=\"w-4 h-4\" />\n          <span>Last saved: {formatLastSaved(lastSaved)}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n            <Type className=\"w-4 h-4\" />\n            <span>{wordCount} words</span>\n          </div>\n          \n          <button\n            onClick={handleSave}\n            disabled={isSaving || (!title.trim() && !content.trim())}\n            className=\"flex items-center space-x-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <Save className=\"w-4 h-4\" />\n            <span>{isSaving ? 'Saving...' : 'Save'}</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Editor content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Title input */}\n        <div className=\"p-4 border-b border-gray-100 dark:border-gray-700\">\n          <input\n            ref={titleRef}\n            type=\"text\"\n            value={title}\n            onChange={(e) => handleTitleChange(e.target.value)}\n            placeholder=\"Enter a title...\"\n            className=\"w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none\"\n            style={{ fontSize: `${preferences.fontSize + 8}px` }}\n          />\n        </div>\n\n        {/* Content textarea */}\n        <div className=\"flex-1 p-4 overflow-auto\">\n          <TextareaAutosize\n            ref={contentRef}\n            value={content}\n            onChange={(e) => handleContentChange(e.target.value)}\n            placeholder=\"Start writing your thoughts...\"\n            className=\"w-full h-full bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none leading-relaxed\"\n            style={{\n              fontSize: `${preferences.fontSize}px`,\n              fontFamily: preferences.fontFamily\n            }}\n            minRows={10}\n          />\n        </div>\n      </div>\n\n      {/* Mobile-friendly bottom toolbar */}\n      <div className=\"md:hidden p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n            {wordCount} words\n          </div>\n          \n          <button\n            onClick={handleSave}\n            disabled={isSaving || (!title.trim() && !content.trim())}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <Save className=\"w-4 h-4\" />\n            <span>{isSaving ? 'Saving...' : 'Save'}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;AAiBe,SAAS,WAAW,EACjC,KAAK,EACL,MAAM,EACN,UAAU,EACV,WAAW,EACX,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAAS;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,WAAW;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,OAAO,QAAQ,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,OAAO,aAAa;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAC/C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAChC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;IAE5C,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IACxE,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,mBAAmB,OAAO,EAAE;YAC9B,aAAa,mBAAmB,OAAO;QACzC;QAEA,mBAAmB,OAAO,GAAG,WAAW;YACtC,IAAI,QAAQ,IAAI,MAAM,MAAM,IAAI,IAAI;gBAClC,6BAA6B;gBAC7B,0HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC,WAAW,OAAO,EAAE;gBAElD,yCAAyC;gBACzC,IAAI,YAAY;oBACd,WAAW;gBACb;gBAEA,aAAa,IAAI;YACnB;QACF,GAAG,YAAY,gBAAgB;IACjC,GAAG;QAAC;QAAS;QAAO,YAAY,gBAAgB;QAAE;KAAW;IAE7D,yBAAyB;IACzB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,WAAW;QACX,aAAa,mBAAmB;QAChC;IACF,GAAG;QAAC;QAAoB;KAAgB;IAExC,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,SAAS;QACT;IACF,GAAG;QAAC;KAAgB;IAEpB,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;QAEtC,YAAY;QAEZ,IAAI;YACF,MAAM,MAAM,IAAI;YAChB,MAAM,aAA2B;gBAC/B,IAAI,WAAW,OAAO;gBACtB,OAAO,MAAM,IAAI,MAAM;gBACvB,SAAS;gBACT,MAAM;gBACN,WAAW,OAAO,aAAa;gBAC/B,WAAW;gBACX,WAAW,mBAAmB;YAChC;YAEA,uBAAuB;YACvB,0HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;YAE9B,cAAc;YACd,0HAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC,WAAW,OAAO;YAEjD,2BAA2B;YAC3B,OAAO;YAEP,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,YAAY;QACd;IACF,GAAG;QAAC;QAAO;QAAS;QAAM;QAAO;QAAoB;KAAO;IAE5D,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,OAAO;YACV,MAAM,QAAQ,0HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,WAAW,OAAO;YAC7D,IAAI,OAAO;gBACT,WAAW,MAAM,OAAO;gBACxB,aAAa,mBAAmB,MAAM,OAAO;YAC/C;QACF;IACF,GAAG;QAAC;QAAO;KAAmB;IAE9B,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,mBAAmB;IAClC,GAAG;QAAC;QAAS;KAAmB;IAEhC,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAW;IAEf,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QAEzC,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,OAAO,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,OAAO,KAAK,CAAC;QAC7D,IAAI,OAAO,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,SAAS,KAAK,CAAC;QAEhE,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;;oCAAK;oCAAa,gBAAgB;;;;;;;;;;;;;kCAGrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;;4CAAM;4CAAU;;;;;;;;;;;;;0CAGnB,8OAAC;gCACC,SAAS;gCACT,UAAU,YAAa,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;gCACrD,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAM,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BACjD,aAAY;4BACZ,WAAU;4BACV,OAAO;gCAAE,UAAU,GAAG,YAAY,QAAQ,GAAG,EAAE,EAAE,CAAC;4BAAC;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4MAAA,CAAA,UAAgB;4BACf,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,aAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,UAAU,GAAG,YAAY,QAAQ,CAAC,EAAE,CAAC;gCACrC,YAAY,YAAY,UAAU;4BACpC;4BACA,SAAS;;;;;;;;;;;;;;;;;0BAMf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ;gCAAU;;;;;;;sCAGb,8OAAC;4BACC,SAAS;4BACT,UAAU,YAAa,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;4BACrD,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TagManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tag } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { X, Plus, Hash, Palette } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface TagManagerProps {\n  tags: Tag[];\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  onCreateTag?: (tag: Tag) => void;\n  className?: string;\n  maxTags?: number;\n}\n\nconst TAG_COLORS = [\n  '#ef4444', // red\n  '#f97316', // orange\n  '#eab308', // yellow\n  '#22c55e', // green\n  '#06b6d4', // cyan\n  '#3b82f6', // blue\n  '#8b5cf6', // violet\n  '#ec4899', // pink\n  '#6b7280', // gray\n  '#84cc16', // lime\n];\n\nexport default function TagManager({\n  tags,\n  selectedTags,\n  onTagsChange,\n  onCreateTag,\n  className = '',\n  maxTags = 10\n}: TagManagerProps) {\n  const [isCreating, setIsCreating] = useState(false);\n  const [newTagName, setNewTagName] = useState('');\n  const [newTagColor, setNewTagColor] = useState(TAG_COLORS[0]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  \n  const inputRef = useRef<HTMLInputElement>(null);\n  const colorPickerRef = useRef<HTMLDivElement>(null);\n\n  // Filter tags based on search query\n  const filteredTags = tags.filter(tag =>\n    tag.name.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  // Get available tags (not already selected)\n  const availableTags = filteredTags.filter(tag =>\n    !selectedTags.includes(tag.name)\n  );\n\n  // Handle tag selection\n  const handleTagSelect = (tagName: string) => {\n    if (selectedTags.length >= maxTags) return;\n    \n    const newSelectedTags = [...selectedTags, tagName];\n    onTagsChange(newSelectedTags);\n    setSearchQuery('');\n  };\n\n  // Handle tag removal\n  const handleTagRemove = (tagName: string) => {\n    const newSelectedTags = selectedTags.filter(t => t !== tagName);\n    onTagsChange(newSelectedTags);\n  };\n\n  // Handle new tag creation\n  const handleCreateTag = () => {\n    const trimmedName = newTagName.trim();\n    if (!trimmedName) return;\n\n    // Check if tag already exists\n    const existingTag = tags.find(tag => \n      tag.name.toLowerCase() === trimmedName.toLowerCase()\n    );\n\n    if (existingTag) {\n      // If tag exists, just select it\n      handleTagSelect(existingTag.name);\n    } else {\n      // Create new tag\n      const newTag: Tag = {\n        id: uuidv4(),\n        name: trimmedName,\n        color: newTagColor,\n        createdAt: new Date(),\n        usageCount: 1\n      };\n\n      // Save to localStorage\n      localStorageManager.saveTag(newTag);\n\n      // Call parent callback\n      if (onCreateTag) {\n        onCreateTag(newTag);\n      }\n\n      // Select the new tag\n      handleTagSelect(newTag.name);\n    }\n\n    // Reset form\n    setNewTagName('');\n    setIsCreating(false);\n    setShowColorPicker(false);\n  };\n\n  // Handle input key events\n  const handleInputKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (isCreating) {\n        handleCreateTag();\n      } else if (availableTags.length > 0) {\n        handleTagSelect(availableTags[0].name);\n      }\n    } else if (e.key === 'Escape') {\n      setIsCreating(false);\n      setSearchQuery('');\n      setNewTagName('');\n    } else if (e.key === 'Backspace' && !searchQuery && !newTagName && selectedTags.length > 0) {\n      // Remove last selected tag when backspacing on empty input\n      handleTagRemove(selectedTags[selectedTags.length - 1]);\n    }\n  };\n\n  // Focus input when creating\n  useEffect(() => {\n    if (isCreating && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isCreating]);\n\n  // Close color picker when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {\n        setShowColorPicker(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getTagColor = (tagName: string) => {\n    const tag = tags.find(t => t.name === tagName);\n    return tag?.color || TAG_COLORS[0];\n  };\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* Selected tags */}\n      {selectedTags.length > 0 && (\n        <div className=\"flex flex-wrap gap-2\">\n          {selectedTags.map(tagName => (\n            <span\n              key={tagName}\n              className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-sm font-medium text-white\"\n              style={{ backgroundColor: getTagColor(tagName) }}\n            >\n              <Hash className=\"w-3 h-3\" />\n              {tagName}\n              <button\n                onClick={() => handleTagRemove(tagName)}\n                className=\"ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors\"\n              >\n                <X className=\"w-3 h-3\" />\n              </button>\n            </span>\n          ))}\n        </div>\n      )}\n\n      {/* Tag input */}\n      <div className=\"relative\">\n        <div className=\"flex items-center gap-2 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800\">\n          <Hash className=\"w-4 h-4 text-gray-400\" />\n          \n          {isCreating ? (\n            <div className=\"flex-1 flex items-center gap-2\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={newTagName}\n                onChange={(e) => setNewTagName(e.target.value)}\n                onKeyDown={handleInputKeyDown}\n                placeholder=\"Enter tag name...\"\n                className=\"flex-1 bg-transparent outline-none text-gray-900 dark:text-white\"\n                maxLength={30}\n              />\n              \n              <div className=\"relative\" ref={colorPickerRef}>\n                <button\n                  onClick={() => setShowColorPicker(!showColorPicker)}\n                  className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  <div\n                    className=\"w-4 h-4 rounded-full border border-gray-300\"\n                    style={{ backgroundColor: newTagColor }}\n                  />\n                </button>\n                \n                {showColorPicker && (\n                  <div className=\"absolute top-full right-0 mt-1 p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10\">\n                    <div className=\"grid grid-cols-5 gap-1\">\n                      {TAG_COLORS.map(color => (\n                        <button\n                          key={color}\n                          onClick={() => {\n                            setNewTagColor(color);\n                            setShowColorPicker(false);\n                          }}\n                          className=\"w-6 h-6 rounded-full border border-gray-300 hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: color }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n              \n              <button\n                onClick={handleCreateTag}\n                disabled={!newTagName.trim()}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                Create\n              </button>\n              \n              <button\n                onClick={() => {\n                  setIsCreating(false);\n                  setNewTagName('');\n                  setShowColorPicker(false);\n                }}\n                className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            </div>\n          ) : (\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              onKeyDown={handleInputKeyDown}\n              placeholder={selectedTags.length >= maxTags ? `Max ${maxTags} tags reached` : \"Search or add tags...\"}\n              className=\"flex-1 bg-transparent outline-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500\"\n              disabled={selectedTags.length >= maxTags}\n            />\n          )}\n          \n          {!isCreating && selectedTags.length < maxTags && (\n            <button\n              onClick={() => setIsCreating(true)}\n              className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n              title=\"Create new tag\"\n            >\n              <Plus className=\"w-4 h-4\" />\n            </button>\n          )}\n        </div>\n\n        {/* Tag suggestions */}\n        {!isCreating && searchQuery && availableTags.length > 0 && (\n          <div className=\"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto\">\n            {availableTags.slice(0, 8).map(tag => (\n              <button\n                key={tag.id}\n                onClick={() => handleTagSelect(tag.name)}\n                className=\"w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: tag.color || TAG_COLORS[0] }}\n                />\n                <span className=\"text-gray-900 dark:text-white\">{tag.name}</span>\n                <span className=\"ml-auto text-xs text-gray-500 dark:text-gray-400\">\n                  {tag.usageCount} uses\n                </span>\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Tag limit indicator */}\n      {selectedTags.length > 0 && (\n        <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n          {selectedTags.length} / {maxTags} tags\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AACA;AANA;;;;;;AAiBA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,WAAW,EACjC,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACd,UAAU,EAAE,EACI;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,oCAAoC;IACpC,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAGzD,4CAA4C;IAC5C,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,MACxC,CAAC,aAAa,QAAQ,CAAC,IAAI,IAAI;IAGjC,uBAAuB;IACvB,MAAM,kBAAkB,CAAC;QACvB,IAAI,aAAa,MAAM,IAAI,SAAS;QAEpC,MAAM,kBAAkB;eAAI;YAAc;SAAQ;QAClD,aAAa;QACb,eAAe;IACjB;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QACvD,aAAa;IACf;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB;QACtB,MAAM,cAAc,WAAW,IAAI;QACnC,IAAI,CAAC,aAAa;QAElB,8BAA8B;QAC9B,MAAM,cAAc,KAAK,IAAI,CAAC,CAAA,MAC5B,IAAI,IAAI,CAAC,WAAW,OAAO,YAAY,WAAW;QAGpD,IAAI,aAAa;YACf,gCAAgC;YAChC,gBAAgB,YAAY,IAAI;QAClC,OAAO;YACL,iBAAiB;YACjB,MAAM,SAAc;gBAClB,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;gBACT,MAAM;gBACN,OAAO;gBACP,WAAW,IAAI;gBACf,YAAY;YACd;YAEA,uBAAuB;YACvB,0HAAA,CAAA,sBAAmB,CAAC,OAAO,CAAC;YAE5B,uBAAuB;YACvB,IAAI,aAAa;gBACf,YAAY;YACd;YAEA,qBAAqB;YACrB,gBAAgB,OAAO,IAAI;QAC7B;QAEA,aAAa;QACb,cAAc;QACd,cAAc;QACd,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,YAAY;gBACd;YACF,OAAO,IAAI,cAAc,MAAM,GAAG,GAAG;gBACnC,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI;YACvC;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,cAAc;YACd,eAAe;YACf,cAAc;QAChB,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,eAAe,CAAC,cAAc,aAAa,MAAM,GAAG,GAAG;YAC1F,2DAA2D;YAC3D,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QACvD;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,SAAS,OAAO,EAAE;YAClC,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAW;IAEf,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACpF,mBAAmB;YACrB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACtC,OAAO,KAAK,SAAS,UAAU,CAAC,EAAE;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YAErC,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,YAAY;wBAAS;;0CAE/C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf;0CACD,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;uBAVV;;;;;;;;;;0BAkBb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAEf,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW;wCACX,aAAY;wCACZ,WAAU;wCACV,WAAW;;;;;;kDAGb,8OAAC;wCAAI,WAAU;wCAAW,KAAK;;0DAC7B,8OAAC;gDACC,SAAS,IAAM,mBAAmB,CAAC;gDACnC,WAAU;0DAEV,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAY;;;;;;;;;;;4CAIzC,iCACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAA,sBACd,8OAAC;4DAEC,SAAS;gEACP,eAAe;gEACf,mBAAmB;4DACrB;4DACA,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAM;2DAN3B;;;;;;;;;;;;;;;;;;;;;kDAcjB,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAID,8OAAC;wCACC,SAAS;4CACP,cAAc;4CACd,cAAc;4CACd,mBAAmB;wCACrB;wCACA,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAIjB,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAW;gCACX,aAAa,aAAa,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,QAAQ,aAAa,CAAC,GAAG;gCAC9E,WAAU;gCACV,UAAU,aAAa,MAAM,IAAI;;;;;;4BAIpC,CAAC,cAAc,aAAa,MAAM,GAAG,yBACpC,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMrB,CAAC,cAAc,eAAe,cAAc,MAAM,GAAG,mBACpD,8OAAC;wBAAI,WAAU;kCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,oBAC7B,8OAAC;gCAEC,SAAS,IAAM,gBAAgB,IAAI,IAAI;gCACvC,WAAU;;kDAEV,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,IAAI,KAAK,IAAI,UAAU,CAAC,EAAE;wCAAC;;;;;;kDAEvD,8OAAC;wCAAK,WAAU;kDAAiC,IAAI,IAAI;;;;;;kDACzD,8OAAC;wCAAK,WAAU;;4CACb,IAAI,UAAU;4CAAC;;;;;;;;+BAVb,IAAI,EAAE;;;;;;;;;;;;;;;;YAmBpB,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM;oBAAC;oBAAI;oBAAQ;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport { JournalEntry } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Upload, File, X, Check, AlertCircle, FileText } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface FileUploadProps {\n  onEntriesImported: (entries: JournalEntry[]) => void;\n  className?: string;\n  maxFileSize?: number; // in MB\n  acceptedTypes?: string[];\n}\n\ninterface UploadedFile {\n  id: string;\n  file: File;\n  content: string;\n  status: 'processing' | 'success' | 'error';\n  error?: string;\n  entry?: JournalEntry;\n}\n\nexport default function FileUpload({\n  onEntriesImported,\n  className = '',\n  maxFileSize = 10,\n  acceptedTypes = ['.txt', '.md', '.rtf', '.doc', '.docx']\n}: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  \n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Extract text content from different file types\n  const extractTextContent = useCallback(async (file: File): Promise<string> => {\n    const fileExtension = file.name.toLowerCase().split('.').pop();\n    \n    switch (fileExtension) {\n      case 'txt':\n      case 'md':\n      case 'rtf':\n        return await file.text();\n      \n      case 'doc':\n      case 'docx':\n        // For now, we'll just read as text. In a real app, you'd use a library like mammoth.js\n        try {\n          return await file.text();\n        } catch {\n          throw new Error('Unable to read Word document. Please save as .txt or .md format.');\n        }\n      \n      default:\n        throw new Error(`Unsupported file type: ${fileExtension}`);\n    }\n  }, []);\n\n  // Process uploaded files\n  const processFiles = useCallback(async (files: FileList) => {\n    setIsProcessing(true);\n    const newUploadedFiles: UploadedFile[] = [];\n\n    for (const file of Array.from(files)) {\n      // Check file size\n      if (file.size > maxFileSize * 1024 * 1024) {\n        newUploadedFiles.push({\n          id: uuidv4(),\n          file,\n          content: '',\n          status: 'error',\n          error: `File size exceeds ${maxFileSize}MB limit`\n        });\n        continue;\n      }\n\n      // Check file type\n      const fileExtension = '.' + file.name.toLowerCase().split('.').pop();\n      if (!acceptedTypes.includes(fileExtension)) {\n        newUploadedFiles.push({\n          id: uuidv4(),\n          file,\n          content: '',\n          status: 'error',\n          error: `Unsupported file type. Accepted types: ${acceptedTypes.join(', ')}`\n        });\n        continue;\n      }\n\n      const uploadedFile: UploadedFile = {\n        id: uuidv4(),\n        file,\n        content: '',\n        status: 'processing'\n      };\n\n      newUploadedFiles.push(uploadedFile);\n\n      try {\n        // Extract text content\n        const content = await extractTextContent(file);\n        \n        // Create journal entry\n        const entry: JournalEntry = {\n          id: uuidv4(),\n          title: file.name.replace(/\\.[^/.]+$/, ''), // Remove file extension\n          content: content.trim(),\n          tags: ['imported'],\n          createdAt: new Date(file.lastModified || Date.now()),\n          updatedAt: new Date(),\n          wordCount: content.trim().split(/\\s+/).filter(word => word.length > 0).length\n        };\n\n        // Update the uploaded file with success status\n        uploadedFile.content = content;\n        uploadedFile.status = 'success';\n        uploadedFile.entry = entry;\n\n      } catch (error) {\n        uploadedFile.status = 'error';\n        uploadedFile.error = error instanceof Error ? error.message : 'Failed to process file';\n      }\n    }\n\n    setUploadedFiles(prev => [...prev, ...newUploadedFiles]);\n    setIsProcessing(false);\n  }, [maxFileSize, acceptedTypes, extractTextContent]);\n\n  // Handle file input change\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  }, [processFiles]);\n\n  // Handle drag and drop\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n  }, [processFiles]);\n\n  // Import successful entries\n  const handleImportEntries = useCallback(() => {\n    const successfulEntries = uploadedFiles\n      .filter(file => file.status === 'success' && file.entry)\n      .map(file => file.entry!);\n\n    if (successfulEntries.length > 0) {\n      // Save to localStorage\n      successfulEntries.forEach(entry => {\n        localStorageManager.saveEntry(entry);\n      });\n\n      // Call parent callback\n      onEntriesImported(successfulEntries);\n\n      // Clear uploaded files\n      setUploadedFiles([]);\n    }\n  }, [uploadedFiles, onEntriesImported]);\n\n  // Remove uploaded file\n  const removeUploadedFile = useCallback((fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  }, []);\n\n  // Clear all uploaded files\n  const clearAllFiles = useCallback(() => {\n    setUploadedFiles([]);\n  }, []);\n\n  const successfulFiles = uploadedFiles.filter(file => file.status === 'success');\n  const hasErrors = uploadedFiles.some(file => file.status === 'error');\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Upload area */}\n      <div\n        className={`\n          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer\n          ${isDragOver \n            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n          }\n        `}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={() => fileInputRef.current?.click()}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept={acceptedTypes.join(',')}\n          onChange={handleFileInputChange}\n          className=\"hidden\"\n        />\n        \n        <div className=\"space-y-4\">\n          <div className=\"flex justify-center\">\n            <Upload className=\"w-12 h-12 text-gray-400\" />\n          </div>\n          \n          <div>\n            <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Upload your writings\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n              Drag and drop files here, or click to browse\n            </p>\n          </div>\n          \n          <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n            <p>Supported formats: {acceptedTypes.join(', ')}</p>\n            <p>Maximum file size: {maxFileSize}MB</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Processing indicator */}\n      {isProcessing && (\n        <div className=\"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3\"></div>\n          <span className=\"text-blue-600 dark:text-blue-400\">Processing files...</span>\n        </div>\n      )}\n\n      {/* Uploaded files list */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Uploaded Files ({uploadedFiles.length})\n            </h3>\n            \n            <div className=\"flex gap-2\">\n              {successfulFiles.length > 0 && (\n                <button\n                  onClick={handleImportEntries}\n                  className=\"px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm\"\n                >\n                  Import {successfulFiles.length} entries\n                </button>\n              )}\n              \n              <button\n                onClick={clearAllFiles}\n                className=\"px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm\"\n              >\n                Clear all\n              </button>\n            </div>\n          </div>\n\n          <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n            {uploadedFiles.map(uploadedFile => (\n              <div\n                key={uploadedFile.id}\n                className=\"flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\"\n              >\n                <div className=\"flex-shrink-0\">\n                  {uploadedFile.status === 'processing' && (\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600\"></div>\n                  )}\n                  {uploadedFile.status === 'success' && (\n                    <Check className=\"w-5 h-5 text-green-600\" />\n                  )}\n                  {uploadedFile.status === 'error' && (\n                    <AlertCircle className=\"w-5 h-5 text-red-600\" />\n                  )}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center gap-2\">\n                    <FileText className=\"w-4 h-4 text-gray-400\" />\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {uploadedFile.file.name}\n                    </span>\n                  </div>\n                  \n                  {uploadedFile.status === 'success' && uploadedFile.entry && (\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      {uploadedFile.entry.wordCount} words • {uploadedFile.entry.tags.join(', ')}\n                    </p>\n                  )}\n                  \n                  {uploadedFile.status === 'error' && uploadedFile.error && (\n                    <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                      {uploadedFile.error}\n                    </p>\n                  )}\n                </div>\n\n                <button\n                  onClick={() => removeUploadedFile(uploadedFile.id)}\n                  className=\"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                >\n                  <X className=\"w-4 h-4\" />\n                </button>\n              </div>\n            ))}\n          </div>\n\n          {/* Summary */}\n          {uploadedFiles.length > 0 && (\n            <div className=\"text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-3\">\n              {successfulFiles.length} successful, {uploadedFiles.filter(f => f.status === 'error').length} failed\n              {hasErrors && (\n                <span className=\"text-red-600 dark:text-red-400 ml-2\">\n                  • Check error messages above\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAwBe,SAAS,WAAW,EACjC,iBAAiB,EACjB,YAAY,EAAE,EACd,cAAc,EAAE,EAChB,gBAAgB;IAAC;IAAQ;IAAO;IAAQ;IAAQ;CAAQ,EACxC;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,iDAAiD;IACjD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC5C,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;QAE5D,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,MAAM,KAAK,IAAI;YAExB,KAAK;YACL,KAAK;gBACH,uFAAuF;gBACvF,IAAI;oBACF,OAAO,MAAM,KAAK,IAAI;gBACxB,EAAE,OAAM;oBACN,MAAM,IAAI,MAAM;gBAClB;YAEF;gBACE,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,eAAe;QAC7D;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,gBAAgB;QAChB,MAAM,mBAAmC,EAAE;QAE3C,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;YACpC,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,cAAc,OAAO,MAAM;gBACzC,iBAAiB,IAAI,CAAC;oBACpB,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;oBACT;oBACA,SAAS;oBACT,QAAQ;oBACR,OAAO,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC;gBACnD;gBACA;YACF;YAEA,kBAAkB;YAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;YAClE,IAAI,CAAC,cAAc,QAAQ,CAAC,gBAAgB;gBAC1C,iBAAiB,IAAI,CAAC;oBACpB,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;oBACT;oBACA,SAAS;oBACT,QAAQ;oBACR,OAAO,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAAC,OAAO;gBAC7E;gBACA;YACF;YAEA,MAAM,eAA6B;gBACjC,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;gBACT;gBACA,SAAS;gBACT,QAAQ;YACV;YAEA,iBAAiB,IAAI,CAAC;YAEtB,IAAI;gBACF,uBAAuB;gBACvB,MAAM,UAAU,MAAM,mBAAmB;gBAEzC,uBAAuB;gBACvB,MAAM,QAAsB;oBAC1B,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;oBACT,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;oBACtC,SAAS,QAAQ,IAAI;oBACrB,MAAM;wBAAC;qBAAW;oBAClB,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG;oBACjD,WAAW,IAAI;oBACf,WAAW,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;gBAC/E;gBAEA,+CAA+C;gBAC/C,aAAa,OAAO,GAAG;gBACvB,aAAa,MAAM,GAAG;gBACtB,aAAa,KAAK,GAAG;YAEvB,EAAE,OAAO,OAAO;gBACd,aAAa,MAAM,GAAG;gBACtB,aAAa,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChE;QACF;QAEA,iBAAiB,CAAA,OAAQ;mBAAI;mBAAS;aAAiB;QACvD,gBAAgB;IAClB,GAAG;QAAC;QAAa;QAAe;KAAmB;IAEnD,2BAA2B;IAC3B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,aAAa;QACf;QACA,2DAA2D;QAC3D,EAAE,MAAM,CAAC,KAAK,GAAG;IACnB,GAAG;QAAC;KAAa;IAEjB,uBAAuB;IACvB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,oBAAoB,cACvB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,KAAK,KAAK,EACtD,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK;QAEzB,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,uBAAuB;YACvB,kBAAkB,OAAO,CAAC,CAAA;gBACxB,0HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;YAChC;YAEA,uBAAuB;YACvB,kBAAkB;YAElB,uBAAuB;YACvB,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;QAAe;KAAkB;IAErC,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC3D,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IACrE,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAE7D,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,aACE,mDACA,wFACH;QACH,CAAC;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,aAAa,OAAO,EAAE;;kCAErC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAQ,cAAc,IAAI,CAAC;wBAC3B,UAAU;wBACV,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAK/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAE;4CAAoB,cAAc,IAAI,CAAC;;;;;;;kDAC1C,8OAAC;;4CAAE;4CAAoB;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;YAMxC,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;YAKtD,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;oCAC/C,cAAc,MAAM;oCAAC;;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;wCACC,SAAS;wCACT,WAAU;;4CACX;4CACS,gBAAgB,MAAM;4CAAC;;;;;;;kDAInC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAA,6BACjB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,MAAM,KAAK,8BACvB,8OAAC;gDAAI,WAAU;;;;;;4CAEhB,aAAa,MAAM,KAAK,2BACvB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAElB,aAAa,MAAM,KAAK,yBACvB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAI3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEACb,aAAa,IAAI,CAAC,IAAI;;;;;;;;;;;;4CAI1B,aAAa,MAAM,KAAK,aAAa,aAAa,KAAK,kBACtD,8OAAC;gDAAE,WAAU;;oDACV,aAAa,KAAK,CAAC,SAAS;oDAAC;oDAAU,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;4CAIxE,aAAa,MAAM,KAAK,WAAW,aAAa,KAAK,kBACpD,8OAAC;gDAAE,WAAU;0DACV,aAAa,KAAK;;;;;;;;;;;;kDAKzB,8OAAC;wCACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;wCACjD,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAxCV,aAAa,EAAE;;;;;;;;;;oBA+CzB,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,MAAM;4BAAC;4BAAc,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;4BAAC;4BAC5F,2BACC,8OAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;AAUtE", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/SearchComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { JournalEntry, Tag, SearchFilters } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Search, X, Filter, Calendar, Hash, SortAsc, SortDesc } from 'lucide-react';\n\ninterface SearchComponentProps {\n  entries: JournalEntry[];\n  tags: Tag[];\n  onSearchResults: (results: JournalEntry[]) => void;\n  className?: string;\n}\n\ninterface SearchResult extends JournalEntry {\n  matchType: 'title' | 'content' | 'tag';\n  matchText: string;\n  score: number;\n}\n\nexport default function SearchComponent({\n  entries,\n  tags,\n  onSearchResults,\n  className = ''\n}: SearchComponentProps) {\n  const [query, setQuery] = useState('');\n  const [filters, setFilters] = useState<SearchFilters>({\n    sortBy: 'updatedAt',\n    sortOrder: 'desc'\n  });\n  const [showFilters, setShowFilters] = useState(false);\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchHistory, setSearchHistory] = useState<string[]>([]);\n  const [showHistory, setShowHistory] = useState(false);\n  \n  const searchInputRef = useRef<HTMLInputElement>(null);\n  const filtersRef = useRef<HTMLDivElement>(null);\n  const historyRef = useRef<HTMLDivElement>(null);\n\n  // Load search history from localStorage\n  useEffect(() => {\n    const history = localStorage.getItem('journal_search_history');\n    if (history) {\n      setSearchHistory(JSON.parse(history));\n    }\n  }, []);\n\n  // Save search history to localStorage\n  const saveSearchHistory = useCallback((newQuery: string) => {\n    if (!newQuery.trim()) return;\n    \n    const updatedHistory = [\n      newQuery,\n      ...searchHistory.filter(q => q !== newQuery)\n    ].slice(0, 10); // Keep only last 10 searches\n    \n    setSearchHistory(updatedHistory);\n    localStorage.setItem('journal_search_history', JSON.stringify(updatedHistory));\n  }, [searchHistory]);\n\n  // Highlight search terms in text\n  const highlightText = useCallback((text: string, searchQuery: string): string => {\n    if (!searchQuery.trim()) return text;\n    \n    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi');\n    return text.replace(regex, '<mark class=\"bg-yellow-200 dark:bg-yellow-800\">$1</mark>');\n  }, []);\n\n  // Calculate search score\n  const calculateScore = useCallback((entry: JournalEntry, searchQuery: string): number => {\n    let score = 0;\n    const lowerQuery = searchQuery.toLowerCase();\n    const lowerTitle = entry.title.toLowerCase();\n    const lowerContent = entry.content.toLowerCase();\n    \n    // Title matches get higher score\n    if (lowerTitle.includes(lowerQuery)) {\n      score += lowerTitle === lowerQuery ? 100 : 50;\n    }\n    \n    // Content matches\n    const contentMatches = (lowerContent.match(new RegExp(lowerQuery, 'g')) || []).length;\n    score += contentMatches * 10;\n    \n    // Tag matches get high score\n    const tagMatches = entry.tags.filter(tag => \n      tag.toLowerCase().includes(lowerQuery)\n    ).length;\n    score += tagMatches * 30;\n    \n    // Boost recent entries slightly\n    const daysSinceUpdate = (Date.now() - entry.updatedAt.getTime()) / (1000 * 60 * 60 * 24);\n    score += Math.max(0, 10 - daysSinceUpdate);\n    \n    return score;\n  }, []);\n\n  // Perform search\n  const performSearch = useCallback(() => {\n    setIsSearching(true);\n    \n    try {\n      let results: SearchResult[] = [];\n      \n      if (query.trim()) {\n        // Text search\n        results = entries\n          .map(entry => {\n            const score = calculateScore(entry, query);\n            let matchType: 'title' | 'content' | 'tag' = 'content';\n            let matchText = '';\n            \n            // Determine match type and extract match text\n            if (entry.title.toLowerCase().includes(query.toLowerCase())) {\n              matchType = 'title';\n              matchText = entry.title;\n            } else if (entry.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))) {\n              matchType = 'tag';\n              matchText = entry.tags.find(tag => \n                tag.toLowerCase().includes(query.toLowerCase())\n              ) || '';\n            } else {\n              matchType = 'content';\n              // Extract snippet around the match\n              const contentLower = entry.content.toLowerCase();\n              const queryLower = query.toLowerCase();\n              const matchIndex = contentLower.indexOf(queryLower);\n              if (matchIndex !== -1) {\n                const start = Math.max(0, matchIndex - 50);\n                const end = Math.min(entry.content.length, matchIndex + query.length + 50);\n                matchText = entry.content.slice(start, end);\n                if (start > 0) matchText = '...' + matchText;\n                if (end < entry.content.length) matchText = matchText + '...';\n              }\n            }\n            \n            return {\n              ...entry,\n              matchType,\n              matchText,\n              score\n            } as SearchResult;\n          })\n          .filter(result => result.score > 0);\n      } else {\n        // No query, return all entries\n        results = entries.map(entry => ({\n          ...entry,\n          matchType: 'content' as const,\n          matchText: '',\n          score: 0\n        }));\n      }\n      \n      // Apply filters\n      if (filters.tags && filters.tags.length > 0) {\n        results = results.filter(entry =>\n          filters.tags!.every(tag => entry.tags.includes(tag))\n        );\n      }\n      \n      if (filters.dateFrom) {\n        results = results.filter(entry =>\n          entry.createdAt >= filters.dateFrom!\n        );\n      }\n      \n      if (filters.dateTo) {\n        results = results.filter(entry =>\n          entry.createdAt <= filters.dateTo!\n        );\n      }\n      \n      // Sort results\n      results.sort((a, b) => {\n        if (query.trim() && a.score !== b.score) {\n          return b.score - a.score; // Sort by relevance first when searching\n        }\n        \n        const sortBy = filters.sortBy || 'updatedAt';\n        const sortOrder = filters.sortOrder || 'desc';\n        \n        let aValue: any = a[sortBy];\n        let bValue: any = b[sortBy];\n        \n        if (sortBy === 'createdAt' || sortBy === 'updatedAt') {\n          aValue = aValue.getTime();\n          bValue = bValue.getTime();\n        }\n        \n        if (sortOrder === 'desc') {\n          return bValue > aValue ? 1 : -1;\n        } else {\n          return aValue > bValue ? 1 : -1;\n        }\n      });\n      \n      onSearchResults(results);\n      \n      // Save to search history\n      if (query.trim()) {\n        saveSearchHistory(query);\n      }\n      \n    } finally {\n      setIsSearching(false);\n    }\n  }, [query, filters, entries, calculateScore, onSearchResults, saveSearchHistory]);\n\n  // Debounced search\n  useEffect(() => {\n    const timer = setTimeout(performSearch, 300);\n    return () => clearTimeout(timer);\n  }, [performSearch]);\n\n  // Handle filter changes\n  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n  }, []);\n\n  // Clear search\n  const clearSearch = useCallback(() => {\n    setQuery('');\n    setFilters({ sortBy: 'updatedAt', sortOrder: 'desc' });\n    onSearchResults(entries);\n  }, [entries, onSearchResults]);\n\n  // Handle search history selection\n  const selectFromHistory = useCallback((historyQuery: string) => {\n    setQuery(historyQuery);\n    setShowHistory(false);\n  }, []);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {\n        setShowFilters(false);\n      }\n      if (historyRef.current && !historyRef.current.contains(event.target as Node)) {\n        setShowHistory(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Search input */}\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            ref={searchInputRef}\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            onFocus={() => setShowHistory(searchHistory.length > 0 && !query)}\n            placeholder=\"Search entries, tags, or content...\"\n            className=\"w-full pl-10 pr-20 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          \n          <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1\">\n            {query && (\n              <button\n                onClick={clearSearch}\n                className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            )}\n            \n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className={`p-1 transition-colors ${\n                showFilters || filters.tags?.length || filters.dateFrom || filters.dateTo\n                  ? 'text-blue-600 dark:text-blue-400'\n                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'\n              }`}\n            >\n              <Filter className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Search history dropdown */}\n        {showHistory && searchHistory.length > 0 && (\n          <div\n            ref={historyRef}\n            className=\"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto\"\n          >\n            <div className=\"px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700\">\n              Recent searches\n            </div>\n            {searchHistory.map((historyQuery, index) => (\n              <button\n                key={index}\n                onClick={() => selectFromHistory(historyQuery)}\n                className=\"w-full px-3 py-2 text-left text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                {historyQuery}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Filters */}\n      {showFilters && (\n        <div\n          ref={filtersRef}\n          className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 space-y-4\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">Filters</h3>\n            <button\n              onClick={() => setShowFilters(false)}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Tag filter */}\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                Tags\n              </label>\n              <select\n                multiple\n                value={filters.tags || []}\n                onChange={(e) => updateFilter('tags', Array.from(e.target.selectedOptions, option => option.value))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n                size={3}\n              >\n                {tags.map(tag => (\n                  <option key={tag.id} value={tag.name}>\n                    {tag.name} ({tag.usageCount})\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort options */}\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                Sort by\n              </label>\n              <div className=\"flex space-x-2\">\n                <select\n                  value={filters.sortBy || 'updatedAt'}\n                  onChange={(e) => updateFilter('sortBy', e.target.value)}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n                >\n                  <option value=\"updatedAt\">Last Modified</option>\n                  <option value=\"createdAt\">Created Date</option>\n                  <option value=\"title\">Title</option>\n                  <option value=\"wordCount\">Word Count</option>\n                </select>\n                \n                <button\n                  onClick={() => updateFilter('sortOrder', filters.sortOrder === 'desc' ? 'asc' : 'desc')}\n                  className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  {filters.sortOrder === 'desc' ? <SortDesc className=\"w-4 h-4\" /> : <SortAsc className=\"w-4 h-4\" />}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Date range */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                From Date\n              </label>\n              <input\n                type=\"date\"\n                value={filters.dateFrom ? filters.dateFrom.toISOString().split('T')[0] : ''}\n                onChange={(e) => updateFilter('dateFrom', e.target.value ? new Date(e.target.value) : undefined)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                To Date\n              </label>\n              <input\n                type=\"date\"\n                value={filters.dateTo ? filters.dateTo.toISOString().split('T')[0] : ''}\n                onChange={(e) => updateFilter('dateTo', e.target.value ? new Date(e.target.value) : undefined)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Search status */}\n      {isSearching && (\n        <div className=\"flex items-center justify-center py-4\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3\"></div>\n          <span className=\"text-sm text-gray-600 dark:text-gray-400\">Searching...</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;AALA;;;;AAoBe,SAAS,gBAAgB,EACtC,OAAO,EACP,IAAI,EACJ,eAAe,EACf,YAAY,EAAE,EACO;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,SAAS;YACX,iBAAiB,KAAK,KAAK,CAAC;QAC9B;IACF,GAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,MAAM,iBAAiB;YACrB;eACG,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;SACpC,CAAC,KAAK,CAAC,GAAG,KAAK,6BAA6B;QAE7C,iBAAiB;QACjB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE,GAAG;QAAC;KAAc;IAElB,iCAAiC;IACjC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAC/C,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO;QAEhC,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;QACpF,OAAO,KAAK,OAAO,CAAC,OAAO;IAC7B,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAqB;QACvD,IAAI,QAAQ;QACZ,MAAM,aAAa,YAAY,WAAW;QAC1C,MAAM,aAAa,MAAM,KAAK,CAAC,WAAW;QAC1C,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;QAE9C,iCAAiC;QACjC,IAAI,WAAW,QAAQ,CAAC,aAAa;YACnC,SAAS,eAAe,aAAa,MAAM;QAC7C;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,CAAC,aAAa,KAAK,CAAC,IAAI,OAAO,YAAY,SAAS,EAAE,EAAE,MAAM;QACrF,SAAS,iBAAiB;QAE1B,6BAA6B;QAC7B,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAC,CAAA,MACnC,IAAI,WAAW,GAAG,QAAQ,CAAC,aAC3B,MAAM;QACR,SAAS,aAAa;QAEtB,gCAAgC;QAChC,MAAM,kBAAkB,CAAC,KAAK,GAAG,KAAK,MAAM,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QACvF,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK;QAE1B,OAAO;IACT,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,eAAe;QAEf,IAAI;YACF,IAAI,UAA0B,EAAE;YAEhC,IAAI,MAAM,IAAI,IAAI;gBAChB,cAAc;gBACd,UAAU,QACP,GAAG,CAAC,CAAA;oBACH,MAAM,QAAQ,eAAe,OAAO;oBACpC,IAAI,YAAyC;oBAC7C,IAAI,YAAY;oBAEhB,8CAA8C;oBAC9C,IAAI,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,KAAK;wBAC3D,YAAY;wBACZ,YAAY,MAAM,KAAK;oBACzB,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,MAAM;wBAClF,YAAY;wBACZ,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,MAC1B,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,QACzC;oBACP,OAAO;wBACL,YAAY;wBACZ,mCAAmC;wBACnC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;wBAC9C,MAAM,aAAa,MAAM,WAAW;wBACpC,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,eAAe,CAAC,GAAG;4BACrB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa;4BACvC,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC,MAAM,EAAE,aAAa,MAAM,MAAM,GAAG;4BACvE,YAAY,MAAM,OAAO,CAAC,KAAK,CAAC,OAAO;4BACvC,IAAI,QAAQ,GAAG,YAAY,QAAQ;4BACnC,IAAI,MAAM,MAAM,OAAO,CAAC,MAAM,EAAE,YAAY,YAAY;wBAC1D;oBACF;oBAEA,OAAO;wBACL,GAAG,KAAK;wBACR;wBACA;wBACA;oBACF;gBACF,GACC,MAAM,CAAC,CAAA,SAAU,OAAO,KAAK,GAAG;YACrC,OAAO;gBACL,+BAA+B;gBAC/B,UAAU,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;wBAC9B,GAAG,KAAK;wBACR,WAAW;wBACX,WAAW;wBACX,OAAO;oBACT,CAAC;YACH;YAEA,gBAAgB;YAChB,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC3C,UAAU,QAAQ,MAAM,CAAC,CAAA,QACvB,QAAQ,IAAI,CAAE,KAAK,CAAC,CAAA,MAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;YAEnD;YAEA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,UAAU,QAAQ,MAAM,CAAC,CAAA,QACvB,MAAM,SAAS,IAAI,QAAQ,QAAQ;YAEvC;YAEA,IAAI,QAAQ,MAAM,EAAE;gBAClB,UAAU,QAAQ,MAAM,CAAC,CAAA,QACvB,MAAM,SAAS,IAAI,QAAQ,MAAM;YAErC;YAEA,eAAe;YACf,QAAQ,IAAI,CAAC,CAAC,GAAG;gBACf,IAAI,MAAM,IAAI,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;oBACvC,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,yCAAyC;gBACrE;gBAEA,MAAM,SAAS,QAAQ,MAAM,IAAI;gBACjC,MAAM,YAAY,QAAQ,SAAS,IAAI;gBAEvC,IAAI,SAAc,CAAC,CAAC,OAAO;gBAC3B,IAAI,SAAc,CAAC,CAAC,OAAO;gBAE3B,IAAI,WAAW,eAAe,WAAW,aAAa;oBACpD,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,OAAO;gBACzB;gBAEA,IAAI,cAAc,QAAQ;oBACxB,OAAO,SAAS,SAAS,IAAI,CAAC;gBAChC,OAAO;oBACL,OAAO,SAAS,SAAS,IAAI,CAAC;gBAChC;YACF;YAEA,gBAAgB;YAEhB,yBAAyB;YACzB,IAAI,MAAM,IAAI,IAAI;gBAChB,kBAAkB;YACpB;QAEF,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;QAAO;QAAS;QAAS;QAAgB;QAAiB;KAAkB;IAEhF,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAc;IAElB,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,KAA0B;QAC1D,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAC/C,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,SAAS;QACT,WAAW;YAAE,QAAQ;YAAa,WAAW;QAAO;QACpD,gBAAgB;IAClB,GAAG;QAAC;QAAS;KAAgB;IAE7B,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,SAAS;QACT,eAAe;IACjB,GAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5E,eAAe;YACjB;YACA,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5E,eAAe;YACjB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,KAAK;gCACL,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,SAAS,IAAM,eAAe,cAAc,MAAM,GAAG,KAAK,CAAC;gCAC3D,aAAY;gCACZ,WAAU;;;;;;0CAGZ,8OAAC;gCAAI,WAAU;;oCACZ,uBACC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;kDAIjB,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,sBAAsB,EAChC,eAAe,QAAQ,IAAI,EAAE,UAAU,QAAQ,QAAQ,IAAI,QAAQ,MAAM,GACrE,qCACA,8DACJ;kDAEF,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMvB,eAAe,cAAc,MAAM,GAAG,mBACrC,8OAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAA+G;;;;;;4BAG7H,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAET;mCAJI;;;;;;;;;;;;;;;;;YAYd,6BACC,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,8OAAC;wCACC,QAAQ;wCACR,OAAO,QAAQ,IAAI,IAAI,EAAE;wCACzB,UAAU,CAAC,IAAM,aAAa,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;wCACjG,WAAU;wCACV,MAAM;kDAEL,KAAK,GAAG,CAAC,CAAA,oBACR,8OAAC;gDAAoB,OAAO,IAAI,IAAI;;oDACjC,IAAI,IAAI;oDAAC;oDAAG,IAAI,UAAU;oDAAC;;+CADjB,IAAI,EAAE;;;;;;;;;;;;;;;;0CAQzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO,QAAQ,MAAM,IAAI;gDACzB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;gDACtD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAY;;;;;;;;;;;;0DAG5B,8OAAC;gDACC,SAAS,IAAM,aAAa,aAAa,QAAQ,SAAS,KAAK,SAAS,QAAQ;gDAChF,WAAU;0DAET,QAAQ,SAAS,KAAK,uBAAS,8OAAC,iOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;yEAAe,8OAAC,8NAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wCACzE,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI;wCACtF,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wCACrE,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI;wCACpF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;YAQnB,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAA2C;;;;;;;;;;;;;;;;;;AAKrE", "debugId": null}}, {"offset": {"line": 1908, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/Settings.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useTheme, ThemeToggle } from './ThemeProvider';\nimport { UserPreferences } from '@/lib/types';\nimport { Settings as SettingsIcon, Type, Palette, Save, RotateCcw, Zap } from 'lucide-react';\n\ninterface SettingsProps {\n  isOpen: boolean;\n  onClose: () => void;\n  className?: string;\n}\n\nconst FONT_FAMILIES = [\n  { value: 'Inter', label: 'Inter (Default)' },\n  { value: 'Georgia', label: 'Georgia (Serif)' },\n  { value: 'Times New Roman', label: 'Times New Roman' },\n  { value: 'Arial', label: 'Arial (Sans-serif)' },\n  { value: 'Helvetica', label: 'Helvetica' },\n  { value: 'Courier New', label: 'Courier New (Monospace)' },\n  { value: 'Verdana', label: 'Verdana' },\n  { value: 'Trebuchet MS', label: 'Trebuchet MS' }\n];\n\nconst FONT_SIZES = [\n  { value: 12, label: 'Extra Small' },\n  { value: 14, label: 'Small' },\n  { value: 16, label: 'Medium (Default)' },\n  { value: 18, label: 'Large' },\n  { value: 20, label: 'Extra Large' },\n  { value: 24, label: 'Huge' }\n];\n\nconst AUTO_SAVE_INTERVALS = [\n  { value: 1000, label: '1 second' },\n  { value: 2000, label: '2 seconds (Default)' },\n  { value: 5000, label: '5 seconds' },\n  { value: 10000, label: '10 seconds' },\n  { value: 30000, label: '30 seconds' },\n  { value: 60000, label: '1 minute' }\n];\n\nexport default function Settings({ isOpen, onClose, className = '' }: SettingsProps) {\n  const { preferences, updatePreferences } = useTheme();\n  const [tempPreferences, setTempPreferences] = useState<UserPreferences | null>(preferences);\n  const [hasChanges, setHasChanges] = useState(false);\n\n  // Update temp preferences when actual preferences change\n  React.useEffect(() => {\n    if (preferences && !hasChanges) {\n      setTempPreferences(preferences);\n    }\n  }, [preferences, hasChanges]);\n\n  if (!isOpen || !tempPreferences) return null;\n\n  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {\n    setTempPreferences(prev => prev ? { ...prev, [key]: value } : null);\n    setHasChanges(true);\n  };\n\n  const handleSave = () => {\n    if (tempPreferences) {\n      updatePreferences(tempPreferences);\n      setHasChanges(false);\n    }\n  };\n\n  const handleReset = () => {\n    setTempPreferences(preferences);\n    setHasChanges(false);\n  };\n\n  const handleResetToDefaults = () => {\n    const defaultPreferences: UserPreferences = {\n      id: tempPreferences.id,\n      theme: 'system',\n      fontSize: 16,\n      fontFamily: 'Inter',\n      spellCheckEnabled: true,\n      autoSaveInterval: 2000\n    };\n    setTempPreferences(defaultPreferences);\n    setHasChanges(true);\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div className=\"relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-lg shadow-xl\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3\">\n              <SettingsIcon className=\"w-6 h-6 text-gray-600 dark:text-gray-400\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                Settings\n              </h2>\n            </div>\n            \n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 space-y-8 max-h-96 overflow-y-auto\">\n            {/* Theme Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Palette className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Appearance\n                </h3>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Theme\n                </label>\n                <ThemeToggle />\n              </div>\n            </div>\n\n            {/* Typography Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Type className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Typography\n                </h3>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {/* Font Family */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Font Family\n                  </label>\n                  <select\n                    value={tempPreferences.fontFamily}\n                    onChange={(e) => handlePreferenceChange('fontFamily', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {FONT_FAMILIES.map(font => (\n                      <option key={font.value} value={font.value}>\n                        {font.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Font Size */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Font Size\n                  </label>\n                  <select\n                    value={tempPreferences.fontSize}\n                    onChange={(e) => handlePreferenceChange('fontSize', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {FONT_SIZES.map(size => (\n                      <option key={size.value} value={size.value}>\n                        {size.label} ({size.value}px)\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Font Preview */}\n              <div className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Preview:</p>\n                <p \n                  style={{ \n                    fontFamily: tempPreferences.fontFamily,\n                    fontSize: `${tempPreferences.fontSize}px`\n                  }}\n                  className=\"text-gray-900 dark:text-white\"\n                >\n                  The quick brown fox jumps over the lazy dog. This is how your journal entries will look with the selected font settings.\n                </p>\n              </div>\n            </div>\n\n            {/* Editor Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Zap className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Editor\n                </h3>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {/* Auto-save Interval */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Auto-save Interval\n                  </label>\n                  <select\n                    value={tempPreferences.autoSaveInterval}\n                    onChange={(e) => handlePreferenceChange('autoSaveInterval', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {AUTO_SAVE_INTERVALS.map(interval => (\n                      <option key={interval.value} value={interval.value}>\n                        {interval.label}\n                      </option>\n                    ))}\n                  </select>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    How often to automatically save your work while typing\n                  </p>\n                </div>\n\n                {/* Spell Check */}\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Spell Check\n                    </label>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      Enable spell checking while typing\n                    </p>\n                  </div>\n                  <button\n                    onClick={() => handlePreferenceChange('spellCheckEnabled', !tempPreferences.spellCheckEnabled)}\n                    className={`\n                      relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                      ${tempPreferences.spellCheckEnabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                    `}\n                  >\n                    <span\n                      className={`\n                        inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                        ${tempPreferences.spellCheckEnabled ? 'translate-x-6' : 'translate-x-1'}\n                      `}\n                    />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={handleResetToDefaults}\n              className=\"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n            >\n              <RotateCcw className=\"w-4 h-4\" />\n              <span>Reset to Defaults</span>\n            </button>\n            \n            <div className=\"flex space-x-3\">\n              {hasChanges && (\n                <button\n                  onClick={handleReset}\n                  className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n                >\n                  Cancel\n                </button>\n              )}\n              \n              <button\n                onClick={handleSave}\n                disabled={!hasChanges}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                <Save className=\"w-4 h-4\" />\n                <span>Save Changes</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAaA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAS,OAAO;IAAkB;IAC3C;QAAE,OAAO;QAAW,OAAO;IAAkB;IAC7C;QAAE,OAAO;QAAmB,OAAO;IAAkB;IACrD;QAAE,OAAO;QAAS,OAAO;IAAqB;IAC9C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAe,OAAO;IAA0B;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAI,OAAO;IAAc;IAClC;QAAE,OAAO;QAAI,OAAO;IAAQ;IAC5B;QAAE,OAAO;QAAI,OAAO;IAAmB;IACvC;QAAE,OAAO;QAAI,OAAO;IAAQ;IAC5B;QAAE,OAAO;QAAI,OAAO;IAAc;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;CAC5B;AAED,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAM,OAAO;IAAW;IACjC;QAAE,OAAO;QAAM,OAAO;IAAsB;IAC5C;QAAE,OAAO;QAAM,OAAO;IAAY;IAClC;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAO,OAAO;IAAW;CACnC;AAEc,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,EAAiB;IACjF,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yDAAyD;IACzD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,eAAe,CAAC,YAAY;YAC9B,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,IAAI,CAAC,UAAU,CAAC,iBAAiB,OAAO;IAExC,MAAM,yBAAyB,CAAC,KAA4B;QAC1D,mBAAmB,CAAA,OAAQ,OAAO;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,IAAI;QAC9D,cAAc;IAChB;IAEA,MAAM,aAAa;QACjB,IAAI,iBAAiB;YACnB,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,mBAAmB;QACnB,cAAc;IAChB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,qBAAsC;YAC1C,IAAI,gBAAgB,EAAE;YACtB,OAAO;YACP,UAAU;YACV,YAAY;YACZ,mBAAmB;YACnB,kBAAkB;QACpB;QACA,mBAAmB;QACnB,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;sDACxB,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;;;;;;;8CAKtE,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA6D;;;;;;8DAG9E,8OAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,8OAAC;4DACC,OAAO,gBAAgB,UAAU;4DACjC,UAAU,CAAC,IAAM,uBAAuB,cAAc,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAU;sEAET,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;oEAAwB,OAAO,KAAK,KAAK;8EACvC,KAAK,KAAK;mEADA,KAAK,KAAK;;;;;;;;;;;;;;;;8DAQ7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,8OAAC;4DACC,OAAO,gBAAgB,QAAQ;4DAC/B,UAAU,CAAC,IAAM,uBAAuB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;4DAC3E,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;oEAAwB,OAAO,KAAK,KAAK;;wEACvC,KAAK,KAAK;wEAAC;wEAAG,KAAK,KAAK;wEAAC;;mEADf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sDAS/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAC7D,8OAAC;oDACC,OAAO;wDACL,YAAY,gBAAgB,UAAU;wDACtC,UAAU,GAAG,gBAAgB,QAAQ,CAAC,EAAE,CAAC;oDAC3C;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,8OAAC;4DACC,OAAO,gBAAgB,gBAAgB;4DACvC,UAAU,CAAC,IAAM,uBAAuB,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;4DACnF,WAAU;sEAET,oBAAoB,GAAG,CAAC,CAAA,yBACvB,8OAAC;oEAA4B,OAAO,SAAS,KAAK;8EAC/C,SAAS,KAAK;mEADJ,SAAS,KAAK;;;;;;;;;;sEAK/B,8OAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;8DAM1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAuD;;;;;;8EAGxE,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;sEAI1D,8OAAC;4DACC,SAAS,IAAM,uBAAuB,qBAAqB,CAAC,gBAAgB,iBAAiB;4DAC7F,WAAW,CAAC;;sBAEV,EAAE,gBAAgB,iBAAiB,GAAG,gBAAgB,+BAA+B;oBACvF,CAAC;sEAED,cAAA,8OAAC;gEACC,WAAW,CAAC;;wBAEV,EAAE,gBAAgB,iBAAiB,GAAG,kBAAkB,gBAAgB;sBAC1E,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAKH,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 2577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/JournalApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { JournalEntry, Tag, UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { useTheme } from './ThemeProvider';\nimport TextEditor from './TextEditor';\nimport TagManager from './TagManager';\nimport FileUpload from './FileUpload';\nimport SearchComponent from './SearchComponent';\nimport Settings from './Settings';\nimport { \n  PlusCircle, \n  Search, \n  Upload, \n  Settings as SettingsIcon, \n  BookOpen, \n  Menu,\n  X,\n  Edit3,\n  Calendar,\n  Hash\n} from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ntype ViewMode = 'list' | 'editor' | 'search' | 'upload';\n\nexport default function JournalApp() {\n  const { preferences } = useTheme();\n  const [entries, setEntries] = useState<JournalEntry[]>([]);\n  const [tags, setTags] = useState<Tag[]>([]);\n  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);\n  const [viewMode, setViewMode] = useState<ViewMode>('list');\n  const [searchResults, setSearchResults] = useState<JournalEntry[]>([]);\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const loadData = () => {\n      try {\n        const savedEntries = localStorageManager.getEntries();\n        const savedTags = localStorageManager.getTags();\n\n        // Ensure we always have arrays\n        const entriesArray = Array.isArray(savedEntries) ? savedEntries : [];\n        const tagsArray = Array.isArray(savedTags) ? savedTags : [];\n\n        setEntries(entriesArray);\n        setTags(tagsArray);\n        setSearchResults(entriesArray);\n      } catch (error) {\n        console.error('Failed to load data:', error);\n        // Set empty arrays as fallback\n        setEntries([]);\n        setTags([]);\n        setSearchResults([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Create new entry\n  const createNewEntry = useCallback(() => {\n    const newEntry: JournalEntry = {\n      id: uuidv4(),\n      title: '',\n      content: '',\n      tags: [],\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      wordCount: 0\n    };\n    \n    setCurrentEntry(newEntry);\n    setViewMode('editor');\n    setShowMobileMenu(false);\n  }, []);\n\n  // Save entry\n  const handleSaveEntry = useCallback((entry: JournalEntry) => {\n    try {\n      // Save to localStorage\n      localStorageManager.saveEntry(entry);\n      \n      // Update state\n      setEntries(prev => {\n        const existingIndex = prev.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          const updated = [...prev];\n          updated[existingIndex] = entry;\n          return updated;\n        } else {\n          return [entry, ...prev];\n        }\n      });\n      \n      // Update search results if needed\n      setSearchResults(prev => {\n        const existingIndex = prev.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          const updated = [...prev];\n          updated[existingIndex] = entry;\n          return updated;\n        } else {\n          return [entry, ...prev];\n        }\n      });\n      \n      setCurrentEntry(entry);\n    } catch (error) {\n      console.error('Failed to save entry:', error);\n    }\n  }, []);\n\n  // Delete entry\n  const handleDeleteEntry = useCallback((entryId: string) => {\n    try {\n      localStorageManager.deleteEntry(entryId);\n      setEntries(prev => prev.filter(e => e.id !== entryId));\n      setSearchResults(prev => prev.filter(e => e.id !== entryId));\n      \n      if (currentEntry?.id === entryId) {\n        setCurrentEntry(null);\n        setViewMode('list');\n      }\n    } catch (error) {\n      console.error('Failed to delete entry:', error);\n    }\n  }, [currentEntry]);\n\n  // Create new tag\n  const handleCreateTag = useCallback((tag: Tag) => {\n    setTags(prev => [...prev, tag]);\n  }, []);\n\n  // Handle file uploads\n  const handleEntriesImported = useCallback((importedEntries: JournalEntry[]) => {\n    setEntries(prev => [...importedEntries, ...prev]);\n    setSearchResults(prev => [...importedEntries, ...prev]);\n    setViewMode('list');\n  }, []);\n\n  // Edit entry\n  const editEntry = useCallback((entry: JournalEntry) => {\n    setCurrentEntry(entry);\n    setViewMode('editor');\n    setShowMobileMenu(false);\n  }, []);\n\n  // Format date for display\n  const formatDate = (date: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    \n    if (days === 0) return 'Today';\n    if (days === 1) return 'Yesterday';\n    if (days < 7) return `${days} days ago`;\n    \n    return date.toLocaleDateString();\n  };\n\n  // Mobile navigation\n  const MobileNav = () => (\n    <div className=\"md:hidden\">\n      <button\n        onClick={() => setShowMobileMenu(!showMobileMenu)}\n        className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n      >\n        {showMobileMenu ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n      </button>\n      \n      {showMobileMenu && (\n        <div className=\"absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-20\">\n          <div className=\"p-4 space-y-2\">\n            <button\n              onClick={() => { setViewMode('list'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <BookOpen className=\"w-5 h-5\" />\n              <span>All Entries</span>\n            </button>\n            \n            <button\n              onClick={() => { setViewMode('search'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <Search className=\"w-5 h-5\" />\n              <span>Search</span>\n            </button>\n            \n            <button\n              onClick={() => { setViewMode('upload'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <Upload className=\"w-5 h-5\" />\n              <span>Import</span>\n            </button>\n            \n            <button\n              onClick={createNewEntry}\n              className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors\"\n            >\n              <PlusCircle className=\"w-5 h-5\" />\n              <span>New Entry</span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white\">\n      {/* Header */}\n      <header className=\"sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                AllJournal\n              </h1>\n              <MobileNav />\n            </div>\n            \n            {/* Desktop navigation */}\n            <nav className=\"hidden md:flex items-center space-x-4\">\n              <button\n                onClick={() => setViewMode('list')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <BookOpen className=\"w-4 h-4\" />\n                <span>Entries</span>\n              </button>\n              \n              <button\n                onClick={() => setViewMode('search')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <Search className=\"w-4 h-4\" />\n                <span>Search</span>\n              </button>\n              \n              <button\n                onClick={() => setViewMode('upload')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <Upload className=\"w-4 h-4\" />\n                <span>Import</span>\n              </button>\n            </nav>\n            \n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={createNewEntry}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                <PlusCircle className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">New Entry</span>\n              </button>\n              \n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n              >\n                <SettingsIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {viewMode === 'editor' && currentEntry && preferences && (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden\">\n              <TextEditor\n                entry={currentEntry}\n                onSave={handleSaveEntry}\n                preferences={preferences}\n              />\n              \n              {/* Tag manager */}\n              <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n                <TagManager\n                  tags={tags}\n                  selectedTags={currentEntry.tags}\n                  onTagsChange={(newTags) => setCurrentEntry(prev => prev ? { ...prev, tags: newTags } : null)}\n                  onCreateTag={handleCreateTag}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {viewMode === 'list' && (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Your Journal Entries\n              </h2>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {entries.length} {entries.length === 1 ? 'entry' : 'entries'}\n              </span>\n            </div>\n            \n            {entries.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <BookOpen className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  No entries yet\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                  Start your journaling journey by creating your first entry.\n                </p>\n                <button\n                  onClick={createNewEntry}\n                  className=\"inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n                >\n                  <PlusCircle className=\"w-4 h-4\" />\n                  <span>Create First Entry</span>\n                </button>\n              </div>\n            ) : (\n              <div className=\"grid gap-4\">\n                {(searchResults || []).map(entry => (\n                  <div\n                    key={entry.id}\n                    className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer\"\n                    onClick={() => editEntry(entry)}\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white truncate\">\n                        {entry.title || 'Untitled'}\n                      </h3>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          editEntry(entry);\n                        }}\n                        className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n                      >\n                        <Edit3 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                      {entry.content.slice(0, 200)}\n                      {entry.content.length > 200 && '...'}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-4 h-4\" />\n                          <span>{formatDate(entry.updatedAt)}</span>\n                        </div>\n                        <span>{entry.wordCount} words</span>\n                      </div>\n                      \n                      {entry.tags.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Hash className=\"w-3 h-3\" />\n                          <span>{entry.tags.slice(0, 2).join(', ')}</span>\n                          {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {viewMode === 'search' && (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Search Entries\n            </h2>\n            \n            <SearchComponent\n              entries={entries}\n              tags={tags}\n              onSearchResults={setSearchResults}\n            />\n            \n            <div className=\"grid gap-4\">\n              {(searchResults || []).map(entry => (\n                <div\n                  key={entry.id}\n                  className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer\"\n                  onClick={() => editEntry(entry)}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white truncate\">\n                      {entry.title || 'Untitled'}\n                    </h3>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        editEntry(entry);\n                      }}\n                      className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n                    >\n                      <Edit3 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                    {entry.content.slice(0, 200)}\n                    {entry.content.length > 200 && '...'}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span>{formatDate(entry.updatedAt)}</span>\n                      </div>\n                      <span>{entry.wordCount} words</span>\n                    </div>\n                    \n                    {entry.tags.length > 0 && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Hash className=\"w-3 h-3\" />\n                        <span>{entry.tags.slice(0, 2).join(', ')}</span>\n                        {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {viewMode === 'upload' && (\n          <div className=\"max-w-2xl mx-auto\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n              Import Writings\n            </h2>\n            \n            <FileUpload onEntriesImported={handleEntriesImported} />\n          </div>\n        )}\n      </main>\n\n      {/* Settings modal */}\n      <Settings\n        isOpen={showSettings}\n        onClose={() => setShowSettings(false)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAvBA;;;;;;;;;;;;AA2Be,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,MAAM,eAAe,0HAAA,CAAA,sBAAmB,CAAC,UAAU;gBACnD,MAAM,YAAY,0HAAA,CAAA,sBAAmB,CAAC,OAAO;gBAE7C,+BAA+B;gBAC/B,MAAM,eAAe,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;gBACpE,MAAM,YAAY,MAAM,OAAO,CAAC,aAAa,YAAY,EAAE;gBAE3D,WAAW;gBACX,QAAQ;gBACR,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,+BAA+B;gBAC/B,WAAW,EAAE;gBACb,QAAQ,EAAE;gBACV,iBAAiB,EAAE;YACrB,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,MAAM,WAAyB;YAC7B,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;YACT,OAAO;YACP,SAAS;YACT,MAAM,EAAE;YACR,WAAW,IAAI;YACf,WAAW,IAAI;YACf,WAAW;QACb;QAEA,gBAAgB;QAChB,YAAY;QACZ,kBAAkB;IACpB,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI;YACF,uBAAuB;YACvB,0HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;YAE9B,eAAe;YACf,WAAW,CAAA;gBACT,MAAM,gBAAgB,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;gBAC3D,IAAI,iBAAiB,GAAG;oBACtB,MAAM,UAAU;2BAAI;qBAAK;oBACzB,OAAO,CAAC,cAAc,GAAG;oBACzB,OAAO;gBACT,OAAO;oBACL,OAAO;wBAAC;2BAAU;qBAAK;gBACzB;YACF;YAEA,kCAAkC;YAClC,iBAAiB,CAAA;gBACf,MAAM,gBAAgB,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;gBAC3D,IAAI,iBAAiB,GAAG;oBACtB,MAAM,UAAU;2BAAI;qBAAK;oBACzB,OAAO,CAAC,cAAc,GAAG;oBACzB,OAAO;gBACT,OAAO;oBACL,OAAO;wBAAC;2BAAU;qBAAK;gBACzB;YACF;YAEA,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,IAAI;YACF,0HAAA,CAAA,sBAAmB,CAAC,WAAW,CAAC;YAChC,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC7C,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEnD,IAAI,cAAc,OAAO,SAAS;gBAChC,gBAAgB;gBAChB,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,GAAG;QAAC;KAAa;IAEjB,iBAAiB;IACjB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,QAAQ,CAAA,OAAQ;mBAAI;gBAAM;aAAI;IAChC,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,WAAW,CAAA,OAAQ;mBAAI;mBAAoB;aAAK;QAChD,iBAAiB,CAAA,OAAQ;mBAAI;mBAAoB;aAAK;QACtD,YAAY;IACd,GAAG,EAAE;IAEL,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,gBAAgB;QAChB,YAAY;QACZ,kBAAkB;IACpB,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QACzC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;QAEnD,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,SAAS,CAAC;QAEvC,OAAO,KAAK,kBAAkB;IAChC;IAEA,oBAAoB;IACpB,MAAM,YAAY,kBAChB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,SAAS,IAAM,kBAAkB,CAAC;oBAClC,WAAU;8BAET,+BAAiB,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;gBAG/D,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAS,kBAAkB;gCAAQ;gCAChE,WAAW,CAAC,0EAA0E,EACpF,aAAa,SAAS,kEAAkE,6EACxF;;kDAEF,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAW,kBAAkB;gCAAQ;gCAClE,WAAW,CAAC,0EAA0E,EACpF,aAAa,WAAW,kEAAkE,6EAC1F;;kDAEF,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAW,kBAAkB;gCAAQ;gCAClE,WAAW,CAAC,0EAA0E,EACpF,aAAa,WAAW,kEAAkE,6EAC1F;;kDAEF,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAGhE,8OAAC;;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,SAAS,kEAAkE,6EACxF;;0DAEF,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,WAAW,kEAAkE,6EAC1F;;0DAEF,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,WAAW,kEAAkE,6EAC1F;;0DAEF,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAGrC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAK,WAAU;;oBACb,aAAa,YAAY,gBAAgB,6BACxC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,UAAU;oCACT,OAAO;oCACP,QAAQ;oCACR,aAAa;;;;;;8CAIf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;wCACT,MAAM;wCACN,cAAc,aAAa,IAAI;wCAC/B,cAAc,CAAC,UAAY,gBAAgB,CAAA,OAAQ,OAAO;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAQ,IAAI;wCACvF,aAAa;;;;;;;;;;;;;;;;;;;;;;oBAOtB,aAAa,wBACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,8OAAC;wCAAK,WAAU;;4CACb,QAAQ,MAAM;4CAAC;4CAAE,QAAQ,MAAM,KAAK,IAAI,UAAU;;;;;;;;;;;;;4BAItD,QAAQ,MAAM,KAAK,kBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;qDAIV,8OAAC;gCAAI,WAAU;0CACZ,CAAC,iBAAiB,EAAE,EAAE,GAAG,CAAC,CAAA,sBACzB,8OAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK,IAAI;;;;;;kEAElB,8OAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,UAAU;wDACZ;wDACA,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;oDACvB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;kFAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;0EAEnC,8OAAC;;oEAAM,MAAM,SAAS;oEAAC;;;;;;;;;;;;;oDAGxB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;4DAClC,MAAM,IAAI,CAAC,MAAM,GAAG,mBAAK,8OAAC;;oEAAK;oEAAE,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;uCArCvD,MAAM,EAAE;;;;;;;;;;;;;;;;oBAgDxB,aAAa,0BACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAIjE,8OAAC,qIAAA,CAAA,UAAe;gCACd,SAAS;gCACT,MAAM;gCACN,iBAAiB;;;;;;0CAGnB,8OAAC;gCAAI,WAAU;0CACZ,CAAC,iBAAiB,EAAE,EAAE,GAAG,CAAC,CAAA,sBACzB,8OAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK,IAAI;;;;;;kEAElB,8OAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,UAAU;wDACZ;wDACA,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIrB,8OAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;oDACvB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;kFAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;0EAEnC,8OAAC;;oEAAM,MAAM,SAAS;oEAAC;;;;;;;;;;;;;oDAGxB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;4DAClC,MAAM,IAAI,CAAC,MAAM,GAAG,mBAAK,8OAAC;;oEAAK;oEAAE,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;uCArCvD,MAAM,EAAE;;;;;;;;;;;;;;;;oBA+CtB,aAAa,0BACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAItE,8OAAC,gIAAA,CAAA,UAAU;gCAAC,mBAAmB;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC,8HAAA,CAAA,UAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAIvC", "debugId": null}}]}