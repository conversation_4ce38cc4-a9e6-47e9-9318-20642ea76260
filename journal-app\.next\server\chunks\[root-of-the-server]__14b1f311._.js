module.exports = {

"[project]/.next-internal/server/app/api/entries/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/better-sqlite3 [external] (better-sqlite3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("better-sqlite3", () => require("better-sqlite3"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
class JournalDatabase {
    db;
    constructor(dbPath = 'journal.db'){
        this.db = new __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__["default"](dbPath);
        this.initializeTables();
    }
    initializeTables() {
        // Create journal entries table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        tags TEXT NOT NULL DEFAULT '[]',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        attachments TEXT,
        word_count INTEGER DEFAULT 0
      )
    `);
        // Create tags table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL,
        usage_count INTEGER DEFAULT 0
      )
    `);
        // Create user preferences table
        this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id TEXT PRIMARY KEY,
        theme TEXT DEFAULT 'system',
        font_size INTEGER DEFAULT 16,
        font_family TEXT DEFAULT 'Inter',
        spell_check_enabled INTEGER DEFAULT 1,
        auto_save_interval INTEGER DEFAULT 2000,
        last_sync_at TEXT
      )
    `);
        // Create indexes for better performance
        this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_entries_created_at ON journal_entries(created_at);
      CREATE INDEX IF NOT EXISTS idx_entries_updated_at ON journal_entries(updated_at);
      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
    `);
        // Insert default preferences if none exist
        const prefsCount = this.db.prepare('SELECT COUNT(*) as count FROM user_preferences').get();
        if (prefsCount.count === 0) {
            this.createDefaultPreferences();
        }
    }
    createDefaultPreferences() {
        const defaultPrefs = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(),
            theme: 'system',
            font_size: 16,
            font_family: 'Inter',
            spell_check_enabled: 1,
            auto_save_interval: 2000,
            last_sync_at: null
        };
        this.db.prepare(`
      INSERT INTO user_preferences (id, theme, font_size, font_family, spell_check_enabled, auto_save_interval, last_sync_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(defaultPrefs.id, defaultPrefs.theme, defaultPrefs.font_size, defaultPrefs.font_family, defaultPrefs.spell_check_enabled, defaultPrefs.auto_save_interval, defaultPrefs.last_sync_at);
    }
    // Journal Entry operations
    createEntry(entry) {
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const now = new Date().toISOString();
        const wordCount = this.countWords(entry.content);
        const newEntry = {
            id,
            ...entry,
            createdAt: new Date(now),
            updatedAt: new Date(now),
            wordCount
        };
        this.db.prepare(`
      INSERT INTO journal_entries (id, title, content, tags, created_at, updated_at, attachments, word_count)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(id, entry.title, entry.content, JSON.stringify(entry.tags || []), now, now, JSON.stringify(entry.attachments || []), wordCount);
        // Update tag usage counts
        this.updateTagUsage(entry.tags || []);
        return newEntry;
    }
    updateEntry(id, updates) {
        const existing = this.getEntry(id);
        if (!existing) return null;
        const now = new Date().toISOString();
        const wordCount = updates.content ? this.countWords(updates.content) : existing.wordCount;
        const updatedEntry = {
            ...existing,
            ...updates,
            updatedAt: new Date(now),
            wordCount
        };
        this.db.prepare(`
      UPDATE journal_entries 
      SET title = ?, content = ?, tags = ?, updated_at = ?, attachments = ?, word_count = ?
      WHERE id = ?
    `).run(updatedEntry.title, updatedEntry.content, JSON.stringify(updatedEntry.tags), now, JSON.stringify(updatedEntry.attachments || []), wordCount, id);
        // Update tag usage counts
        if (updates.tags) {
            this.updateTagUsage(updates.tags);
        }
        return updatedEntry;
    }
    getEntry(id) {
        const row = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(id);
        return row ? this.rowToEntry(row) : null;
    }
    getAllEntries(limit, offset) {
        let query = 'SELECT * FROM journal_entries ORDER BY updated_at DESC';
        if (limit) {
            query += ` LIMIT ${limit}`;
            if (offset) {
                query += ` OFFSET ${offset}`;
            }
        }
        const rows = this.db.prepare(query).all();
        return rows.map((row)=>this.rowToEntry(row));
    }
    deleteEntry(id) {
        const result = this.db.prepare('DELETE FROM journal_entries WHERE id = ?').run(id);
        return result.changes > 0;
    }
    // Tag operations
    createTag(name, color) {
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const now = new Date().toISOString();
        const newTag = {
            id,
            name,
            color,
            createdAt: new Date(now),
            usageCount: 0
        };
        this.db.prepare(`
      INSERT INTO tags (id, name, color, created_at, usage_count)
      VALUES (?, ?, ?, ?, ?)
    `).run(id, name, color, now, 0);
        return newTag;
    }
    getAllTags() {
        const rows = this.db.prepare('SELECT * FROM tags ORDER BY usage_count DESC, name ASC').all();
        return rows.map((row)=>this.rowToTag(row));
    }
    // User preferences operations
    getPreferences() {
        const row = this.db.prepare('SELECT * FROM user_preferences LIMIT 1').get();
        return this.rowToPreferences(row);
    }
    updatePreferences(updates) {
        const existing = this.getPreferences();
        const updated = {
            ...existing,
            ...updates
        };
        this.db.prepare(`
      UPDATE user_preferences 
      SET theme = ?, font_size = ?, font_family = ?, spell_check_enabled = ?, auto_save_interval = ?
      WHERE id = ?
    `).run(updated.theme, updated.fontSize, updated.fontFamily, updated.spellCheckEnabled ? 1 : 0, updated.autoSaveInterval, existing.id);
        return updated;
    }
    // Search operations
    searchEntries(query, tags) {
        let sql = `
      SELECT * FROM journal_entries 
      WHERE (title LIKE ? OR content LIKE ?)
    `;
        const params = [
            `%${query}%`,
            `%${query}%`
        ];
        if (tags && tags.length > 0) {
            const tagConditions = tags.map(()=>'tags LIKE ?').join(' AND ');
            sql += ` AND (${tagConditions})`;
            tags.forEach((tag)=>params.push(`%"${tag}"%`));
        }
        sql += ' ORDER BY updated_at DESC';
        const rows = this.db.prepare(sql).all(...params);
        return rows.map((row)=>this.rowToEntry(row));
    }
    // Helper methods
    countWords(text) {
        return text.trim().split(/\s+/).filter((word)=>word.length > 0).length;
    }
    updateTagUsage(tags) {
        tags.forEach((tagName)=>{
            this.db.prepare(`
        INSERT INTO tags (id, name, created_at, usage_count) 
        VALUES (?, ?, ?, 1)
        ON CONFLICT(name) DO UPDATE SET usage_count = usage_count + 1
      `).run((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])(), tagName, new Date().toISOString());
        });
    }
    rowToEntry(row) {
        return {
            id: row.id,
            title: row.title,
            content: row.content,
            tags: JSON.parse(row.tags),
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
            attachments: row.attachments ? JSON.parse(row.attachments) : undefined,
            wordCount: row.word_count
        };
    }
    rowToTag(row) {
        return {
            id: row.id,
            name: row.name,
            color: row.color || undefined,
            createdAt: new Date(row.created_at),
            usageCount: row.usage_count
        };
    }
    rowToPreferences(row) {
        return {
            id: row.id,
            theme: row.theme,
            fontSize: row.font_size,
            fontFamily: row.font_family,
            spellCheckEnabled: row.spell_check_enabled === 1,
            autoSaveInterval: row.auto_save_interval,
            lastSyncAt: row.last_sync_at ? new Date(row.last_sync_at) : undefined
        };
    }
    close() {
        this.db.close();
    }
}
const __TURBOPACK__default__export__ = JournalDatabase;
}}),
"[project]/src/app/api/entries/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Initialize database (in production, this would be a singleton)
let db;
function getDatabase() {
    if (!db) {
        db = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]();
    }
    return db;
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '20');
        const search = searchParams.get('search');
        const tags = searchParams.get('tags')?.split(',').filter(Boolean);
        const database = getDatabase();
        let entries;
        if (search || tags) {
            // Search entries
            entries = database.searchEntries(search || '', tags);
        } else {
            // Get all entries with pagination
            const offset = (page - 1) * limit;
            entries = database.getAllEntries(limit, offset);
        }
        // Get total count for pagination
        const allEntries = database.getAllEntries();
        const total = allEntries.length;
        const response = {
            data: entries,
            total,
            page,
            limit,
            hasMore: page * limit < total
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: response
        });
    } catch (error) {
        console.error('Error fetching entries:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch entries'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { title, content, tags = [] } = body;
        if (!title && !content) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Title or content is required'
            }, {
                status: 400
            });
        }
        const database = getDatabase();
        const newEntry = database.createEntry({
            title: title || 'Untitled',
            content: content || '',
            tags: Array.isArray(tags) ? tags : []
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: newEntry
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating entry:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to create entry'
        }, {
            status: 500
        });
    }
}
async function PUT(request) {
    try {
        const body = await request.json();
        const { entries } = body;
        if (!Array.isArray(entries)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Entries must be an array'
            }, {
                status: 400
            });
        }
        const database = getDatabase();
        const updatedEntries = [];
        for (const entryData of entries){
            try {
                if (entryData.id) {
                    // Update existing entry
                    const updated = database.updateEntry(entryData.id, entryData);
                    if (updated) {
                        updatedEntries.push(updated);
                    }
                } else {
                    // Create new entry
                    const created = database.createEntry(entryData);
                    updatedEntries.push(created);
                }
            } catch (entryError) {
                console.error('Error processing entry:', entryError);
            // Continue with other entries
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: updatedEntries
        });
    } catch (error) {
        console.error('Error bulk updating entries:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to update entries'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__14b1f311._.js.map