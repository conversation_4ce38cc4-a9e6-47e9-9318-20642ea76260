'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { JournalEntry, Tag, UserPreferences } from '@/lib/types';
import { localStorageManager } from '@/lib/localStorage';
import { useTheme } from './ThemeProvider';
import TextEditor from './TextEditor';
import TagManager from './TagManager';
import FileUpload from './FileUpload';
import SearchComponent from './SearchComponent';
import Settings from './Settings';
import { 
  PlusCircle, 
  Search, 
  Upload, 
  Settings as SettingsIcon, 
  BookOpen, 
  Menu,
  X,
  Edit3,
  Calendar,
  Hash
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

type ViewMode = 'list' | 'editor' | 'search' | 'upload';

export default function JournalApp() {
  const { preferences } = useTheme();
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [searchResults, setSearchResults] = useState<JournalEntry[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load data from localStorage on mount
  useEffect(() => {
    const loadData = () => {
      try {
        const savedEntries = localStorageManager.getEntries();
        const savedTags = localStorageManager.getTags();
        
        setEntries(savedEntries);
        setTags(savedTags);
        setSearchResults(savedEntries);
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Create new entry
  const createNewEntry = useCallback(() => {
    const newEntry: JournalEntry = {
      id: uuidv4(),
      title: '',
      content: '',
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      wordCount: 0
    };
    
    setCurrentEntry(newEntry);
    setViewMode('editor');
    setShowMobileMenu(false);
  }, []);

  // Save entry
  const handleSaveEntry = useCallback((entry: JournalEntry) => {
    try {
      // Save to localStorage
      localStorageManager.saveEntry(entry);
      
      // Update state
      setEntries(prev => {
        const existingIndex = prev.findIndex(e => e.id === entry.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = entry;
          return updated;
        } else {
          return [entry, ...prev];
        }
      });
      
      // Update search results if needed
      setSearchResults(prev => {
        const existingIndex = prev.findIndex(e => e.id === entry.id);
        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = entry;
          return updated;
        } else {
          return [entry, ...prev];
        }
      });
      
      setCurrentEntry(entry);
    } catch (error) {
      console.error('Failed to save entry:', error);
    }
  }, []);

  // Delete entry
  const handleDeleteEntry = useCallback((entryId: string) => {
    try {
      localStorageManager.deleteEntry(entryId);
      setEntries(prev => prev.filter(e => e.id !== entryId));
      setSearchResults(prev => prev.filter(e => e.id !== entryId));
      
      if (currentEntry?.id === entryId) {
        setCurrentEntry(null);
        setViewMode('list');
      }
    } catch (error) {
      console.error('Failed to delete entry:', error);
    }
  }, [currentEntry]);

  // Create new tag
  const handleCreateTag = useCallback((tag: Tag) => {
    setTags(prev => [...prev, tag]);
  }, []);

  // Handle file uploads
  const handleEntriesImported = useCallback((importedEntries: JournalEntry[]) => {
    setEntries(prev => [...importedEntries, ...prev]);
    setSearchResults(prev => [...importedEntries, ...prev]);
    setViewMode('list');
  }, []);

  // Edit entry
  const editEntry = useCallback((entry: JournalEntry) => {
    setCurrentEntry(entry);
    setViewMode('editor');
    setShowMobileMenu(false);
  }, []);

  // Format date for display
  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 7) return `${days} days ago`;
    
    return date.toLocaleDateString();
  };

  // Mobile navigation
  const MobileNav = () => (
    <div className="md:hidden">
      <button
        onClick={() => setShowMobileMenu(!showMobileMenu)}
        className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
      >
        {showMobileMenu ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>
      
      {showMobileMenu && (
        <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-20">
          <div className="p-4 space-y-2">
            <button
              onClick={() => { setViewMode('list'); setShowMobileMenu(false); }}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
                viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <BookOpen className="w-5 h-5" />
              <span>All Entries</span>
            </button>
            
            <button
              onClick={() => { setViewMode('search'); setShowMobileMenu(false); }}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
                viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Search className="w-5 h-5" />
              <span>Search</span>
            </button>
            
            <button
              onClick={() => { setViewMode('upload'); setShowMobileMenu(false); }}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${
                viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Upload className="w-5 h-5" />
              <span>Import</span>
            </button>
            
            <button
              onClick={createNewEntry}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors"
            >
              <PlusCircle className="w-5 h-5" />
              <span>New Entry</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                AllJournal
              </h1>
              <MobileNav />
            </div>
            
            {/* Desktop navigation */}
            <nav className="hidden md:flex items-center space-x-4">
              <button
                onClick={() => setViewMode('list')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <BookOpen className="w-4 h-4" />
                <span>Entries</span>
              </button>
              
              <button
                onClick={() => setViewMode('search')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Search className="w-4 h-4" />
                <span>Search</span>
              </button>
              
              <button
                onClick={() => setViewMode('upload')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                  viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Upload className="w-4 h-4" />
                <span>Import</span>
              </button>
            </nav>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={createNewEntry}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <PlusCircle className="w-4 h-4" />
                <span className="hidden sm:inline">New Entry</span>
              </button>
              
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <SettingsIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {viewMode === 'editor' && currentEntry && preferences && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <TextEditor
                entry={currentEntry}
                onSave={handleSaveEntry}
                preferences={preferences}
              />
              
              {/* Tag manager */}
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <TagManager
                  tags={tags}
                  selectedTags={currentEntry.tags}
                  onTagsChange={(newTags) => setCurrentEntry(prev => prev ? { ...prev, tags: newTags } : null)}
                  onCreateTag={handleCreateTag}
                />
              </div>
            </div>
          </div>
        )}

        {viewMode === 'list' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Your Journal Entries
              </h2>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {entries.length} {entries.length === 1 ? 'entry' : 'entries'}
              </span>
            </div>
            
            {entries.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No entries yet
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Start your journaling journey by creating your first entry.
                </p>
                <button
                  onClick={createNewEntry}
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>Create First Entry</span>
                </button>
              </div>
            ) : (
              <div className="grid gap-4">
                {searchResults.map(entry => (
                  <div
                    key={entry.id}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => editEntry(entry)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {entry.title || 'Untitled'}
                      </h3>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          editEntry(entry);
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {entry.content.slice(0, 200)}
                      {entry.content.length > 200 && '...'}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(entry.updatedAt)}</span>
                        </div>
                        <span>{entry.wordCount} words</span>
                      </div>
                      
                      {entry.tags.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <Hash className="w-3 h-3" />
                          <span>{entry.tags.slice(0, 2).join(', ')}</span>
                          {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {viewMode === 'search' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Search Entries
            </h2>
            
            <SearchComponent
              entries={entries}
              tags={tags}
              onSearchResults={setSearchResults}
            />
            
            <div className="grid gap-4">
              {searchResults.map(entry => (
                <div
                  key={entry.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => editEntry(entry)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {entry.title || 'Untitled'}
                    </h3>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        editEntry(entry);
                      }}
                      className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                    {entry.content.slice(0, 200)}
                    {entry.content.length > 200 && '...'}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(entry.updatedAt)}</span>
                      </div>
                      <span>{entry.wordCount} words</span>
                    </div>
                    
                    {entry.tags.length > 0 && (
                      <div className="flex items-center space-x-1">
                        <Hash className="w-3 h-3" />
                        <span>{entry.tags.slice(0, 2).join(', ')}</span>
                        {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {viewMode === 'upload' && (
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Import Writings
            </h2>
            
            <FileUpload onEntriesImported={handleEntriesImported} />
          </div>
        )}
      </main>

      {/* Settings modal */}
      <Settings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </div>
  );
}
