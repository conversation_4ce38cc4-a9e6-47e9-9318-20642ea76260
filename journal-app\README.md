# AllJournal - Your Ultimate Journal App

A powerful, feature-rich journal application built with Next.js, React, and TypeScript. AllJournal provides everything you need for digital journaling with modern features like auto-save, tagging, file import, and customizable themes.

## ✨ Features

### 🔄 Auto-Save
- **Real-time saving** as you type with configurable intervals
- **Local storage backup** for immediate access
- **SQLite database integration** for persistent storage
- **Conflict resolution** between local and server data

### 🏷️ Advanced Tagging & Search
- **Flexible tagging system** with auto-complete suggestions
- **Full-text search** across all entries
- **Tag-based filtering** for quick content discovery
- **Smart tag management** with usage statistics

### 📁 File Import & Export
- **Import existing writings** from .txt and .docx files
- **Automatic title extraction** from document content
- **Batch file processing** with progress indicators
- **Export functionality** for data portability

### 🎨 Customizable Interface
- **Light/Dark/System themes** with smooth transitions
- **Adjustable text size** and font family options
- **Zoom controls** for better accessibility
- **Responsive design** for all screen sizes

### ✍️ Writing Enhancement
- **Spell checking** with custom dictionary support
- **Word and character counting** in real-time
- **Distraction-free writing mode**
- **Keyboard shortcuts** for power users

### 💾 Data Management
- **Local-first approach** with offline capabilities
- **SQLite database** for reliable data storage
- **Automatic backups** and data synchronization
- **Privacy-focused** - your data stays on your device

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd journal-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Building for Production

```bash
npm run build
npm start
```

## 📖 Usage Guide

### Creating Your First Entry
1. Click the **"New Entry"** button in the sidebar
2. Add a title and start writing your content
3. Add tags by typing in the tag input field
4. Your work is automatically saved as you type

### Organizing with Tags
- Type tags in the tag input field and press Enter
- Click on existing tags to filter entries
- Use the tag manager to search and organize your content

### Importing Existing Content
1. Click the **Upload** button in the toolbar
2. Select .txt or .docx files from your computer
3. Review the imported content and click **Import**
4. Your files will be converted to journal entries

### Customizing Your Experience
1. Click the **Settings** button (gear icon)
2. Adjust theme, font size, and other preferences
3. Configure auto-save intervals and spell check options
4. Changes are saved automatically

### Keyboard Shortcuts
- `Ctrl/Cmd + S` - Manual save
- `Ctrl/Cmd + N` - New entry (when implemented)
- `Ctrl/Cmd + F` - Focus search (when implemented)

## 🛠️ Technical Architecture

### Frontend Stack
- **Next.js 15** - React framework with App Router
- **React 18** - UI library with hooks and context
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Lucide React** - Beautiful icons

### Backend & Database
- **SQLite** - Local database with better-sqlite3
- **Next.js API Routes** - Server-side endpoints
- **File System API** - Local file operations

### Key Libraries
- **react-dropzone** - File upload handling
- **mammoth** - DOCX file processing
- **better-sqlite3** - SQLite database operations

### Data Flow
1. **User Input** → Local State → Auto-save Timer
2. **Auto-save** → localStorage (immediate) → SQLite (persistent)
3. **Search/Filter** → SQLite Query → Cached Results
4. **File Import** → File Processing → Database Storage

---

**Built with ❤️ for writers, thinkers, and digital journaling enthusiasts.**
