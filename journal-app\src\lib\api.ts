import { JournalEntry, Tag } from './database';

const API_BASE = '/api';

export class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE}${endpoint}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      response.status,
      errorData.error || `HTTP ${response.status}: ${response.statusText}`
    );
  }

  return response.json();
}

// Entry API functions
export const entriesApi = {
  // Get all entries with optional search and filtering
  async getAll(params?: {
    q?: string;
    tags?: string[];
    limit?: number;
    offset?: number;
  }): Promise<{ entries: JournalEntry[] }> {
    const searchParams = new URLSearchParams();
    
    if (params?.q) searchParams.set('q', params.q);
    if (params?.tags?.length) searchParams.set('tags', params.tags.join(','));
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.offset) searchParams.set('offset', params.offset.toString());

    const query = searchParams.toString();
    return apiRequest(`/entries${query ? `?${query}` : ''}`);
  },

  // Get a specific entry by ID
  async getById(id: string): Promise<{ entry: JournalEntry }> {
    return apiRequest(`/entries/${id}`);
  },

  // Create a new entry
  async create(data: {
    title: string;
    content: string;
    tags?: string[];
  }): Promise<{ entry: JournalEntry }> {
    return apiRequest('/entries', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update an existing entry
  async update(
    id: string,
    data: {
      title?: string;
      content?: string;
      tags?: string[];
    }
  ): Promise<{ entry: JournalEntry }> {
    return apiRequest(`/entries/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete an entry
  async delete(id: string): Promise<{ message: string }> {
    return apiRequest(`/entries/${id}`, {
      method: 'DELETE',
    });
  },
};

// Tags API functions
export const tagsApi = {
  // Get all tags
  async getAll(): Promise<{ tags: Tag[] }> {
    return apiRequest('/tags');
  },

  // Create a new tag
  async create(data: {
    name: string;
    color?: string;
  }): Promise<{ tag: Tag }> {
    return apiRequest('/tags', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
};

// Sync functions for localStorage and server
export const syncApi = {
  // Sync local entries to server
  async syncToServer(localEntries: JournalEntry[]): Promise<void> {
    for (const entry of localEntries) {
      try {
        // Try to update first (in case it exists on server)
        await entriesApi.update(entry.id, {
          title: entry.title,
          content: entry.content,
          tags: entry.tags,
        });
      } catch (error) {
        if (error instanceof ApiError && error.status === 404) {
          // Entry doesn't exist on server, create it
          await entriesApi.create({
            title: entry.title,
            content: entry.content,
            tags: entry.tags,
          });
        } else {
          console.error('Failed to sync entry to server:', error);
        }
      }
    }
  },

  // Sync server entries to local storage
  async syncFromServer(): Promise<JournalEntry[]> {
    try {
      const { entries } = await entriesApi.getAll();
      return entries;
    } catch (error) {
      console.error('Failed to sync from server:', error);
      return [];
    }
  },

  // Full bidirectional sync
  async fullSync(localEntries: JournalEntry[]): Promise<JournalEntry[]> {
    try {
      // First, get all server entries
      const serverEntries = await this.syncFromServer();
      
      // Create a map of server entries by ID
      const serverEntriesMap = new Map(
        serverEntries.map(entry => [entry.id, entry])
      );

      // Sync local entries to server
      for (const localEntry of localEntries) {
        const serverEntry = serverEntriesMap.get(localEntry.id);
        
        if (!serverEntry) {
          // Entry only exists locally, create on server
          await entriesApi.create({
            title: localEntry.title,
            content: localEntry.content,
            tags: localEntry.tags,
          });
        } else if (new Date(localEntry.updated_at) > new Date(serverEntry.updated_at)) {
          // Local entry is newer, update server
          await entriesApi.update(localEntry.id, {
            title: localEntry.title,
            content: localEntry.content,
            tags: localEntry.tags,
          });
        }
      }

      // Get the final state from server
      return await this.syncFromServer();
    } catch (error) {
      console.error('Failed to perform full sync:', error);
      return localEntries; // Return local entries as fallback
    }
  },
};

export default {
  entries: entriesApi,
  tags: tagsApi,
  sync: syncApi,
};
