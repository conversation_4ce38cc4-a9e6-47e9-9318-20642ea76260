'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Tag } from '@/lib/types';
import { localStorageManager } from '@/lib/localStorage';
import { X, Plus, Hash, Palette } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

interface TagManagerProps {
  tags: Tag[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  onCreateTag?: (tag: Tag) => void;
  className?: string;
  maxTags?: number;
}

const TAG_COLORS = [
  '#ef4444', // red
  '#f97316', // orange
  '#eab308', // yellow
  '#22c55e', // green
  '#06b6d4', // cyan
  '#3b82f6', // blue
  '#8b5cf6', // violet
  '#ec4899', // pink
  '#6b7280', // gray
  '#84cc16', // lime
];

export default function TagManager({
  tags,
  selectedTags,
  onTagsChange,
  onCreateTag,
  className = '',
  maxTags = 10
}: TagManagerProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState(TAG_COLORS[0]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showColorPicker, setShowColorPicker] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const colorPickerRef = useRef<HTMLDivElement>(null);

  // Filter tags based on search query
  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get available tags (not already selected)
  const availableTags = filteredTags.filter(tag =>
    !selectedTags.includes(tag.name)
  );

  // Handle tag selection
  const handleTagSelect = (tagName: string) => {
    if (selectedTags.length >= maxTags) return;
    
    const newSelectedTags = [...selectedTags, tagName];
    onTagsChange(newSelectedTags);
    setSearchQuery('');
  };

  // Handle tag removal
  const handleTagRemove = (tagName: string) => {
    const newSelectedTags = selectedTags.filter(t => t !== tagName);
    onTagsChange(newSelectedTags);
  };

  // Handle new tag creation
  const handleCreateTag = () => {
    const trimmedName = newTagName.trim();
    if (!trimmedName) return;

    // Check if tag already exists
    const existingTag = tags.find(tag => 
      tag.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existingTag) {
      // If tag exists, just select it
      handleTagSelect(existingTag.name);
    } else {
      // Create new tag
      const newTag: Tag = {
        id: uuidv4(),
        name: trimmedName,
        color: newTagColor,
        createdAt: new Date(),
        usageCount: 1
      };

      // Save to localStorage
      localStorageManager.saveTag(newTag);

      // Call parent callback
      if (onCreateTag) {
        onCreateTag(newTag);
      }

      // Select the new tag
      handleTagSelect(newTag.name);
    }

    // Reset form
    setNewTagName('');
    setIsCreating(false);
    setShowColorPicker(false);
  };

  // Handle input key events
  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (isCreating) {
        handleCreateTag();
      } else if (availableTags.length > 0) {
        handleTagSelect(availableTags[0].name);
      }
    } else if (e.key === 'Escape') {
      setIsCreating(false);
      setSearchQuery('');
      setNewTagName('');
    } else if (e.key === 'Backspace' && !searchQuery && !newTagName && selectedTags.length > 0) {
      // Remove last selected tag when backspacing on empty input
      handleTagRemove(selectedTags[selectedTags.length - 1]);
    }
  };

  // Focus input when creating
  useEffect(() => {
    if (isCreating && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isCreating]);

  // Close color picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {
        setShowColorPicker(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getTagColor = (tagName: string) => {
    const tag = tags.find(t => t.name === tagName);
    return tag?.color || TAG_COLORS[0];
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Selected tags */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTags.map(tagName => (
            <span
              key={tagName}
              className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-sm font-medium text-white"
              style={{ backgroundColor: getTagColor(tagName) }}
            >
              <Hash className="w-3 h-3" />
              {tagName}
              <button
                onClick={() => handleTagRemove(tagName)}
                className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Tag input */}
      <div className="relative">
        <div className="flex items-center gap-2 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800">
          <Hash className="w-4 h-4 text-gray-400" />
          
          {isCreating ? (
            <div className="flex-1 flex items-center gap-2">
              <input
                ref={inputRef}
                type="text"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                onKeyDown={handleInputKeyDown}
                placeholder="Enter tag name..."
                className="flex-1 bg-transparent outline-none text-gray-900 dark:text-white"
                maxLength={30}
              />
              
              <div className="relative" ref={colorPickerRef}>
                <button
                  onClick={() => setShowColorPicker(!showColorPicker)}
                  className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: newTagColor }}
                  />
                </button>
                
                {showColorPicker && (
                  <div className="absolute top-full right-0 mt-1 p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10">
                    <div className="grid grid-cols-5 gap-1">
                      {TAG_COLORS.map(color => (
                        <button
                          key={color}
                          onClick={() => {
                            setNewTagColor(color);
                            setShowColorPicker(false);
                          }}
                          className="w-6 h-6 rounded-full border border-gray-300 hover:scale-110 transition-transform"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <button
                onClick={handleCreateTag}
                disabled={!newTagName.trim()}
                className="px-2 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Create
              </button>
              
              <button
                onClick={() => {
                  setIsCreating(false);
                  setNewTagName('');
                  setShowColorPicker(false);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleInputKeyDown}
              placeholder={selectedTags.length >= maxTags ? `Max ${maxTags} tags reached` : "Search or add tags..."}
              className="flex-1 bg-transparent outline-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              disabled={selectedTags.length >= maxTags}
            />
          )}
          
          {!isCreating && selectedTags.length < maxTags && (
            <button
              onClick={() => setIsCreating(true)}
              className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="Create new tag"
            >
              <Plus className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Tag suggestions */}
        {!isCreating && searchQuery && availableTags.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto">
            {availableTags.slice(0, 8).map(tag => (
              <button
                key={tag.id}
                onClick={() => handleTagSelect(tag.name)}
                className="w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: tag.color || TAG_COLORS[0] }}
                />
                <span className="text-gray-900 dark:text-white">{tag.name}</span>
                <span className="ml-auto text-xs text-gray-500 dark:text-gray-400">
                  {tag.usageCount} uses
                </span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Tag limit indicator */}
      {selectedTags.length > 0 && (
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {selectedTags.length} / {maxTags} tags
        </div>
      )}
    </div>
  );
}
