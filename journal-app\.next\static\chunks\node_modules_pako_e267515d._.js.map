{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/utils/common.js"], "sourcesContent": ["'use strict';\n\n\nvar TYPED_OK =  (typeof Uint8Array !== 'undefined') &&\n                (typeof Uint16Array !== 'undefined') &&\n                (typeof Int32Array !== 'undefined');\n\nfunction _has(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexports.assign = function (obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n  while (sources.length) {\n    var source = sources.shift();\n    if (!source) { continue; }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be non-object');\n    }\n\n    for (var p in source) {\n      if (_has(source, p)) {\n        obj[p] = source[p];\n      }\n    }\n  }\n\n  return obj;\n};\n\n\n// reduce buffer size, avoiding mem copy\nexports.shrinkBuf = function (buf, size) {\n  if (buf.length === size) { return buf; }\n  if (buf.subarray) { return buf.subarray(0, size); }\n  buf.length = size;\n  return buf;\n};\n\n\nvar fnTyped = {\n  arraySet: function (dest, src, src_offs, len, dest_offs) {\n    if (src.subarray && dest.subarray) {\n      dest.set(src.subarray(src_offs, src_offs + len), dest_offs);\n      return;\n    }\n    // Fallback to ordinary array\n    for (var i = 0; i < len; i++) {\n      dest[dest_offs + i] = src[src_offs + i];\n    }\n  },\n  // Join array of chunks to single array.\n  flattenChunks: function (chunks) {\n    var i, l, len, pos, chunk, result;\n\n    // calculate data length\n    len = 0;\n    for (i = 0, l = chunks.length; i < l; i++) {\n      len += chunks[i].length;\n    }\n\n    // join chunks\n    result = new Uint8Array(len);\n    pos = 0;\n    for (i = 0, l = chunks.length; i < l; i++) {\n      chunk = chunks[i];\n      result.set(chunk, pos);\n      pos += chunk.length;\n    }\n\n    return result;\n  }\n};\n\nvar fnUntyped = {\n  arraySet: function (dest, src, src_offs, len, dest_offs) {\n    for (var i = 0; i < len; i++) {\n      dest[dest_offs + i] = src[src_offs + i];\n    }\n  },\n  // Join array of chunks to single array.\n  flattenChunks: function (chunks) {\n    return [].concat.apply([], chunks);\n  }\n};\n\n\n// Enable/Disable typed arrays use, for testing\n//\nexports.setTyped = function (on) {\n  if (on) {\n    exports.Buf8  = Uint8Array;\n    exports.Buf16 = Uint16Array;\n    exports.Buf32 = Int32Array;\n    exports.assign(exports, fnTyped);\n  } else {\n    exports.Buf8  = Array;\n    exports.Buf16 = Array;\n    exports.Buf32 = Array;\n    exports.assign(exports, fnUntyped);\n  }\n};\n\nexports.setTyped(TYPED_OK);\n"], "names": [], "mappings": "AAAA;AAGA,IAAI,WAAY,AAAC,OAAO,eAAe,eACtB,OAAO,gBAAgB,eACvB,OAAO,eAAe;AAEvC,SAAS,KAAK,GAAG,EAAE,GAAG;IACpB,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AACnD;AAEA,QAAQ,MAAM,GAAG,SAAU,IAAI,0BAA0B,GAA3B;IAC5B,IAAI,UAAU,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACpD,MAAO,QAAQ,MAAM,CAAE;QACrB,IAAI,SAAS,QAAQ,KAAK;QAC1B,IAAI,CAAC,QAAQ;YAAE;QAAU;QAEzB,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,IAAI,UAAU,SAAS;QAC/B;QAEA,IAAK,IAAI,KAAK,OAAQ;YACpB,IAAI,KAAK,QAAQ,IAAI;gBACnB,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;YACpB;QACF;IACF;IAEA,OAAO;AACT;AAGA,wCAAwC;AACxC,QAAQ,SAAS,GAAG,SAAU,GAAG,EAAE,IAAI;IACrC,IAAI,IAAI,MAAM,KAAK,MAAM;QAAE,OAAO;IAAK;IACvC,IAAI,IAAI,QAAQ,EAAE;QAAE,OAAO,IAAI,QAAQ,CAAC,GAAG;IAAO;IAClD,IAAI,MAAM,GAAG;IACb,OAAO;AACT;AAGA,IAAI,UAAU;IACZ,UAAU,SAAU,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS;QACrD,IAAI,IAAI,QAAQ,IAAI,KAAK,QAAQ,EAAE;YACjC,KAAK,GAAG,CAAC,IAAI,QAAQ,CAAC,UAAU,WAAW,MAAM;YACjD;QACF;QACA,6BAA6B;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAI,CAAC,YAAY,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE;QACzC;IACF;IACA,wCAAwC;IACxC,eAAe,SAAU,MAAM;QAC7B,IAAI,GAAG,GAAG,KAAK,KAAK,OAAO;QAE3B,wBAAwB;QACxB,MAAM;QACN,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAK;YACzC,OAAO,MAAM,CAAC,EAAE,CAAC,MAAM;QACzB;QAEA,cAAc;QACd,SAAS,IAAI,WAAW;QACxB,MAAM;QACN,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAK;YACzC,QAAQ,MAAM,CAAC,EAAE;YACjB,OAAO,GAAG,CAAC,OAAO;YAClB,OAAO,MAAM,MAAM;QACrB;QAEA,OAAO;IACT;AACF;AAEA,IAAI,YAAY;IACd,UAAU,SAAU,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAI,CAAC,YAAY,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE;QACzC;IACF;IACA,wCAAwC;IACxC,eAAe,SAAU,MAAM;QAC7B,OAAO,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;IAC7B;AACF;AAGA,+CAA+C;AAC/C,EAAE;AACF,QAAQ,QAAQ,GAAG,SAAU,EAAE;IAC7B,IAAI,IAAI;QACN,QAAQ,IAAI,GAAI;QAChB,QAAQ,KAAK,GAAG;QAChB,QAAQ,KAAK,GAAG;QAChB,QAAQ,MAAM,CAAC,SAAS;IAC1B,OAAO;QACL,QAAQ,IAAI,GAAI;QAChB,QAAQ,KAAK,GAAG;QAChB,QAAQ,KAAK,GAAG;QAChB,QAAQ,MAAM,CAAC,SAAS;IAC1B;AACF;AAEA,QAAQ,QAAQ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/trees.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n/* eslint-disable space-unary-ops */\n\nvar utils = require('../utils/common');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n//var Z_FILTERED          = 1;\n//var Z_HUFFMAN_ONLY      = 2;\n//var Z_RLE               = 3;\nvar Z_FIXED               = 4;\n//var Z_DEFAULT_STRATEGY  = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\nvar Z_BINARY              = 0;\nvar Z_TEXT                = 1;\n//var Z_ASCII             = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n/*============================================================================*/\n\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n// From zutil.h\n\nvar STORED_BLOCK = 0;\nvar STATIC_TREES = 1;\nvar DYN_TREES    = 2;\n/* The three kinds of block type */\n\nvar MIN_MATCH    = 3;\nvar MAX_MATCH    = 258;\n/* The minimum and maximum match lengths */\n\n// From deflate.h\n/* ===========================================================================\n * Internal compression state.\n */\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\n\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\n\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\n\nvar D_CODES       = 30;\n/* number of distance codes */\n\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\n\nvar HEAP_SIZE     = 2 * L_CODES + 1;\n/* maximum heap size */\n\nvar MAX_BITS      = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar Buf_size      = 16;\n/* size of bit buffer in bi_buf */\n\n\n/* ===========================================================================\n * Constants\n */\n\nvar MAX_BL_BITS = 7;\n/* Bit length codes must not exceed MAX_BL_BITS bits */\n\nvar END_BLOCK   = 256;\n/* end of block literal code */\n\nvar REP_3_6     = 16;\n/* repeat previous bit length 3-6 times (2 bits of repeat count) */\n\nvar REPZ_3_10   = 17;\n/* repeat a zero length 3-10 times  (3 bits of repeat count) */\n\nvar REPZ_11_138 = 18;\n/* repeat a zero length 11-138 times  (7 bits of repeat count) */\n\n/* eslint-disable comma-spacing,array-bracket-spacing */\nvar extra_lbits =   /* extra bits for each length code */\n  [0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0];\n\nvar extra_dbits =   /* extra bits for each distance code */\n  [0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13];\n\nvar extra_blbits =  /* extra bits for each bit length code */\n  [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7];\n\nvar bl_order =\n  [16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];\n/* eslint-enable comma-spacing,array-bracket-spacing */\n\n/* The lengths of the bit length codes are sent in order of decreasing\n * probability, to avoid transmitting the lengths for unused bit length codes.\n */\n\n/* ===========================================================================\n * Local data. These are initialized only once.\n */\n\n// We pre-fill arrays with 0 to avoid uninitialized gaps\n\nvar DIST_CODE_LEN = 512; /* see definition of array dist_code below */\n\n// !!!! Use flat array instead of structure, Freq = i*2, Len = i*2+1\nvar static_ltree  = new Array((L_CODES + 2) * 2);\nzero(static_ltree);\n/* The static literal tree. Since the bit lengths are imposed, there is no\n * need for the L_CODES extra codes used during heap construction. However\n * The codes 286 and 287 are needed to build a canonical tree (see _tr_init\n * below).\n */\n\nvar static_dtree  = new Array(D_CODES * 2);\nzero(static_dtree);\n/* The static distance tree. (Actually a trivial tree since all codes use\n * 5 bits.)\n */\n\nvar _dist_code    = new Array(DIST_CODE_LEN);\nzero(_dist_code);\n/* Distance codes. The first 256 values correspond to the distances\n * 3 .. 258, the last 256 values correspond to the top 8 bits of\n * the 15 bit distances.\n */\n\nvar _length_code  = new Array(MAX_MATCH - MIN_MATCH + 1);\nzero(_length_code);\n/* length code for each normalized match length (0 == MIN_MATCH) */\n\nvar base_length   = new Array(LENGTH_CODES);\nzero(base_length);\n/* First normalized length for each code (0 = MIN_MATCH) */\n\nvar base_dist     = new Array(D_CODES);\nzero(base_dist);\n/* First normalized distance for each code (0 = distance of 1) */\n\n\nfunction StaticTreeDesc(static_tree, extra_bits, extra_base, elems, max_length) {\n\n  this.static_tree  = static_tree;  /* static tree or NULL */\n  this.extra_bits   = extra_bits;   /* extra bits for each code or NULL */\n  this.extra_base   = extra_base;   /* base index for extra_bits */\n  this.elems        = elems;        /* max number of elements in the tree */\n  this.max_length   = max_length;   /* max bit length for the codes */\n\n  // show if `static_tree` has data or dummy - needed for monomorphic objects\n  this.has_stree    = static_tree && static_tree.length;\n}\n\n\nvar static_l_desc;\nvar static_d_desc;\nvar static_bl_desc;\n\n\nfunction TreeDesc(dyn_tree, stat_desc) {\n  this.dyn_tree = dyn_tree;     /* the dynamic tree */\n  this.max_code = 0;            /* largest code with non zero frequency */\n  this.stat_desc = stat_desc;   /* the corresponding static tree */\n}\n\n\n\nfunction d_code(dist) {\n  return dist < 256 ? _dist_code[dist] : _dist_code[256 + (dist >>> 7)];\n}\n\n\n/* ===========================================================================\n * Output a short LSB first on the stream.\n * IN assertion: there is enough room in pendingBuf.\n */\nfunction put_short(s, w) {\n//    put_byte(s, (uch)((w) & 0xff));\n//    put_byte(s, (uch)((ush)(w) >> 8));\n  s.pending_buf[s.pending++] = (w) & 0xff;\n  s.pending_buf[s.pending++] = (w >>> 8) & 0xff;\n}\n\n\n/* ===========================================================================\n * Send a value on a given number of bits.\n * IN assertion: length <= 16 and value fits in length bits.\n */\nfunction send_bits(s, value, length) {\n  if (s.bi_valid > (Buf_size - length)) {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    put_short(s, s.bi_buf);\n    s.bi_buf = value >> (Buf_size - s.bi_valid);\n    s.bi_valid += length - Buf_size;\n  } else {\n    s.bi_buf |= (value << s.bi_valid) & 0xffff;\n    s.bi_valid += length;\n  }\n}\n\n\nfunction send_code(s, c, tree) {\n  send_bits(s, tree[c * 2]/*.Code*/, tree[c * 2 + 1]/*.Len*/);\n}\n\n\n/* ===========================================================================\n * Reverse the first len bits of a code, using straightforward code (a faster\n * method would use a table)\n * IN assertion: 1 <= len <= 15\n */\nfunction bi_reverse(code, len) {\n  var res = 0;\n  do {\n    res |= code & 1;\n    code >>>= 1;\n    res <<= 1;\n  } while (--len > 0);\n  return res >>> 1;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer, keeping at most 7 bits in it.\n */\nfunction bi_flush(s) {\n  if (s.bi_valid === 16) {\n    put_short(s, s.bi_buf);\n    s.bi_buf = 0;\n    s.bi_valid = 0;\n\n  } else if (s.bi_valid >= 8) {\n    s.pending_buf[s.pending++] = s.bi_buf & 0xff;\n    s.bi_buf >>= 8;\n    s.bi_valid -= 8;\n  }\n}\n\n\n/* ===========================================================================\n * Compute the optimal bit lengths for a tree and update the total bit length\n * for the current block.\n * IN assertion: the fields freq and dad are set, heap[heap_max] and\n *    above are the tree nodes sorted by increasing frequency.\n * OUT assertions: the field len is set to the optimal bit length, the\n *     array bl_count contains the frequencies for each bit length.\n *     The length opt_len is updated; static_len is also updated if stree is\n *     not null.\n */\nfunction gen_bitlen(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc;    /* the tree descriptor */\n{\n  var tree            = desc.dyn_tree;\n  var max_code        = desc.max_code;\n  var stree           = desc.stat_desc.static_tree;\n  var has_stree       = desc.stat_desc.has_stree;\n  var extra           = desc.stat_desc.extra_bits;\n  var base            = desc.stat_desc.extra_base;\n  var max_length      = desc.stat_desc.max_length;\n  var h;              /* heap index */\n  var n, m;           /* iterate over the tree elements */\n  var bits;           /* bit length */\n  var xbits;          /* extra bits */\n  var f;              /* frequency */\n  var overflow = 0;   /* number of elements with bit length too large */\n\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    s.bl_count[bits] = 0;\n  }\n\n  /* In a first pass, compute the optimal bit lengths (which may\n   * overflow in the case of the bit length tree).\n   */\n  tree[s.heap[s.heap_max] * 2 + 1]/*.Len*/ = 0; /* root of the heap */\n\n  for (h = s.heap_max + 1; h < HEAP_SIZE; h++) {\n    n = s.heap[h];\n    bits = tree[tree[n * 2 + 1]/*.Dad*/ * 2 + 1]/*.Len*/ + 1;\n    if (bits > max_length) {\n      bits = max_length;\n      overflow++;\n    }\n    tree[n * 2 + 1]/*.Len*/ = bits;\n    /* We overwrite tree[n].Dad which is no longer needed */\n\n    if (n > max_code) { continue; } /* not a leaf node */\n\n    s.bl_count[bits]++;\n    xbits = 0;\n    if (n >= base) {\n      xbits = extra[n - base];\n    }\n    f = tree[n * 2]/*.Freq*/;\n    s.opt_len += f * (bits + xbits);\n    if (has_stree) {\n      s.static_len += f * (stree[n * 2 + 1]/*.Len*/ + xbits);\n    }\n  }\n  if (overflow === 0) { return; }\n\n  // Trace((stderr,\"\\nbit length overflow\\n\"));\n  /* This happens for example on obj2 and pic of the Calgary corpus */\n\n  /* Find the first bit length which could increase: */\n  do {\n    bits = max_length - 1;\n    while (s.bl_count[bits] === 0) { bits--; }\n    s.bl_count[bits]--;      /* move one leaf down the tree */\n    s.bl_count[bits + 1] += 2; /* move one overflow item as its brother */\n    s.bl_count[max_length]--;\n    /* The brother of the overflow item also moves one step up,\n     * but this does not affect bl_count[max_length]\n     */\n    overflow -= 2;\n  } while (overflow > 0);\n\n  /* Now recompute all bit lengths, scanning in increasing frequency.\n   * h is still equal to HEAP_SIZE. (It is simpler to reconstruct all\n   * lengths instead of fixing only the wrong ones. This idea is taken\n   * from 'ar' written by Haruhiko Okumura.)\n   */\n  for (bits = max_length; bits !== 0; bits--) {\n    n = s.bl_count[bits];\n    while (n !== 0) {\n      m = s.heap[--h];\n      if (m > max_code) { continue; }\n      if (tree[m * 2 + 1]/*.Len*/ !== bits) {\n        // Trace((stderr,\"code %d bits %d->%d\\n\", m, tree[m].Len, bits));\n        s.opt_len += (bits - tree[m * 2 + 1]/*.Len*/) * tree[m * 2]/*.Freq*/;\n        tree[m * 2 + 1]/*.Len*/ = bits;\n      }\n      n--;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Generate the codes for a given tree and bit counts (which need not be\n * optimal).\n * IN assertion: the array bl_count contains the bit length statistics for\n * the given tree and the field len is set for all tree elements.\n * OUT assertion: the field code is set for all tree elements of non\n *     zero code length.\n */\nfunction gen_codes(tree, max_code, bl_count)\n//    ct_data *tree;             /* the tree to decorate */\n//    int max_code;              /* largest code with non zero frequency */\n//    ushf *bl_count;            /* number of codes at each bit length */\n{\n  var next_code = new Array(MAX_BITS + 1); /* next code value for each bit length */\n  var code = 0;              /* running code value */\n  var bits;                  /* bit index */\n  var n;                     /* code index */\n\n  /* The distribution counts are first used to generate the code values\n   * without bit reversal.\n   */\n  for (bits = 1; bits <= MAX_BITS; bits++) {\n    next_code[bits] = code = (code + bl_count[bits - 1]) << 1;\n  }\n  /* Check that the bit counts in bl_count are consistent. The last code\n   * must be all ones.\n   */\n  //Assert (code + bl_count[MAX_BITS]-1 == (1<<MAX_BITS)-1,\n  //        \"inconsistent bit counts\");\n  //Tracev((stderr,\"\\ngen_codes: max_code %d \", max_code));\n\n  for (n = 0;  n <= max_code; n++) {\n    var len = tree[n * 2 + 1]/*.Len*/;\n    if (len === 0) { continue; }\n    /* Now reverse the bits */\n    tree[n * 2]/*.Code*/ = bi_reverse(next_code[len]++, len);\n\n    //Tracecv(tree != static_ltree, (stderr,\"\\nn %3d %c l %2d c %4x (%x) \",\n    //     n, (isgraph(n) ? n : ' '), len, tree[n].Code, next_code[len]-1));\n  }\n}\n\n\n/* ===========================================================================\n * Initialize the various 'constant' tables.\n */\nfunction tr_static_init() {\n  var n;        /* iterates over tree elements */\n  var bits;     /* bit counter */\n  var length;   /* length value */\n  var code;     /* code value */\n  var dist;     /* distance index */\n  var bl_count = new Array(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  // do check in _tr_init()\n  //if (static_init_done) return;\n\n  /* For some embedded targets, global variables are not initialized: */\n/*#ifdef NO_INIT_GLOBAL_POINTERS\n  static_l_desc.static_tree = static_ltree;\n  static_l_desc.extra_bits = extra_lbits;\n  static_d_desc.static_tree = static_dtree;\n  static_d_desc.extra_bits = extra_dbits;\n  static_bl_desc.extra_bits = extra_blbits;\n#endif*/\n\n  /* Initialize the mapping length (0..255) -> length code (0..28) */\n  length = 0;\n  for (code = 0; code < LENGTH_CODES - 1; code++) {\n    base_length[code] = length;\n    for (n = 0; n < (1 << extra_lbits[code]); n++) {\n      _length_code[length++] = code;\n    }\n  }\n  //Assert (length == 256, \"tr_static_init: length != 256\");\n  /* Note that the length 255 (match length 258) can be represented\n   * in two different ways: code 284 + 5 bits or code 285, so we\n   * overwrite length_code[255] to use the best encoding:\n   */\n  _length_code[length - 1] = code;\n\n  /* Initialize the mapping dist (0..32K) -> dist code (0..29) */\n  dist = 0;\n  for (code = 0; code < 16; code++) {\n    base_dist[code] = dist;\n    for (n = 0; n < (1 << extra_dbits[code]); n++) {\n      _dist_code[dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: dist != 256\");\n  dist >>= 7; /* from now on, all distances are divided by 128 */\n  for (; code < D_CODES; code++) {\n    base_dist[code] = dist << 7;\n    for (n = 0; n < (1 << (extra_dbits[code] - 7)); n++) {\n      _dist_code[256 + dist++] = code;\n    }\n  }\n  //Assert (dist == 256, \"tr_static_init: 256+dist != 512\");\n\n  /* Construct the codes of the static literal tree */\n  for (bits = 0; bits <= MAX_BITS; bits++) {\n    bl_count[bits] = 0;\n  }\n\n  n = 0;\n  while (n <= 143) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  while (n <= 255) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 9;\n    n++;\n    bl_count[9]++;\n  }\n  while (n <= 279) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 7;\n    n++;\n    bl_count[7]++;\n  }\n  while (n <= 287) {\n    static_ltree[n * 2 + 1]/*.Len*/ = 8;\n    n++;\n    bl_count[8]++;\n  }\n  /* Codes 286 and 287 do not exist, but we must include them in the\n   * tree construction to get a canonical Huffman tree (longest code\n   * all ones)\n   */\n  gen_codes(static_ltree, L_CODES + 1, bl_count);\n\n  /* The static distance tree is trivial: */\n  for (n = 0; n < D_CODES; n++) {\n    static_dtree[n * 2 + 1]/*.Len*/ = 5;\n    static_dtree[n * 2]/*.Code*/ = bi_reverse(n, 5);\n  }\n\n  // Now data ready and we can init static trees\n  static_l_desc = new StaticTreeDesc(static_ltree, extra_lbits, LITERALS + 1, L_CODES, MAX_BITS);\n  static_d_desc = new StaticTreeDesc(static_dtree, extra_dbits, 0,          D_CODES, MAX_BITS);\n  static_bl_desc = new StaticTreeDesc(new Array(0), extra_blbits, 0,         BL_CODES, MAX_BL_BITS);\n\n  //static_init_done = true;\n}\n\n\n/* ===========================================================================\n * Initialize a new block.\n */\nfunction init_block(s) {\n  var n; /* iterates over tree elements */\n\n  /* Initialize the trees. */\n  for (n = 0; n < L_CODES;  n++) { s.dyn_ltree[n * 2]/*.Freq*/ = 0; }\n  for (n = 0; n < D_CODES;  n++) { s.dyn_dtree[n * 2]/*.Freq*/ = 0; }\n  for (n = 0; n < BL_CODES; n++) { s.bl_tree[n * 2]/*.Freq*/ = 0; }\n\n  s.dyn_ltree[END_BLOCK * 2]/*.Freq*/ = 1;\n  s.opt_len = s.static_len = 0;\n  s.last_lit = s.matches = 0;\n}\n\n\n/* ===========================================================================\n * Flush the bit buffer and align the output on a byte boundary\n */\nfunction bi_windup(s)\n{\n  if (s.bi_valid > 8) {\n    put_short(s, s.bi_buf);\n  } else if (s.bi_valid > 0) {\n    //put_byte(s, (Byte)s->bi_buf);\n    s.pending_buf[s.pending++] = s.bi_buf;\n  }\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n}\n\n/* ===========================================================================\n * Copy a stored block, storing first the length and its\n * one's complement if requested.\n */\nfunction copy_block(s, buf, len, header)\n//DeflateState *s;\n//charf    *buf;    /* the input data */\n//unsigned len;     /* its length */\n//int      header;  /* true if block header must be written */\n{\n  bi_windup(s);        /* align on byte boundary */\n\n  if (header) {\n    put_short(s, len);\n    put_short(s, ~len);\n  }\n//  while (len--) {\n//    put_byte(s, *buf++);\n//  }\n  utils.arraySet(s.pending_buf, s.window, buf, len, s.pending);\n  s.pending += len;\n}\n\n/* ===========================================================================\n * Compares to subtrees, using the tree depth as tie breaker when\n * the subtrees have equal frequency. This minimizes the worst case length.\n */\nfunction smaller(tree, n, m, depth) {\n  var _n2 = n * 2;\n  var _m2 = m * 2;\n  return (tree[_n2]/*.Freq*/ < tree[_m2]/*.Freq*/ ||\n         (tree[_n2]/*.Freq*/ === tree[_m2]/*.Freq*/ && depth[n] <= depth[m]));\n}\n\n/* ===========================================================================\n * Restore the heap property by moving down the tree starting at node k,\n * exchanging a node with the smallest of its two sons if necessary, stopping\n * when the heap property is re-established (each father smaller than its\n * two sons).\n */\nfunction pqdownheap(s, tree, k)\n//    deflate_state *s;\n//    ct_data *tree;  /* the tree to restore */\n//    int k;               /* node to move down */\n{\n  var v = s.heap[k];\n  var j = k << 1;  /* left son of k */\n  while (j <= s.heap_len) {\n    /* Set j to the smallest of the two sons: */\n    if (j < s.heap_len &&\n      smaller(tree, s.heap[j + 1], s.heap[j], s.depth)) {\n      j++;\n    }\n    /* Exit if v is smaller than both sons */\n    if (smaller(tree, v, s.heap[j], s.depth)) { break; }\n\n    /* Exchange v with the smallest son */\n    s.heap[k] = s.heap[j];\n    k = j;\n\n    /* And continue down the tree, setting j to the left son of k */\n    j <<= 1;\n  }\n  s.heap[k] = v;\n}\n\n\n// inlined manually\n// var SMALLEST = 1;\n\n/* ===========================================================================\n * Send the block data compressed using the given Huffman trees\n */\nfunction compress_block(s, ltree, dtree)\n//    deflate_state *s;\n//    const ct_data *ltree; /* literal tree */\n//    const ct_data *dtree; /* distance tree */\n{\n  var dist;           /* distance of matched string */\n  var lc;             /* match length or unmatched char (if dist == 0) */\n  var lx = 0;         /* running index in l_buf */\n  var code;           /* the code to send */\n  var extra;          /* number of extra bits to send */\n\n  if (s.last_lit !== 0) {\n    do {\n      dist = (s.pending_buf[s.d_buf + lx * 2] << 8) | (s.pending_buf[s.d_buf + lx * 2 + 1]);\n      lc = s.pending_buf[s.l_buf + lx];\n      lx++;\n\n      if (dist === 0) {\n        send_code(s, lc, ltree); /* send a literal byte */\n        //Tracecv(isgraph(lc), (stderr,\" '%c' \", lc));\n      } else {\n        /* Here, lc is the match length - MIN_MATCH */\n        code = _length_code[lc];\n        send_code(s, code + LITERALS + 1, ltree); /* send the length code */\n        extra = extra_lbits[code];\n        if (extra !== 0) {\n          lc -= base_length[code];\n          send_bits(s, lc, extra);       /* send the extra length bits */\n        }\n        dist--; /* dist is now the match distance - 1 */\n        code = d_code(dist);\n        //Assert (code < D_CODES, \"bad d_code\");\n\n        send_code(s, code, dtree);       /* send the distance code */\n        extra = extra_dbits[code];\n        if (extra !== 0) {\n          dist -= base_dist[code];\n          send_bits(s, dist, extra);   /* send the extra distance bits */\n        }\n      } /* literal or match pair ? */\n\n      /* Check that the overlay between pending_buf and d_buf+l_buf is ok: */\n      //Assert((uInt)(s->pending) < s->lit_bufsize + 2*lx,\n      //       \"pendingBuf overflow\");\n\n    } while (lx < s.last_lit);\n  }\n\n  send_code(s, END_BLOCK, ltree);\n}\n\n\n/* ===========================================================================\n * Construct one Huffman tree and assigns the code bit strings and lengths.\n * Update the total bit length for the current block.\n * IN assertion: the field freq is set for all tree elements.\n * OUT assertions: the fields len and code are set to the optimal bit length\n *     and corresponding code. The length opt_len is updated; static_len is\n *     also updated if stree is not null. The field max_code is set.\n */\nfunction build_tree(s, desc)\n//    deflate_state *s;\n//    tree_desc *desc; /* the tree descriptor */\n{\n  var tree     = desc.dyn_tree;\n  var stree    = desc.stat_desc.static_tree;\n  var has_stree = desc.stat_desc.has_stree;\n  var elems    = desc.stat_desc.elems;\n  var n, m;          /* iterate over heap elements */\n  var max_code = -1; /* largest code with non zero frequency */\n  var node;          /* new node being created */\n\n  /* Construct the initial heap, with least frequent element in\n   * heap[SMALLEST]. The sons of heap[n] are heap[2*n] and heap[2*n+1].\n   * heap[0] is not used.\n   */\n  s.heap_len = 0;\n  s.heap_max = HEAP_SIZE;\n\n  for (n = 0; n < elems; n++) {\n    if (tree[n * 2]/*.Freq*/ !== 0) {\n      s.heap[++s.heap_len] = max_code = n;\n      s.depth[n] = 0;\n\n    } else {\n      tree[n * 2 + 1]/*.Len*/ = 0;\n    }\n  }\n\n  /* The pkzip format requires that at least one distance code exists,\n   * and that at least one bit should be sent even if there is only one\n   * possible code. So to avoid special checks later on we force at least\n   * two codes of non zero frequency.\n   */\n  while (s.heap_len < 2) {\n    node = s.heap[++s.heap_len] = (max_code < 2 ? ++max_code : 0);\n    tree[node * 2]/*.Freq*/ = 1;\n    s.depth[node] = 0;\n    s.opt_len--;\n\n    if (has_stree) {\n      s.static_len -= stree[node * 2 + 1]/*.Len*/;\n    }\n    /* node is 0 or 1 so it does not have extra bits */\n  }\n  desc.max_code = max_code;\n\n  /* The elements heap[heap_len/2+1 .. heap_len] are leaves of the tree,\n   * establish sub-heaps of increasing lengths:\n   */\n  for (n = (s.heap_len >> 1/*int /2*/); n >= 1; n--) { pqdownheap(s, tree, n); }\n\n  /* Construct the Huffman tree by repeatedly combining the least two\n   * frequent nodes.\n   */\n  node = elems;              /* next internal node of the tree */\n  do {\n    //pqremove(s, tree, n);  /* n = node of least frequency */\n    /*** pqremove ***/\n    n = s.heap[1/*SMALLEST*/];\n    s.heap[1/*SMALLEST*/] = s.heap[s.heap_len--];\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n    /***/\n\n    m = s.heap[1/*SMALLEST*/]; /* m = node of next least frequency */\n\n    s.heap[--s.heap_max] = n; /* keep the nodes sorted by frequency */\n    s.heap[--s.heap_max] = m;\n\n    /* Create a new node father of n and m */\n    tree[node * 2]/*.Freq*/ = tree[n * 2]/*.Freq*/ + tree[m * 2]/*.Freq*/;\n    s.depth[node] = (s.depth[n] >= s.depth[m] ? s.depth[n] : s.depth[m]) + 1;\n    tree[n * 2 + 1]/*.Dad*/ = tree[m * 2 + 1]/*.Dad*/ = node;\n\n    /* and insert the new node in the heap */\n    s.heap[1/*SMALLEST*/] = node++;\n    pqdownheap(s, tree, 1/*SMALLEST*/);\n\n  } while (s.heap_len >= 2);\n\n  s.heap[--s.heap_max] = s.heap[1/*SMALLEST*/];\n\n  /* At this point, the fields freq and dad are set. We can now\n   * generate the bit lengths.\n   */\n  gen_bitlen(s, desc);\n\n  /* The field len is now set, we can generate the bit codes */\n  gen_codes(tree, max_code, s.bl_count);\n}\n\n\n/* ===========================================================================\n * Scan a literal or distance tree to determine the frequencies of the codes\n * in the bit length tree.\n */\nfunction scan_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree;   /* the tree to be scanned */\n//    int max_code;    /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n  tree[(max_code + 1) * 2 + 1]/*.Len*/ = 0xffff; /* guard */\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      s.bl_tree[curlen * 2]/*.Freq*/ += count;\n\n    } else if (curlen !== 0) {\n\n      if (curlen !== prevlen) { s.bl_tree[curlen * 2]/*.Freq*/++; }\n      s.bl_tree[REP_3_6 * 2]/*.Freq*/++;\n\n    } else if (count <= 10) {\n      s.bl_tree[REPZ_3_10 * 2]/*.Freq*/++;\n\n    } else {\n      s.bl_tree[REPZ_11_138 * 2]/*.Freq*/++;\n    }\n\n    count = 0;\n    prevlen = curlen;\n\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Send a literal or distance tree in compressed form, using the codes in\n * bl_tree.\n */\nfunction send_tree(s, tree, max_code)\n//    deflate_state *s;\n//    ct_data *tree; /* the tree to be scanned */\n//    int max_code;       /* and its largest code of non zero frequency */\n{\n  var n;                     /* iterates over all tree elements */\n  var prevlen = -1;          /* last emitted length */\n  var curlen;                /* length of current code */\n\n  var nextlen = tree[0 * 2 + 1]/*.Len*/; /* length of next code */\n\n  var count = 0;             /* repeat count of the current code */\n  var max_count = 7;         /* max repeat count */\n  var min_count = 4;         /* min repeat count */\n\n  /* tree[max_code+1].Len = -1; */  /* guard already set */\n  if (nextlen === 0) {\n    max_count = 138;\n    min_count = 3;\n  }\n\n  for (n = 0; n <= max_code; n++) {\n    curlen = nextlen;\n    nextlen = tree[(n + 1) * 2 + 1]/*.Len*/;\n\n    if (++count < max_count && curlen === nextlen) {\n      continue;\n\n    } else if (count < min_count) {\n      do { send_code(s, curlen, s.bl_tree); } while (--count !== 0);\n\n    } else if (curlen !== 0) {\n      if (curlen !== prevlen) {\n        send_code(s, curlen, s.bl_tree);\n        count--;\n      }\n      //Assert(count >= 3 && count <= 6, \" 3_6?\");\n      send_code(s, REP_3_6, s.bl_tree);\n      send_bits(s, count - 3, 2);\n\n    } else if (count <= 10) {\n      send_code(s, REPZ_3_10, s.bl_tree);\n      send_bits(s, count - 3, 3);\n\n    } else {\n      send_code(s, REPZ_11_138, s.bl_tree);\n      send_bits(s, count - 11, 7);\n    }\n\n    count = 0;\n    prevlen = curlen;\n    if (nextlen === 0) {\n      max_count = 138;\n      min_count = 3;\n\n    } else if (curlen === nextlen) {\n      max_count = 6;\n      min_count = 3;\n\n    } else {\n      max_count = 7;\n      min_count = 4;\n    }\n  }\n}\n\n\n/* ===========================================================================\n * Construct the Huffman tree for the bit lengths and return the index in\n * bl_order of the last bit length code to send.\n */\nfunction build_bl_tree(s) {\n  var max_blindex;  /* index of last bit length code of non zero freq */\n\n  /* Determine the bit length frequencies for literal and distance trees */\n  scan_tree(s, s.dyn_ltree, s.l_desc.max_code);\n  scan_tree(s, s.dyn_dtree, s.d_desc.max_code);\n\n  /* Build the bit length tree: */\n  build_tree(s, s.bl_desc);\n  /* opt_len now includes the length of the tree representations, except\n   * the lengths of the bit lengths codes and the 5+5+4 bits for the counts.\n   */\n\n  /* Determine the number of bit length codes to send. The pkzip format\n   * requires that at least 4 bit length codes be sent. (appnote.txt says\n   * 3 but the actual value used is 4.)\n   */\n  for (max_blindex = BL_CODES - 1; max_blindex >= 3; max_blindex--) {\n    if (s.bl_tree[bl_order[max_blindex] * 2 + 1]/*.Len*/ !== 0) {\n      break;\n    }\n  }\n  /* Update opt_len to include the bit length tree and counts */\n  s.opt_len += 3 * (max_blindex + 1) + 5 + 5 + 4;\n  //Tracev((stderr, \"\\ndyn trees: dyn %ld, stat %ld\",\n  //        s->opt_len, s->static_len));\n\n  return max_blindex;\n}\n\n\n/* ===========================================================================\n * Send the header for a block using dynamic Huffman trees: the counts, the\n * lengths of the bit length codes, the literal tree and the distance tree.\n * IN assertion: lcodes >= 257, dcodes >= 1, blcodes >= 4.\n */\nfunction send_all_trees(s, lcodes, dcodes, blcodes)\n//    deflate_state *s;\n//    int lcodes, dcodes, blcodes; /* number of codes for each tree */\n{\n  var rank;                    /* index in bl_order */\n\n  //Assert (lcodes >= 257 && dcodes >= 1 && blcodes >= 4, \"not enough codes\");\n  //Assert (lcodes <= L_CODES && dcodes <= D_CODES && blcodes <= BL_CODES,\n  //        \"too many codes\");\n  //Tracev((stderr, \"\\nbl counts: \"));\n  send_bits(s, lcodes - 257, 5); /* not +255 as stated in appnote.txt */\n  send_bits(s, dcodes - 1,   5);\n  send_bits(s, blcodes - 4,  4); /* not -3 as stated in appnote.txt */\n  for (rank = 0; rank < blcodes; rank++) {\n    //Tracev((stderr, \"\\nbl code %2d \", bl_order[rank]));\n    send_bits(s, s.bl_tree[bl_order[rank] * 2 + 1]/*.Len*/, 3);\n  }\n  //Tracev((stderr, \"\\nbl tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_ltree, lcodes - 1); /* literal tree */\n  //Tracev((stderr, \"\\nlit tree: sent %ld\", s->bits_sent));\n\n  send_tree(s, s.dyn_dtree, dcodes - 1); /* distance tree */\n  //Tracev((stderr, \"\\ndist tree: sent %ld\", s->bits_sent));\n}\n\n\n/* ===========================================================================\n * Check if the data type is TEXT or BINARY, using the following algorithm:\n * - TEXT if the two conditions below are satisfied:\n *    a) There are no non-portable control characters belonging to the\n *       \"black list\" (0..6, 14..25, 28..31).\n *    b) There is at least one printable character belonging to the\n *       \"white list\" (9 {TAB}, 10 {LF}, 13 {CR}, 32..255).\n * - BINARY otherwise.\n * - The following partially-portable control characters form a\n *   \"gray list\" that is ignored in this detection algorithm:\n *   (7 {BEL}, 8 {BS}, 11 {VT}, 12 {FF}, 26 {SUB}, 27 {ESC}).\n * IN assertion: the fields Freq of dyn_ltree are set.\n */\nfunction detect_data_type(s) {\n  /* black_mask is the bit mask of black-listed bytes\n   * set bits 0..6, 14..25, and 28..31\n   * 0xf3ffc07f = binary 11110011111111111100000001111111\n   */\n  var black_mask = 0xf3ffc07f;\n  var n;\n\n  /* Check for non-textual (\"black-listed\") bytes. */\n  for (n = 0; n <= 31; n++, black_mask >>>= 1) {\n    if ((black_mask & 1) && (s.dyn_ltree[n * 2]/*.Freq*/ !== 0)) {\n      return Z_BINARY;\n    }\n  }\n\n  /* Check for textual (\"white-listed\") bytes. */\n  if (s.dyn_ltree[9 * 2]/*.Freq*/ !== 0 || s.dyn_ltree[10 * 2]/*.Freq*/ !== 0 ||\n      s.dyn_ltree[13 * 2]/*.Freq*/ !== 0) {\n    return Z_TEXT;\n  }\n  for (n = 32; n < LITERALS; n++) {\n    if (s.dyn_ltree[n * 2]/*.Freq*/ !== 0) {\n      return Z_TEXT;\n    }\n  }\n\n  /* There are no \"black-listed\" or \"white-listed\" bytes:\n   * this stream either is empty or has tolerated (\"gray-listed\") bytes only.\n   */\n  return Z_BINARY;\n}\n\n\nvar static_init_done = false;\n\n/* ===========================================================================\n * Initialize the tree data structures for a new zlib stream.\n */\nfunction _tr_init(s)\n{\n\n  if (!static_init_done) {\n    tr_static_init();\n    static_init_done = true;\n  }\n\n  s.l_desc  = new TreeDesc(s.dyn_ltree, static_l_desc);\n  s.d_desc  = new TreeDesc(s.dyn_dtree, static_d_desc);\n  s.bl_desc = new TreeDesc(s.bl_tree, static_bl_desc);\n\n  s.bi_buf = 0;\n  s.bi_valid = 0;\n\n  /* Initialize the first block of the first file: */\n  init_block(s);\n}\n\n\n/* ===========================================================================\n * Send a stored block\n */\nfunction _tr_stored_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  send_bits(s, (STORED_BLOCK << 1) + (last ? 1 : 0), 3);    /* send block type */\n  copy_block(s, buf, stored_len, true); /* with header */\n}\n\n\n/* ===========================================================================\n * Send one empty static block to give enough lookahead for inflate.\n * This takes 10 bits, of which 7 may remain in the bit buffer.\n */\nfunction _tr_align(s) {\n  send_bits(s, STATIC_TREES << 1, 3);\n  send_code(s, END_BLOCK, static_ltree);\n  bi_flush(s);\n}\n\n\n/* ===========================================================================\n * Determine the best encoding for the current block: dynamic trees, static\n * trees or store, and output the encoded block to the zip file.\n */\nfunction _tr_flush_block(s, buf, stored_len, last)\n//DeflateState *s;\n//charf *buf;       /* input block, or NULL if too old */\n//ulg stored_len;   /* length of input block */\n//int last;         /* one if this is the last block for a file */\n{\n  var opt_lenb, static_lenb;  /* opt_len and static_len in bytes */\n  var max_blindex = 0;        /* index of last bit length code of non zero freq */\n\n  /* Build the Huffman trees unless a stored block is forced */\n  if (s.level > 0) {\n\n    /* Check if the file is binary or text */\n    if (s.strm.data_type === Z_UNKNOWN) {\n      s.strm.data_type = detect_data_type(s);\n    }\n\n    /* Construct the literal and distance trees */\n    build_tree(s, s.l_desc);\n    // Tracev((stderr, \"\\nlit data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n\n    build_tree(s, s.d_desc);\n    // Tracev((stderr, \"\\ndist data: dyn %ld, stat %ld\", s->opt_len,\n    //        s->static_len));\n    /* At this point, opt_len and static_len are the total bit lengths of\n     * the compressed block data, excluding the tree representations.\n     */\n\n    /* Build the bit length tree for the above two trees, and get the index\n     * in bl_order of the last bit length code to send.\n     */\n    max_blindex = build_bl_tree(s);\n\n    /* Determine the best encoding. Compute the block lengths in bytes. */\n    opt_lenb = (s.opt_len + 3 + 7) >>> 3;\n    static_lenb = (s.static_len + 3 + 7) >>> 3;\n\n    // Tracev((stderr, \"\\nopt %lu(%lu) stat %lu(%lu) stored %lu lit %u \",\n    //        opt_lenb, s->opt_len, static_lenb, s->static_len, stored_len,\n    //        s->last_lit));\n\n    if (static_lenb <= opt_lenb) { opt_lenb = static_lenb; }\n\n  } else {\n    // Assert(buf != (char*)0, \"lost buf\");\n    opt_lenb = static_lenb = stored_len + 5; /* force a stored block */\n  }\n\n  if ((stored_len + 4 <= opt_lenb) && (buf !== -1)) {\n    /* 4: two words for the lengths */\n\n    /* The test buf != NULL is only necessary if LIT_BUFSIZE > WSIZE.\n     * Otherwise we can't have processed more than WSIZE input bytes since\n     * the last block flush, because compression would have been\n     * successful. If LIT_BUFSIZE <= WSIZE, it is never too late to\n     * transform a block into a stored block.\n     */\n    _tr_stored_block(s, buf, stored_len, last);\n\n  } else if (s.strategy === Z_FIXED || static_lenb === opt_lenb) {\n\n    send_bits(s, (STATIC_TREES << 1) + (last ? 1 : 0), 3);\n    compress_block(s, static_ltree, static_dtree);\n\n  } else {\n    send_bits(s, (DYN_TREES << 1) + (last ? 1 : 0), 3);\n    send_all_trees(s, s.l_desc.max_code + 1, s.d_desc.max_code + 1, max_blindex + 1);\n    compress_block(s, s.dyn_ltree, s.dyn_dtree);\n  }\n  // Assert (s->compressed_len == s->bits_sent, \"bad compressed size\");\n  /* The above check is made mod 2^32, for files larger than 512 MB\n   * and uLong implemented on 32 bits.\n   */\n  init_block(s);\n\n  if (last) {\n    bi_windup(s);\n  }\n  // Tracev((stderr,\"\\ncomprlen %lu(%lu) \", s->compressed_len>>3,\n  //       s->compressed_len-7*last));\n}\n\n/* ===========================================================================\n * Save the match info and tally the frequency counts. Return true if\n * the current block must be flushed.\n */\nfunction _tr_tally(s, dist, lc)\n//    deflate_state *s;\n//    unsigned dist;  /* distance of matched string */\n//    unsigned lc;    /* match length-MIN_MATCH or unmatched char (if dist==0) */\n{\n  //var out_length, in_length, dcode;\n\n  s.pending_buf[s.d_buf + s.last_lit * 2]     = (dist >>> 8) & 0xff;\n  s.pending_buf[s.d_buf + s.last_lit * 2 + 1] = dist & 0xff;\n\n  s.pending_buf[s.l_buf + s.last_lit] = lc & 0xff;\n  s.last_lit++;\n\n  if (dist === 0) {\n    /* lc is the unmatched char */\n    s.dyn_ltree[lc * 2]/*.Freq*/++;\n  } else {\n    s.matches++;\n    /* Here, lc is the match length - MIN_MATCH */\n    dist--;             /* dist = match distance - 1 */\n    //Assert((ush)dist < (ush)MAX_DIST(s) &&\n    //       (ush)lc <= (ush)(MAX_MATCH-MIN_MATCH) &&\n    //       (ush)d_code(dist) < (ush)D_CODES,  \"_tr_tally: bad match\");\n\n    s.dyn_ltree[(_length_code[lc] + LITERALS + 1) * 2]/*.Freq*/++;\n    s.dyn_dtree[d_code(dist) * 2]/*.Freq*/++;\n  }\n\n// (!) This block is disabled in zlib defaults,\n// don't enable it for binary compatibility\n\n//#ifdef TRUNCATE_BLOCK\n//  /* Try to guess if it is profitable to stop the current block here */\n//  if ((s.last_lit & 0x1fff) === 0 && s.level > 2) {\n//    /* Compute an upper bound for the compressed length */\n//    out_length = s.last_lit*8;\n//    in_length = s.strstart - s.block_start;\n//\n//    for (dcode = 0; dcode < D_CODES; dcode++) {\n//      out_length += s.dyn_dtree[dcode*2]/*.Freq*/ * (5 + extra_dbits[dcode]);\n//    }\n//    out_length >>>= 3;\n//    //Tracev((stderr,\"\\nlast_lit %u, in %ld, out ~%ld(%ld%%) \",\n//    //       s->last_lit, in_length, out_length,\n//    //       100L - out_length*100L/in_length));\n//    if (s.matches < (s.last_lit>>1)/*int /2*/ && out_length < (in_length>>1)/*int /2*/) {\n//      return true;\n//    }\n//  }\n//#endif\n\n  return (s.last_lit === s.lit_bufsize - 1);\n  /* We avoid equality with lit_bufsize because of wraparound at 64K\n   * on 16 bit machines and because stored blocks are restricted to\n   * 64K-1 bytes.\n   */\n}\n\nexports._tr_init  = _tr_init;\nexports._tr_stored_block = _tr_stored_block;\nexports._tr_flush_block  = _tr_flush_block;\nexports._tr_tally = _tr_tally;\nexports._tr_align = _tr_align;\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,kCAAkC,GAElC,IAAI;AAEJ,8EAA8E,GAC9E,8EAA8E,GAG9E,8BAA8B;AAC9B,8BAA8B;AAC9B,8BAA8B;AAC9B,IAAI,UAAwB;AAC5B,8BAA8B;AAE9B,iEAAiE,GACjE,IAAI,WAAwB;AAC5B,IAAI,SAAwB;AAC5B,0CAA0C;AAC1C,IAAI,YAAwB;AAE5B,8EAA8E,GAG9E,SAAS,KAAK,GAAG;IAAI,IAAI,MAAM,IAAI,MAAM;IAAE,MAAO,EAAE,OAAO,EAAG;QAAE,GAAG,CAAC,IAAI,GAAG;IAAG;AAAE;AAEhF,eAAe;AAEf,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,YAAe;AACnB,iCAAiC,GAEjC,IAAI,YAAe;AACnB,IAAI,YAAe;AACnB,yCAAyC,GAEzC,iBAAiB;AACjB;;CAEC,GAED,IAAI,eAAgB;AACpB,mEAAmE,GAEnE,IAAI,WAAgB;AACpB,kCAAkC,GAElC,IAAI,UAAgB,WAAW,IAAI;AACnC,mEAAmE,GAEnE,IAAI,UAAgB;AACpB,4BAA4B,GAE5B,IAAI,WAAgB;AACpB,oDAAoD,GAEpD,IAAI,YAAgB,IAAI,UAAU;AAClC,qBAAqB,GAErB,IAAI,WAAgB;AACpB,2CAA2C,GAE3C,IAAI,WAAgB;AACpB,gCAAgC,GAGhC;;CAEC,GAED,IAAI,cAAc;AAClB,qDAAqD,GAErD,IAAI,YAAc;AAClB,6BAA6B,GAE7B,IAAI,UAAc;AAClB,iEAAiE,GAEjE,IAAI,YAAc;AAClB,6DAA6D,GAE7D,IAAI,cAAc;AAClB,+DAA+D,GAE/D,sDAAsD,GACtD,IAAI,cAAgB,mCAAmC,GACrD;IAAC;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;CAAE;AAE7D,IAAI,cAAgB,qCAAqC,GACvD;IAAC;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;CAAG;AAEvE,IAAI,eAAgB,uCAAuC,GACzD;IAAC;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;IAAE;CAAE;AAEzC,IAAI,WACF;IAAC;IAAG;IAAG;IAAG;IAAE;IAAE;IAAE;IAAE;IAAE;IAAG;IAAE;IAAG;IAAE;IAAG;IAAE;IAAG;IAAE;IAAG;IAAE;CAAG;AAClD,qDAAqD,GAErD;;CAEC,GAED;;CAEC,GAED,wDAAwD;AAExD,IAAI,gBAAgB,KAAK,2CAA2C;AAEpE,oEAAoE;AACpE,IAAI,eAAgB,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI;AAC9C,KAAK;AACL;;;;CAIC,GAED,IAAI,eAAgB,IAAI,MAAM,UAAU;AACxC,KAAK;AACL;;CAEC,GAED,IAAI,aAAgB,IAAI,MAAM;AAC9B,KAAK;AACL;;;CAGC,GAED,IAAI,eAAgB,IAAI,MAAM,YAAY,YAAY;AACtD,KAAK;AACL,iEAAiE,GAEjE,IAAI,cAAgB,IAAI,MAAM;AAC9B,KAAK;AACL,yDAAyD,GAEzD,IAAI,YAAgB,IAAI,MAAM;AAC9B,KAAK;AACL,+DAA+D,GAG/D,SAAS,eAAe,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU;IAE5E,IAAI,CAAC,WAAW,GAAI,aAAc,uBAAuB;IACzD,IAAI,CAAC,UAAU,GAAK,YAAc,oCAAoC;IACtE,IAAI,CAAC,UAAU,GAAK,YAAc,6BAA6B;IAC/D,IAAI,CAAC,KAAK,GAAU,OAAc,sCAAsC;IACxE,IAAI,CAAC,UAAU,GAAK,YAAc,gCAAgC;IAElE,2EAA2E;IAC3E,IAAI,CAAC,SAAS,GAAM,eAAe,YAAY,MAAM;AACvD;AAGA,IAAI;AACJ,IAAI;AACJ,IAAI;AAGJ,SAAS,SAAS,QAAQ,EAAE,SAAS;IACnC,IAAI,CAAC,QAAQ,GAAG,UAAc,oBAAoB;IAClD,IAAI,CAAC,QAAQ,GAAG,GAAc,wCAAwC;IACtE,IAAI,CAAC,SAAS,GAAG,WAAa,iCAAiC;AACjE;AAIA,SAAS,OAAO,IAAI;IAClB,OAAO,OAAO,MAAM,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;AACvE;AAGA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,CAAC;IACvB,qCAAqC;IACrC,wCAAwC;IACtC,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,AAAC,IAAK;IACnC,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,AAAC,MAAM,IAAK;AAC3C;AAGA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,KAAK,EAAE,MAAM;IACjC,IAAI,EAAE,QAAQ,GAAI,WAAW,QAAS;QACpC,EAAE,MAAM,IAAI,AAAC,SAAS,EAAE,QAAQ,GAAI;QACpC,UAAU,GAAG,EAAE,MAAM;QACrB,EAAE,MAAM,GAAG,SAAU,WAAW,EAAE,QAAQ;QAC1C,EAAE,QAAQ,IAAI,SAAS;IACzB,OAAO;QACL,EAAE,MAAM,IAAI,AAAC,SAAS,EAAE,QAAQ,GAAI;QACpC,EAAE,QAAQ,IAAI;IAChB;AACF;AAGA,SAAS,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI;IAC3B,UAAU,GAAG,IAAI,CAAC,IAAI,EAAE,EAAW,IAAI,CAAC,IAAI,IAAI,EAAE;AACpD;AAGA;;;;CAIC,GACD,SAAS,WAAW,IAAI,EAAE,GAAG;IAC3B,IAAI,MAAM;IACV,GAAG;QACD,OAAO,OAAO;QACd,UAAU;QACV,QAAQ;IACV,QAAS,EAAE,MAAM,EAAG;IACpB,OAAO,QAAQ;AACjB;AAGA;;CAEC,GACD,SAAS,SAAS,CAAC;IACjB,IAAI,EAAE,QAAQ,KAAK,IAAI;QACrB,UAAU,GAAG,EAAE,MAAM;QACrB,EAAE,MAAM,GAAG;QACX,EAAE,QAAQ,GAAG;IAEf,OAAO,IAAI,EAAE,QAAQ,IAAI,GAAG;QAC1B,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,EAAE,MAAM,GAAG;QACxC,EAAE,MAAM,KAAK;QACb,EAAE,QAAQ,IAAI;IAChB;AACF;AAGA;;;;;;;;;CASC,GACD,SAAS,WAAW,CAAC,EAAE,IAAI,EAC3B,uBAAuB;AACvB,mDAAmD;;IAEjD,IAAI,OAAkB,KAAK,QAAQ;IACnC,IAAI,WAAkB,KAAK,QAAQ;IACnC,IAAI,QAAkB,KAAK,SAAS,CAAC,WAAW;IAChD,IAAI,YAAkB,KAAK,SAAS,CAAC,SAAS;IAC9C,IAAI,QAAkB,KAAK,SAAS,CAAC,UAAU;IAC/C,IAAI,OAAkB,KAAK,SAAS,CAAC,UAAU;IAC/C,IAAI,aAAkB,KAAK,SAAS,CAAC,UAAU;IAC/C,IAAI,GAAgB,cAAc;IAClC,IAAI,GAAG,GAAa,kCAAkC;IACtD,IAAI,MAAgB,cAAc;IAClC,IAAI,OAAgB,cAAc;IAClC,IAAI,GAAgB,aAAa;IACjC,IAAI,WAAW,GAAK,gDAAgD;IAEpE,IAAK,OAAO,GAAG,QAAQ,UAAU,OAAQ;QACvC,EAAE,QAAQ,CAAC,KAAK,GAAG;IACrB;IAEA;;GAEC,GACD,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,EAAE,GAAW,GAAG,oBAAoB;IAElE,IAAK,IAAI,EAAE,QAAQ,GAAG,GAAG,IAAI,WAAW,IAAK;QAC3C,IAAI,EAAE,IAAI,CAAC,EAAE;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW,IAAI,EAAE,GAAW;QACvD,IAAI,OAAO,YAAY;YACrB,OAAO;YACP;QACF;QACA,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW;QAC1B,sDAAsD,GAEtD,IAAI,IAAI,UAAU;YAAE;QAAU,EAAE,mBAAmB;QAEnD,EAAE,QAAQ,CAAC,KAAK;QAChB,QAAQ;QACR,IAAI,KAAK,MAAM;YACb,QAAQ,KAAK,CAAC,IAAI,KAAK;QACzB;QACA,IAAI,IAAI,CAAC,IAAI,EAAE,CAAA,OAAO;QACtB,EAAE,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK;QAC9B,IAAI,WAAW;YACb,EAAE,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,GAAW,KAAK;QACvD;IACF;IACA,IAAI,aAAa,GAAG;QAAE;IAAQ;IAE9B,6CAA6C;IAC7C,kEAAkE,GAElE,mDAAmD,GACnD,GAAG;QACD,OAAO,aAAa;QACpB,MAAO,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAG;YAAE;QAAQ;QACzC,EAAE,QAAQ,CAAC,KAAK,IAAS,+BAA+B;QACxD,EAAE,QAAQ,CAAC,OAAO,EAAE,IAAI,GAAG,yCAAyC;QACpE,EAAE,QAAQ,CAAC,WAAW;QACtB;;KAEC,GACD,YAAY;IACd,QAAS,WAAW,EAAG;IAEvB;;;;GAIC,GACD,IAAK,OAAO,YAAY,SAAS,GAAG,OAAQ;QAC1C,IAAI,EAAE,QAAQ,CAAC,KAAK;QACpB,MAAO,MAAM,EAAG;YACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;YACf,IAAI,IAAI,UAAU;gBAAE;YAAU;YAC9B,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,KAAa,MAAM;gBACpC,iEAAiE;gBACjE,EAAE,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,AAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAA,OAAO;gBAClE,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW;YAC5B;YACA;QACF;IACF;AACF;AAGA;;;;;;;CAOC,GACD,SAAS,UAAU,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAC3C,2DAA2D;AAC3D,2EAA2E;AAC3E,yEAAyE;;IAEvE,IAAI,YAAY,IAAI,MAAM,WAAW,IAAI,uCAAuC;IAChF,IAAI,OAAO,GAAgB,sBAAsB;IACjD,IAAI,MAAuB,aAAa;IACxC,IAAI,GAAuB,cAAc;IAEzC;;GAEC,GACD,IAAK,OAAO,GAAG,QAAQ,UAAU,OAAQ;QACvC,SAAS,CAAC,KAAK,GAAG,OAAO,AAAC,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAK;IAC1D;IACA;;GAEC,GACD,yDAAyD;IACzD,qCAAqC;IACrC,yDAAyD;IAEzD,IAAK,IAAI,GAAI,KAAK,UAAU,IAAK;QAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA,MAAM;QAC/B,IAAI,QAAQ,GAAG;YAAE;QAAU;QAC3B,wBAAwB,GACxB,IAAI,CAAC,IAAI,EAAE,GAAY,WAAW,SAAS,CAAC,IAAI,IAAI;IAEpD,uEAAuE;IACvE,wEAAwE;IAC1E;AACF;AAGA;;CAEC,GACD,SAAS;IACP,IAAI,GAAU,+BAA+B;IAC7C,IAAI,MAAU,eAAe;IAC7B,IAAI,QAAU,gBAAgB;IAC9B,IAAI,MAAU,cAAc;IAC5B,IAAI,MAAU,kBAAkB;IAChC,IAAI,WAAW,IAAI,MAAM,WAAW;IACpC,0DAA0D,GAE1D,yBAAyB;IACzB,+BAA+B;IAE/B,oEAAoE,GACtE;;;;;;MAMM,GAEJ,iEAAiE,GACjE,SAAS;IACT,IAAK,OAAO,GAAG,OAAO,eAAe,GAAG,OAAQ;QAC9C,WAAW,CAAC,KAAK,GAAG;QACpB,IAAK,IAAI,GAAG,IAAK,KAAK,WAAW,CAAC,KAAK,EAAG,IAAK;YAC7C,YAAY,CAAC,SAAS,GAAG;QAC3B;IACF;IACA,0DAA0D;IAC1D;;;GAGC,GACD,YAAY,CAAC,SAAS,EAAE,GAAG;IAE3B,6DAA6D,GAC7D,OAAO;IACP,IAAK,OAAO,GAAG,OAAO,IAAI,OAAQ;QAChC,SAAS,CAAC,KAAK,GAAG;QAClB,IAAK,IAAI,GAAG,IAAK,KAAK,WAAW,CAAC,KAAK,EAAG,IAAK;YAC7C,UAAU,CAAC,OAAO,GAAG;QACvB;IACF;IACA,sDAAsD;IACtD,SAAS,GAAG,iDAAiD;IAC7D,MAAO,OAAO,SAAS,OAAQ;QAC7B,SAAS,CAAC,KAAK,GAAG,QAAQ;QAC1B,IAAK,IAAI,GAAG,IAAK,KAAM,WAAW,CAAC,KAAK,GAAG,GAAK,IAAK;YACnD,UAAU,CAAC,MAAM,OAAO,GAAG;QAC7B;IACF;IACA,0DAA0D;IAE1D,kDAAkD,GAClD,IAAK,OAAO,GAAG,QAAQ,UAAU,OAAQ;QACvC,QAAQ,CAAC,KAAK,GAAG;IACnB;IAEA,IAAI;IACJ,MAAO,KAAK,IAAK;QACf,YAAY,CAAC,IAAI,IAAI,EAAE,GAAW;QAClC;QACA,QAAQ,CAAC,EAAE;IACb;IACA,MAAO,KAAK,IAAK;QACf,YAAY,CAAC,IAAI,IAAI,EAAE,GAAW;QAClC;QACA,QAAQ,CAAC,EAAE;IACb;IACA,MAAO,KAAK,IAAK;QACf,YAAY,CAAC,IAAI,IAAI,EAAE,GAAW;QAClC;QACA,QAAQ,CAAC,EAAE;IACb;IACA,MAAO,KAAK,IAAK;QACf,YAAY,CAAC,IAAI,IAAI,EAAE,GAAW;QAClC;QACA,QAAQ,CAAC,EAAE;IACb;IACA;;;GAGC,GACD,UAAU,cAAc,UAAU,GAAG;IAErC,wCAAwC,GACxC,IAAK,IAAI,GAAG,IAAI,SAAS,IAAK;QAC5B,YAAY,CAAC,IAAI,IAAI,EAAE,GAAW;QAClC,YAAY,CAAC,IAAI,EAAE,GAAY,WAAW,GAAG;IAC/C;IAEA,8CAA8C;IAC9C,gBAAgB,IAAI,eAAe,cAAc,aAAa,WAAW,GAAG,SAAS;IACrF,gBAAgB,IAAI,eAAe,cAAc,aAAa,GAAY,SAAS;IACnF,iBAAiB,IAAI,eAAe,IAAI,MAAM,IAAI,cAAc,GAAW,UAAU;AAErF,0BAA0B;AAC5B;AAGA;;CAEC,GACD,SAAS,WAAW,CAAC;IACnB,IAAI,GAAG,+BAA+B;IAEtC,yBAAyB,GACzB,IAAK,IAAI,GAAG,IAAI,SAAU,IAAK;QAAE,EAAE,SAAS,CAAC,IAAI,EAAE,GAAY;IAAG;IAClE,IAAK,IAAI,GAAG,IAAI,SAAU,IAAK;QAAE,EAAE,SAAS,CAAC,IAAI,EAAE,GAAY;IAAG;IAClE,IAAK,IAAI,GAAG,IAAI,UAAU,IAAK;QAAE,EAAE,OAAO,CAAC,IAAI,EAAE,GAAY;IAAG;IAEhE,EAAE,SAAS,CAAC,YAAY,EAAE,GAAY;IACtC,EAAE,OAAO,GAAG,EAAE,UAAU,GAAG;IAC3B,EAAE,QAAQ,GAAG,EAAE,OAAO,GAAG;AAC3B;AAGA;;CAEC,GACD,SAAS,UAAU,CAAC;IAElB,IAAI,EAAE,QAAQ,GAAG,GAAG;QAClB,UAAU,GAAG,EAAE,MAAM;IACvB,OAAO,IAAI,EAAE,QAAQ,GAAG,GAAG;QACzB,+BAA+B;QAC/B,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,EAAE,MAAM;IACvC;IACA,EAAE,MAAM,GAAG;IACX,EAAE,QAAQ,GAAG;AACf;AAEA;;;CAGC,GACD,SAAS,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EACvC,kBAAkB;AAClB,wCAAwC;AACxC,oCAAoC;AACpC,8DAA8D;;IAE5D,UAAU,IAAW,0BAA0B;IAE/C,IAAI,QAAQ;QACV,UAAU,GAAG;QACb,UAAU,GAAG,CAAC;IAChB;IACF,mBAAmB;IACnB,0BAA0B;IAC1B,KAAK;IACH,MAAM,QAAQ,CAAC,EAAE,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,KAAK,EAAE,OAAO;IAC3D,EAAE,OAAO,IAAI;AACf;AAEA;;;CAGC,GACD,SAAS,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;IAChC,IAAI,MAAM,IAAI;IACd,IAAI,MAAM,IAAI;IACd,OAAQ,IAAI,CAAC,IAAI,GAAY,IAAI,CAAC,IAAI,IAC9B,IAAI,CAAC,IAAI,KAAc,IAAI,CAAC,IAAI,IAAa,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;AAC3E;AAEA;;;;;CAKC,GACD,SAAS,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,EAC9B,uBAAuB;AACvB,+CAA+C;AAC/C,kDAAkD;;IAEhD,IAAI,IAAI,EAAE,IAAI,CAAC,EAAE;IACjB,IAAI,IAAI,KAAK,GAAI,iBAAiB;IAClC,MAAO,KAAK,EAAE,QAAQ,CAAE;QACtB,0CAA0C,GAC1C,IAAI,IAAI,EAAE,QAAQ,IAChB,QAAQ,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,GAAG;YAClD;QACF;QACA,uCAAuC,GACvC,IAAI,QAAQ,MAAM,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,GAAG;YAAE;QAAO;QAEnD,oCAAoC,GACpC,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;QACrB,IAAI;QAEJ,8DAA8D,GAC9D,MAAM;IACR;IACA,EAAE,IAAI,CAAC,EAAE,GAAG;AACd;AAGA,mBAAmB;AACnB,oBAAoB;AAEpB;;CAEC,GACD,SAAS,eAAe,CAAC,EAAE,KAAK,EAAE,KAAK,EACvC,uBAAuB;AACvB,8CAA8C;AAC9C,+CAA+C;;IAE7C,IAAI,MAAgB,8BAA8B;IAClD,IAAI,IAAgB,iDAAiD;IACrE,IAAI,KAAK,GAAW,0BAA0B;IAC9C,IAAI,MAAgB,oBAAoB;IACxC,IAAI,OAAgB,gCAAgC;IAEpD,IAAI,EAAE,QAAQ,KAAK,GAAG;QACpB,GAAG;YACD,OAAO,AAAC,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,IAAI,IAAM,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,KAAK,IAAI,EAAE;YACpF,KAAK,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,GAAG;YAChC;YAEA,IAAI,SAAS,GAAG;gBACd,UAAU,GAAG,IAAI,QAAQ,uBAAuB;YAChD,8CAA8C;YAChD,OAAO;gBACL,4CAA4C,GAC5C,OAAO,YAAY,CAAC,GAAG;gBACvB,UAAU,GAAG,OAAO,WAAW,GAAG,QAAQ,wBAAwB;gBAClE,QAAQ,WAAW,CAAC,KAAK;gBACzB,IAAI,UAAU,GAAG;oBACf,MAAM,WAAW,CAAC,KAAK;oBACvB,UAAU,GAAG,IAAI,QAAc,8BAA8B;gBAC/D;gBACA,QAAQ,sCAAsC;gBAC9C,OAAO,OAAO;gBACd,wCAAwC;gBAExC,UAAU,GAAG,MAAM,QAAc,0BAA0B;gBAC3D,QAAQ,WAAW,CAAC,KAAK;gBACzB,IAAI,UAAU,GAAG;oBACf,QAAQ,SAAS,CAAC,KAAK;oBACvB,UAAU,GAAG,MAAM,QAAU,gCAAgC;gBAC/D;YACF,EAAE,2BAA2B;QAE7B,qEAAqE,GACrE,oDAAoD;QACpD,gCAAgC;QAElC,QAAS,KAAK,EAAE,QAAQ,CAAE;IAC5B;IAEA,UAAU,GAAG,WAAW;AAC1B;AAGA;;;;;;;CAOC,GACD,SAAS,WAAW,CAAC,EAAE,IAAI,EAC3B,uBAAuB;AACvB,gDAAgD;;IAE9C,IAAI,OAAW,KAAK,QAAQ;IAC5B,IAAI,QAAW,KAAK,SAAS,CAAC,WAAW;IACzC,IAAI,YAAY,KAAK,SAAS,CAAC,SAAS;IACxC,IAAI,QAAW,KAAK,SAAS,CAAC,KAAK;IACnC,IAAI,GAAG,GAAY,8BAA8B;IACjD,IAAI,WAAW,CAAC,GAAG,wCAAwC;IAC3D,IAAI,MAAe,0BAA0B;IAE7C;;;GAGC,GACD,EAAE,QAAQ,GAAG;IACb,EAAE,QAAQ,GAAG;IAEb,IAAK,IAAI,GAAG,IAAI,OAAO,IAAK;QAC1B,IAAI,IAAI,CAAC,IAAI,EAAE,KAAc,GAAG;YAC9B,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,WAAW;YAClC,EAAE,KAAK,CAAC,EAAE,GAAG;QAEf,OAAO;YACL,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW;QAC5B;IACF;IAEA;;;;GAIC,GACD,MAAO,EAAE,QAAQ,GAAG,EAAG;QACrB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAI,WAAW,IAAI,EAAE,WAAW;QAC3D,IAAI,CAAC,OAAO,EAAE,GAAY;QAC1B,EAAE,KAAK,CAAC,KAAK,GAAG;QAChB,EAAE,OAAO;QAET,IAAI,WAAW;YACb,EAAE,UAAU,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE,CAAA,MAAM;QAC3C;IACA,iDAAiD,GACnD;IACA,KAAK,QAAQ,GAAG;IAEhB;;GAEC,GACD,IAAK,IAAK,EAAE,QAAQ,IAAI,EAAC,QAAQ,KAAK,KAAK,GAAG,IAAK;QAAE,WAAW,GAAG,MAAM;IAAI;IAE7E;;GAEC,GACD,OAAO,OAAoB,kCAAkC;IAC7D,GAAG;QACD,0DAA0D;QAC1D,gBAAgB,GAChB,IAAI,EAAE,IAAI,CAAC,EAAC,UAAU,IAAG;QACzB,EAAE,IAAI,CAAC,EAAC,UAAU,IAAG,GAAG,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;QAC5C,WAAW,GAAG,MAAM,EAAC,UAAU;QAC/B,GAAG,GAEH,IAAI,EAAE,IAAI,CAAC,EAAC,UAAU,IAAG,EAAE,oCAAoC;QAE/D,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,GAAG,sCAAsC;QAChE,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG;QAEvB,uCAAuC,GACvC,IAAI,CAAC,OAAO,EAAE,GAAY,IAAI,CAAC,IAAI,EAAE,GAAY,IAAI,CAAC,IAAI,EAAE,CAAA,OAAO;QACnE,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,IAAI;QACvE,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW,IAAI,CAAC,IAAI,IAAI,EAAE,GAAW;QAEpD,uCAAuC,GACvC,EAAE,IAAI,CAAC,EAAC,UAAU,IAAG,GAAG;QACxB,WAAW,GAAG,MAAM,EAAC,UAAU;IAEjC,QAAS,EAAE,QAAQ,IAAI,EAAG;IAE1B,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,EAAC,UAAU,IAAG;IAE5C;;GAEC,GACD,WAAW,GAAG;IAEd,2DAA2D,GAC3D,UAAU,MAAM,UAAU,EAAE,QAAQ;AACtC;AAGA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,IAAI,EAAE,QAAQ,EACpC,uBAAuB;AACvB,mDAAmD;AACnD,uEAAuE;;IAErE,IAAI,GAAuB,mCAAmC;IAC9D,IAAI,UAAU,CAAC,GAAY,uBAAuB;IAClD,IAAI,QAAuB,0BAA0B;IAErD,IAAI,UAAU,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA,MAAM,KAAI,uBAAuB;IAE9D,IAAI,QAAQ,GAAe,oCAAoC;IAC/D,IAAI,YAAY,GAAW,oBAAoB;IAC/C,IAAI,YAAY,GAAW,oBAAoB;IAE/C,IAAI,YAAY,GAAG;QACjB,YAAY;QACZ,YAAY;IACd;IACA,IAAI,CAAC,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,GAAW,QAAQ,SAAS;IAExD,IAAK,IAAI,GAAG,KAAK,UAAU,IAAK;QAC9B,SAAS;QACT,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA,MAAM;QAErC,IAAI,EAAE,QAAQ,aAAa,WAAW,SAAS;YAC7C;QAEF,OAAO,IAAI,QAAQ,WAAW;YAC5B,EAAE,OAAO,CAAC,SAAS,EAAE,IAAa;QAEpC,OAAO,IAAI,WAAW,GAAG;YAEvB,IAAI,WAAW,SAAS;gBAAE,EAAE,OAAO,CAAC,SAAS,EAAE;YAAa;YAC5D,EAAE,OAAO,CAAC,UAAU,EAAE;QAExB,OAAO,IAAI,SAAS,IAAI;YACtB,EAAE,OAAO,CAAC,YAAY,EAAE;QAE1B,OAAO;YACL,EAAE,OAAO,CAAC,cAAc,EAAE;QAC5B;QAEA,QAAQ;QACR,UAAU;QAEV,IAAI,YAAY,GAAG;YACjB,YAAY;YACZ,YAAY;QAEd,OAAO,IAAI,WAAW,SAAS;YAC7B,YAAY;YACZ,YAAY;QAEd,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF;AACF;AAGA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,IAAI,EAAE,QAAQ,EACpC,uBAAuB;AACvB,iDAAiD;AACjD,0EAA0E;;IAExE,IAAI,GAAuB,mCAAmC;IAC9D,IAAI,UAAU,CAAC,GAAY,uBAAuB;IAClD,IAAI,QAAuB,0BAA0B;IAErD,IAAI,UAAU,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA,MAAM,KAAI,uBAAuB;IAE9D,IAAI,QAAQ,GAAe,oCAAoC;IAC/D,IAAI,YAAY,GAAW,oBAAoB;IAC/C,IAAI,YAAY,GAAW,oBAAoB;IAE/C,8BAA8B,GAAI,qBAAqB,GACvD,IAAI,YAAY,GAAG;QACjB,YAAY;QACZ,YAAY;IACd;IAEA,IAAK,IAAI,GAAG,KAAK,UAAU,IAAK;QAC9B,SAAS;QACT,UAAU,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAA,MAAM;QAErC,IAAI,EAAE,QAAQ,aAAa,WAAW,SAAS;YAC7C;QAEF,OAAO,IAAI,QAAQ,WAAW;YAC5B,GAAG;gBAAE,UAAU,GAAG,QAAQ,EAAE,OAAO;YAAG,QAAS,EAAE,UAAU,EAAG;QAEhE,OAAO,IAAI,WAAW,GAAG;YACvB,IAAI,WAAW,SAAS;gBACtB,UAAU,GAAG,QAAQ,EAAE,OAAO;gBAC9B;YACF;YACA,4CAA4C;YAC5C,UAAU,GAAG,SAAS,EAAE,OAAO;YAC/B,UAAU,GAAG,QAAQ,GAAG;QAE1B,OAAO,IAAI,SAAS,IAAI;YACtB,UAAU,GAAG,WAAW,EAAE,OAAO;YACjC,UAAU,GAAG,QAAQ,GAAG;QAE1B,OAAO;YACL,UAAU,GAAG,aAAa,EAAE,OAAO;YACnC,UAAU,GAAG,QAAQ,IAAI;QAC3B;QAEA,QAAQ;QACR,UAAU;QACV,IAAI,YAAY,GAAG;YACjB,YAAY;YACZ,YAAY;QAEd,OAAO,IAAI,WAAW,SAAS;YAC7B,YAAY;YACZ,YAAY;QAEd,OAAO;YACL,YAAY;YACZ,YAAY;QACd;IACF;AACF;AAGA;;;CAGC,GACD,SAAS,cAAc,CAAC;IACtB,IAAI,aAAc,kDAAkD;IAEpE,uEAAuE,GACvE,UAAU,GAAG,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,QAAQ;IAC3C,UAAU,GAAG,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,QAAQ;IAE3C,8BAA8B,GAC9B,WAAW,GAAG,EAAE,OAAO;IACvB;;GAEC,GAED;;;GAGC,GACD,IAAK,cAAc,WAAW,GAAG,eAAe,GAAG,cAAe;QAChE,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,EAAE,KAAa,GAAG;YAC1D;QACF;IACF;IACA,4DAA4D,GAC5D,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI;IAC7C,mDAAmD;IACnD,sCAAsC;IAEtC,OAAO;AACT;AAGA;;;;CAIC,GACD,SAAS,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAClD,uBAAuB;AACvB,sEAAsE;;IAEpE,IAAI,MAAyB,qBAAqB;IAElD,4EAA4E;IAC5E,wEAAwE;IACxE,4BAA4B;IAC5B,oCAAoC;IACpC,UAAU,GAAG,SAAS,KAAK,IAAI,qCAAqC;IACpE,UAAU,GAAG,SAAS,GAAK;IAC3B,UAAU,GAAG,UAAU,GAAI,IAAI,mCAAmC;IAClE,IAAK,OAAO,GAAG,OAAO,SAAS,OAAQ;QACrC,qDAAqD;QACrD,UAAU,GAAG,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,EAAE,EAAU;IAC1D;IACA,wDAAwD;IAExD,UAAU,GAAG,EAAE,SAAS,EAAE,SAAS,IAAI,gBAAgB;IACvD,yDAAyD;IAEzD,UAAU,GAAG,EAAE,SAAS,EAAE,SAAS,IAAI,iBAAiB;AACxD,0DAA0D;AAC5D;AAGA;;;;;;;;;;;;CAYC,GACD,SAAS,iBAAiB,CAAC;IACzB;;;GAGC,GACD,IAAI,aAAa;IACjB,IAAI;IAEJ,iDAAiD,GACjD,IAAK,IAAI,GAAG,KAAK,IAAI,KAAK,gBAAgB,EAAG;QAC3C,IAAI,AAAC,aAAa,KAAO,EAAE,SAAS,CAAC,IAAI,EAAE,KAAc,GAAI;YAC3D,OAAO;QACT;IACF;IAEA,6CAA6C,GAC7C,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,KAAc,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,KAAc,KACtE,EAAE,SAAS,CAAC,KAAK,EAAE,KAAc,GAAG;QACtC,OAAO;IACT;IACA,IAAK,IAAI,IAAI,IAAI,UAAU,IAAK;QAC9B,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,KAAc,GAAG;YACrC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO;AACT;AAGA,IAAI,mBAAmB;AAEvB;;CAEC,GACD,SAAS,SAAS,CAAC;IAGjB,IAAI,CAAC,kBAAkB;QACrB;QACA,mBAAmB;IACrB;IAEA,EAAE,MAAM,GAAI,IAAI,SAAS,EAAE,SAAS,EAAE;IACtC,EAAE,MAAM,GAAI,IAAI,SAAS,EAAE,SAAS,EAAE;IACtC,EAAE,OAAO,GAAG,IAAI,SAAS,EAAE,OAAO,EAAE;IAEpC,EAAE,MAAM,GAAG;IACX,EAAE,QAAQ,GAAG;IAEb,iDAAiD,GACjD,WAAW;AACb;AAGA;;CAEC,GACD,SAAS,iBAAiB,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAClD,kBAAkB;AAClB,qCAAqC;AACrC,+CAA+C;AAC/C,kEAAkE;;IAEhE,UAAU,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,IAAO,mBAAmB;IAC7E,WAAW,GAAG,KAAK,YAAY,OAAO,eAAe;AACvD;AAGA;;;CAGC,GACD,SAAS,UAAU,CAAC;IAClB,UAAU,GAAG,gBAAgB,GAAG;IAChC,UAAU,GAAG,WAAW;IACxB,SAAS;AACX;AAGA;;;CAGC,GACD,SAAS,gBAAgB,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EACjD,kBAAkB;AAClB,yDAAyD;AACzD,+CAA+C;AAC/C,kEAAkE;;IAEhE,IAAI,UAAU,aAAc,mCAAmC;IAC/D,IAAI,cAAc,GAAU,kDAAkD;IAE9E,2DAA2D,GAC3D,IAAI,EAAE,KAAK,GAAG,GAAG;QAEf,uCAAuC,GACvC,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,WAAW;YAClC,EAAE,IAAI,CAAC,SAAS,GAAG,iBAAiB;QACtC;QAEA,4CAA4C,GAC5C,WAAW,GAAG,EAAE,MAAM;QACtB,+DAA+D;QAC/D,0BAA0B;QAE1B,WAAW,GAAG,EAAE,MAAM;QACtB,gEAAgE;QAChE,0BAA0B;QAC1B;;KAEC,GAED;;KAEC,GACD,cAAc,cAAc;QAE5B,oEAAoE,GACpE,WAAW,AAAC,EAAE,OAAO,GAAG,IAAI,MAAO;QACnC,cAAc,AAAC,EAAE,UAAU,GAAG,IAAI,MAAO;QAEzC,qEAAqE;QACrE,uEAAuE;QACvE,wBAAwB;QAExB,IAAI,eAAe,UAAU;YAAE,WAAW;QAAa;IAEzD,OAAO;QACL,uCAAuC;QACvC,WAAW,cAAc,aAAa,GAAG,wBAAwB;IACnE;IAEA,IAAI,AAAC,aAAa,KAAK,YAAc,QAAQ,CAAC,GAAI;QAChD,gCAAgC,GAEhC;;;;;KAKC,GACD,iBAAiB,GAAG,KAAK,YAAY;IAEvC,OAAO,IAAI,EAAE,QAAQ,KAAK,WAAW,gBAAgB,UAAU;QAE7D,UAAU,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG;QACnD,eAAe,GAAG,cAAc;IAElC,OAAO;QACL,UAAU,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG;QAChD,eAAe,GAAG,EAAE,MAAM,CAAC,QAAQ,GAAG,GAAG,EAAE,MAAM,CAAC,QAAQ,GAAG,GAAG,cAAc;QAC9E,eAAe,GAAG,EAAE,SAAS,EAAE,EAAE,SAAS;IAC5C;IACA,qEAAqE;IACrE;;GAEC,GACD,WAAW;IAEX,IAAI,MAAM;QACR,UAAU;IACZ;AACA,+DAA+D;AAC/D,oCAAoC;AACtC;AAEA;;;CAGC,GACD,SAAS,UAAU,CAAC,EAAE,IAAI,EAAE,EAAE,EAC9B,uBAAuB;AACvB,sDAAsD;AACtD,iFAAiF;;IAE/E,mCAAmC;IAEnC,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG,EAAE,GAAO,AAAC,SAAS,IAAK;IAC7D,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO;IAErD,EAAE,WAAW,CAAC,EAAE,KAAK,GAAG,EAAE,QAAQ,CAAC,GAAG,KAAK;IAC3C,EAAE,QAAQ;IAEV,IAAI,SAAS,GAAG;QACd,4BAA4B,GAC5B,EAAE,SAAS,CAAC,KAAK,EAAE;IACrB,OAAO;QACL,EAAE,OAAO;QACT,4CAA4C,GAC5C,QAAoB,6BAA6B;QACjD,wCAAwC;QACxC,iDAAiD;QACjD,oEAAoE;QAEpE,EAAE,SAAS,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,EAAE;QAClD,EAAE,SAAS,CAAC,OAAO,QAAQ,EAAE;IAC/B;IAEF,+CAA+C;IAC/C,2CAA2C;IAE3C,uBAAuB;IACvB,yEAAyE;IACzE,qDAAqD;IACrD,4DAA4D;IAC5D,gCAAgC;IAChC,6CAA6C;IAC7C,EAAE;IACF,iDAAiD;IACjD,+EAA+E;IAC/E,OAAO;IACP,wBAAwB;IACxB,iEAAiE;IACjE,kDAAkD;IAClD,kDAAkD;IAClD,2FAA2F;IAC3F,oBAAoB;IACpB,OAAO;IACP,KAAK;IACL,QAAQ;IAEN,OAAQ,EAAE,QAAQ,KAAK,EAAE,WAAW,GAAG;AACvC;;;GAGC,GACH;AAEA,QAAQ,QAAQ,GAAI;AACpB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,eAAe,GAAI;AAC3B,QAAQ,SAAS,GAAG;AACpB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/adler32.js"], "sourcesContent": ["'use strict';\n\n// Note: adler32 takes 12% for level 0 and 2% for level 6.\n// It isn't worth it to make additional optimizations as in original.\n// Small size is preferable.\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nfunction adler32(adler, buf, len, pos) {\n  var s1 = (adler & 0xffff) |0,\n      s2 = ((adler >>> 16) & 0xffff) |0,\n      n = 0;\n\n  while (len !== 0) {\n    // Set limit ~ twice less than 5552, to keep\n    // s2 in 31-bits, because we force signed ints.\n    // in other case %= will fail.\n    n = len > 2000 ? 2000 : len;\n    len -= n;\n\n    do {\n      s1 = (s1 + buf[pos++]) |0;\n      s2 = (s2 + s1) |0;\n    } while (--n);\n\n    s1 %= 65521;\n    s2 %= 65521;\n  }\n\n  return (s1 | (s2 << 16)) |0;\n}\n\n\nmodule.exports = adler32;\n"], "names": [], "mappings": "AAAA;AAEA,0DAA0D;AAC1D,qEAAqE;AACrE,4BAA4B;AAE5B,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,SAAS,QAAQ,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,IAAI,KAAK,AAAC,QAAQ,SAAS,GACvB,KAAK,AAAE,UAAU,KAAM,SAAS,GAChC,IAAI;IAER,MAAO,QAAQ,EAAG;QAChB,4CAA4C;QAC5C,+CAA+C;QAC/C,8BAA8B;QAC9B,IAAI,MAAM,OAAO,OAAO;QACxB,OAAO;QAEP,GAAG;YACD,KAAK,AAAC,KAAK,GAAG,CAAC,MAAM,GAAG;YACxB,KAAK,AAAC,KAAK,KAAK;QAClB,QAAS,EAAE,EAAG;QAEd,MAAM;QACN,MAAM;IACR;IAEA,OAAO,AAAC,KAAM,MAAM,KAAM;AAC5B;AAGA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/crc32.js"], "sourcesContent": ["'use strict';\n\n// Note: we can't get significant speed boost here.\n// So write code to minimize size - no pregenerated tables\n// and array tools dependencies.\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n// Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n  var c, table = [];\n\n  for (var n = 0; n < 256; n++) {\n    c = n;\n    for (var k = 0; k < 8; k++) {\n      c = ((c & 1) ? (0xEDB88320 ^ (c >>> 1)) : (c >>> 1));\n    }\n    table[n] = c;\n  }\n\n  return table;\n}\n\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\n\n\nfunction crc32(crc, buf, len, pos) {\n  var t = crcTable,\n      end = pos + len;\n\n  crc ^= -1;\n\n  for (var i = pos; i < end; i++) {\n    crc = (crc >>> 8) ^ t[(crc ^ buf[i]) & 0xFF];\n  }\n\n  return (crc ^ (-1)); // >>> 0;\n}\n\n\nmodule.exports = crc32;\n"], "names": [], "mappings": "AAAA;AAEA,mDAAmD;AACnD,0DAA0D;AAC1D,gCAAgC;AAEhC,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,wDAAwD;AACxD,SAAS;IACP,IAAI,GAAG,QAAQ,EAAE;IAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAK,AAAC,IAAI,IAAM,aAAc,MAAM,IAAO,MAAM;QACnD;QACA,KAAK,CAAC,EAAE,GAAG;IACb;IAEA,OAAO;AACT;AAEA,8DAA8D;AAC9D,IAAI,WAAW;AAGf,SAAS,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,IAAI,IAAI,UACJ,MAAM,MAAM;IAEhB,OAAO,CAAC;IAER,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,IAAK;QAC9B,MAAM,AAAC,QAAQ,IAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,KAAK;IAC9C;IAEA,OAAQ,MAAO,CAAC,GAAK,SAAS;AAChC;AAGA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/messages.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nmodule.exports = {\n  2:      'need dictionary',     /* Z_NEED_DICT       2  */\n  1:      'stream end',          /* Z_STREAM_END      1  */\n  0:      '',                    /* Z_OK              0  */\n  '-1':   'file error',          /* Z_ERRNO         (-1) */\n  '-2':   'stream error',        /* Z_STREAM_ERROR  (-2) */\n  '-3':   'data error',          /* Z_DATA_ERROR    (-3) */\n  '-4':   'insufficient memory', /* Z_MEM_ERROR     (-4) */\n  '-5':   'buffer error',        /* Z_BUF_ERROR     (-5) */\n  '-6':   'incompatible version' /* Z_VERSION_ERROR (-6) */\n};\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,OAAO,OAAO,GAAG;IACf,GAAQ;IAAuB,wBAAwB,GACvD,GAAQ;IAAuB,wBAAwB,GACvD,GAAQ;IAAuB,wBAAwB,GACvD,MAAQ;IAAuB,wBAAwB,GACvD,MAAQ;IAAuB,wBAAwB,GACvD,MAAQ;IAAuB,wBAAwB,GACvD,MAAQ;IAAuB,wBAAwB,GACvD,MAAQ;IAAuB,wBAAwB,GACvD,MAAQ,uBAAuB,wBAAwB;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/deflate.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nvar utils   = require('../utils/common');\nvar trees   = require('./trees');\nvar adler32 = require('./adler32');\nvar crc32   = require('./crc32');\nvar msg     = require('./messages');\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n/* Allowed flush values; see deflate() and inflate() below for details */\nvar Z_NO_FLUSH      = 0;\nvar Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\nvar Z_FULL_FLUSH    = 3;\nvar Z_FINISH        = 4;\nvar Z_BLOCK         = 5;\n//var Z_TREES         = 6;\n\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\n//var Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR  = -2;\nvar Z_DATA_ERROR    = -3;\n//var Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR     = -5;\n//var Z_VERSION_ERROR = -6;\n\n\n/* compression levels */\n//var Z_NO_COMPRESSION      = 0;\n//var Z_BEST_SPEED          = 1;\n//var Z_BEST_COMPRESSION    = 9;\nvar Z_DEFAULT_COMPRESSION = -1;\n\n\nvar Z_FILTERED            = 1;\nvar Z_HUFFMAN_ONLY        = 2;\nvar Z_RLE                 = 3;\nvar Z_FIXED               = 4;\nvar Z_DEFAULT_STRATEGY    = 0;\n\n/* Possible values of the data_type field (though see inflate()) */\n//var Z_BINARY              = 0;\n//var Z_TEXT                = 1;\n//var Z_ASCII               = 1; // = Z_TEXT\nvar Z_UNKNOWN             = 2;\n\n\n/* The deflate compression method */\nvar Z_DEFLATED  = 8;\n\n/*============================================================================*/\n\n\nvar MAX_MEM_LEVEL = 9;\n/* Maximum value for memLevel in deflateInit2 */\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_MEM_LEVEL = 8;\n\n\nvar LENGTH_CODES  = 29;\n/* number of length codes, not counting the special END_BLOCK code */\nvar LITERALS      = 256;\n/* number of literal bytes 0..255 */\nvar L_CODES       = LITERALS + 1 + LENGTH_CODES;\n/* number of Literal or Length codes, including the END_BLOCK code */\nvar D_CODES       = 30;\n/* number of distance codes */\nvar BL_CODES      = 19;\n/* number of codes used to transfer the bit lengths */\nvar HEAP_SIZE     = 2 * L_CODES + 1;\n/* maximum heap size */\nvar MAX_BITS  = 15;\n/* All codes must not exceed MAX_BITS bits */\n\nvar MIN_MATCH = 3;\nvar MAX_MATCH = 258;\nvar MIN_LOOKAHEAD = (MAX_MATCH + MIN_MATCH + 1);\n\nvar PRESET_DICT = 0x20;\n\nvar INIT_STATE = 42;\nvar EXTRA_STATE = 69;\nvar NAME_STATE = 73;\nvar COMMENT_STATE = 91;\nvar HCRC_STATE = 103;\nvar BUSY_STATE = 113;\nvar FINISH_STATE = 666;\n\nvar BS_NEED_MORE      = 1; /* block not completed, need more input or more output */\nvar BS_BLOCK_DONE     = 2; /* block flush performed */\nvar BS_FINISH_STARTED = 3; /* finish started, need only more output at next deflate */\nvar BS_FINISH_DONE    = 4; /* finish done, accept no more input or output */\n\nvar OS_CODE = 0x03; // Unix :) . Don't detect, use this default.\n\nfunction err(strm, errorCode) {\n  strm.msg = msg[errorCode];\n  return errorCode;\n}\n\nfunction rank(f) {\n  return ((f) << 1) - ((f) > 4 ? 9 : 0);\n}\n\nfunction zero(buf) { var len = buf.length; while (--len >= 0) { buf[len] = 0; } }\n\n\n/* =========================================================================\n * Flush as much pending output as possible. All deflate() output goes\n * through this function so some applications may wish to modify it\n * to avoid allocating a large strm->output buffer and copying into it.\n * (See also read_buf()).\n */\nfunction flush_pending(strm) {\n  var s = strm.state;\n\n  //_tr_flush_bits(s);\n  var len = s.pending;\n  if (len > strm.avail_out) {\n    len = strm.avail_out;\n  }\n  if (len === 0) { return; }\n\n  utils.arraySet(strm.output, s.pending_buf, s.pending_out, len, strm.next_out);\n  strm.next_out += len;\n  s.pending_out += len;\n  strm.total_out += len;\n  strm.avail_out -= len;\n  s.pending -= len;\n  if (s.pending === 0) {\n    s.pending_out = 0;\n  }\n}\n\n\nfunction flush_block_only(s, last) {\n  trees._tr_flush_block(s, (s.block_start >= 0 ? s.block_start : -1), s.strstart - s.block_start, last);\n  s.block_start = s.strstart;\n  flush_pending(s.strm);\n}\n\n\nfunction put_byte(s, b) {\n  s.pending_buf[s.pending++] = b;\n}\n\n\n/* =========================================================================\n * Put a short in the pending buffer. The 16-bit value is put in MSB order.\n * IN assertion: the stream state is correct and there is enough room in\n * pending_buf.\n */\nfunction putShortMSB(s, b) {\n//  put_byte(s, (Byte)(b >> 8));\n//  put_byte(s, (Byte)(b & 0xff));\n  s.pending_buf[s.pending++] = (b >>> 8) & 0xff;\n  s.pending_buf[s.pending++] = b & 0xff;\n}\n\n\n/* ===========================================================================\n * Read a new buffer from the current input stream, update the adler32\n * and total number of bytes read.  All deflate() input goes through\n * this function so some applications may wish to modify it to avoid\n * allocating a large strm->input buffer and copying from it.\n * (See also flush_pending()).\n */\nfunction read_buf(strm, buf, start, size) {\n  var len = strm.avail_in;\n\n  if (len > size) { len = size; }\n  if (len === 0) { return 0; }\n\n  strm.avail_in -= len;\n\n  // zmemcpy(buf, strm->next_in, len);\n  utils.arraySet(buf, strm.input, strm.next_in, len, start);\n  if (strm.state.wrap === 1) {\n    strm.adler = adler32(strm.adler, buf, len, start);\n  }\n\n  else if (strm.state.wrap === 2) {\n    strm.adler = crc32(strm.adler, buf, len, start);\n  }\n\n  strm.next_in += len;\n  strm.total_in += len;\n\n  return len;\n}\n\n\n/* ===========================================================================\n * Set match_start to the longest match starting at the given string and\n * return its length. Matches shorter or equal to prev_length are discarded,\n * in which case the result is equal to prev_length and match_start is\n * garbage.\n * IN assertions: cur_match is the head of the hash chain for the current\n *   string (strstart) and its distance is <= MAX_DIST, and prev_length >= 1\n * OUT assertion: the match length is not greater than s->lookahead.\n */\nfunction longest_match(s, cur_match) {\n  var chain_length = s.max_chain_length;      /* max hash chain length */\n  var scan = s.strstart; /* current string */\n  var match;                       /* matched string */\n  var len;                           /* length of current match */\n  var best_len = s.prev_length;              /* best match length so far */\n  var nice_match = s.nice_match;             /* stop if match long enough */\n  var limit = (s.strstart > (s.w_size - MIN_LOOKAHEAD)) ?\n      s.strstart - (s.w_size - MIN_LOOKAHEAD) : 0/*NIL*/;\n\n  var _win = s.window; // shortcut\n\n  var wmask = s.w_mask;\n  var prev  = s.prev;\n\n  /* Stop when cur_match becomes <= limit. To simplify the code,\n   * we prevent matches with the string of window index 0.\n   */\n\n  var strend = s.strstart + MAX_MATCH;\n  var scan_end1  = _win[scan + best_len - 1];\n  var scan_end   = _win[scan + best_len];\n\n  /* The code is optimized for HASH_BITS >= 8 and MAX_MATCH-2 multiple of 16.\n   * It is easy to get rid of this optimization if necessary.\n   */\n  // Assert(s->hash_bits >= 8 && MAX_MATCH == 258, \"Code too clever\");\n\n  /* Do not waste too much time if we already have a good match: */\n  if (s.prev_length >= s.good_match) {\n    chain_length >>= 2;\n  }\n  /* Do not look for matches beyond the end of the input. This is necessary\n   * to make deflate deterministic.\n   */\n  if (nice_match > s.lookahead) { nice_match = s.lookahead; }\n\n  // Assert((ulg)s->strstart <= s->window_size-MIN_LOOKAHEAD, \"need lookahead\");\n\n  do {\n    // Assert(cur_match < s->strstart, \"no future\");\n    match = cur_match;\n\n    /* Skip to next match if the match length cannot increase\n     * or if the match length is less than 2.  Note that the checks below\n     * for insufficient lookahead only occur occasionally for performance\n     * reasons.  Therefore uninitialized memory will be accessed, and\n     * conditional jumps will be made that depend on those values.\n     * However the length of the match is limited to the lookahead, so\n     * the output of deflate is not affected by the uninitialized values.\n     */\n\n    if (_win[match + best_len]     !== scan_end  ||\n        _win[match + best_len - 1] !== scan_end1 ||\n        _win[match]                !== _win[scan] ||\n        _win[++match]              !== _win[scan + 1]) {\n      continue;\n    }\n\n    /* The check at best_len-1 can be removed because it will be made\n     * again later. (This heuristic is not always a win.)\n     * It is not necessary to compare scan[2] and match[2] since they\n     * are always equal when the other bytes match, given that\n     * the hash keys are equal and that HASH_BITS >= 8.\n     */\n    scan += 2;\n    match++;\n    // Assert(*scan == *match, \"match[2]?\");\n\n    /* We check for insufficient lookahead only every 8th comparison;\n     * the 256th check will be made at strstart+258.\n     */\n    do {\n      /*jshint noempty:false*/\n    } while (_win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             _win[++scan] === _win[++match] && _win[++scan] === _win[++match] &&\n             scan < strend);\n\n    // Assert(scan <= s->window+(unsigned)(s->window_size-1), \"wild scan\");\n\n    len = MAX_MATCH - (strend - scan);\n    scan = strend - MAX_MATCH;\n\n    if (len > best_len) {\n      s.match_start = cur_match;\n      best_len = len;\n      if (len >= nice_match) {\n        break;\n      }\n      scan_end1  = _win[scan + best_len - 1];\n      scan_end   = _win[scan + best_len];\n    }\n  } while ((cur_match = prev[cur_match & wmask]) > limit && --chain_length !== 0);\n\n  if (best_len <= s.lookahead) {\n    return best_len;\n  }\n  return s.lookahead;\n}\n\n\n/* ===========================================================================\n * Fill the window when the lookahead becomes insufficient.\n * Updates strstart and lookahead.\n *\n * IN assertion: lookahead < MIN_LOOKAHEAD\n * OUT assertions: strstart <= window_size-MIN_LOOKAHEAD\n *    At least one byte has been read, or avail_in == 0; reads are\n *    performed for at least two bytes (required for the zip translate_eol\n *    option -- not supported here).\n */\nfunction fill_window(s) {\n  var _w_size = s.w_size;\n  var p, n, m, more, str;\n\n  //Assert(s->lookahead < MIN_LOOKAHEAD, \"already enough lookahead\");\n\n  do {\n    more = s.window_size - s.lookahead - s.strstart;\n\n    // JS ints have 32 bit, block below not needed\n    /* Deal with !@#$% 64K limit: */\n    //if (sizeof(int) <= 2) {\n    //    if (more == 0 && s->strstart == 0 && s->lookahead == 0) {\n    //        more = wsize;\n    //\n    //  } else if (more == (unsigned)(-1)) {\n    //        /* Very unlikely, but possible on 16 bit machine if\n    //         * strstart == 0 && lookahead == 1 (input done a byte at time)\n    //         */\n    //        more--;\n    //    }\n    //}\n\n\n    /* If the window is almost full and there is insufficient lookahead,\n     * move the upper half to the lower one to make room in the upper half.\n     */\n    if (s.strstart >= _w_size + (_w_size - MIN_LOOKAHEAD)) {\n\n      utils.arraySet(s.window, s.window, _w_size, _w_size, 0);\n      s.match_start -= _w_size;\n      s.strstart -= _w_size;\n      /* we now have strstart >= MAX_DIST */\n      s.block_start -= _w_size;\n\n      /* Slide the hash table (could be avoided with 32 bit values\n       at the expense of memory usage). We slide even when level == 0\n       to keep the hash table consistent if we switch back to level > 0\n       later. (Using level 0 permanently is not an optimal usage of\n       zlib, so we don't care about this pathological case.)\n       */\n\n      n = s.hash_size;\n      p = n;\n      do {\n        m = s.head[--p];\n        s.head[p] = (m >= _w_size ? m - _w_size : 0);\n      } while (--n);\n\n      n = _w_size;\n      p = n;\n      do {\n        m = s.prev[--p];\n        s.prev[p] = (m >= _w_size ? m - _w_size : 0);\n        /* If n is not on any hash chain, prev[n] is garbage but\n         * its value will never be used.\n         */\n      } while (--n);\n\n      more += _w_size;\n    }\n    if (s.strm.avail_in === 0) {\n      break;\n    }\n\n    /* If there was no sliding:\n     *    strstart <= WSIZE+MAX_DIST-1 && lookahead <= MIN_LOOKAHEAD - 1 &&\n     *    more == window_size - lookahead - strstart\n     * => more >= window_size - (MIN_LOOKAHEAD-1 + WSIZE + MAX_DIST-1)\n     * => more >= window_size - 2*WSIZE + 2\n     * In the BIG_MEM or MMAP case (not yet supported),\n     *   window_size == input_size + MIN_LOOKAHEAD  &&\n     *   strstart + s->lookahead <= input_size => more >= MIN_LOOKAHEAD.\n     * Otherwise, window_size == 2*WSIZE so more >= 2.\n     * If there was sliding, more >= WSIZE. So in all cases, more >= 2.\n     */\n    //Assert(more >= 2, \"more < 2\");\n    n = read_buf(s.strm, s.window, s.strstart + s.lookahead, more);\n    s.lookahead += n;\n\n    /* Initialize the hash value now that we have some input: */\n    if (s.lookahead + s.insert >= MIN_MATCH) {\n      str = s.strstart - s.insert;\n      s.ins_h = s.window[str];\n\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + 1]); */\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + 1]) & s.hash_mask;\n//#if MIN_MATCH != 3\n//        Call update_hash() MIN_MATCH-3 more times\n//#endif\n      while (s.insert) {\n        /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n\n        s.prev[str & s.w_mask] = s.head[s.ins_h];\n        s.head[s.ins_h] = str;\n        str++;\n        s.insert--;\n        if (s.lookahead + s.insert < MIN_MATCH) {\n          break;\n        }\n      }\n    }\n    /* If the whole input has less than MIN_MATCH bytes, ins_h is garbage,\n     * but this is not important since only literal bytes will be emitted.\n     */\n\n  } while (s.lookahead < MIN_LOOKAHEAD && s.strm.avail_in !== 0);\n\n  /* If the WIN_INIT bytes after the end of the current data have never been\n   * written, then zero those bytes in order to avoid memory check reports of\n   * the use of uninitialized (or uninitialised as Julian writes) bytes by\n   * the longest match routines.  Update the high water mark for the next\n   * time through here.  WIN_INIT is set to MAX_MATCH since the longest match\n   * routines allow scanning to strstart + MAX_MATCH, ignoring lookahead.\n   */\n//  if (s.high_water < s.window_size) {\n//    var curr = s.strstart + s.lookahead;\n//    var init = 0;\n//\n//    if (s.high_water < curr) {\n//      /* Previous high water mark below current data -- zero WIN_INIT\n//       * bytes or up to end of window, whichever is less.\n//       */\n//      init = s.window_size - curr;\n//      if (init > WIN_INIT)\n//        init = WIN_INIT;\n//      zmemzero(s->window + curr, (unsigned)init);\n//      s->high_water = curr + init;\n//    }\n//    else if (s->high_water < (ulg)curr + WIN_INIT) {\n//      /* High water mark at or above current data, but below current data\n//       * plus WIN_INIT -- zero out to current data plus WIN_INIT, or up\n//       * to end of window, whichever is less.\n//       */\n//      init = (ulg)curr + WIN_INIT - s->high_water;\n//      if (init > s->window_size - s->high_water)\n//        init = s->window_size - s->high_water;\n//      zmemzero(s->window + s->high_water, (unsigned)init);\n//      s->high_water += init;\n//    }\n//  }\n//\n//  Assert((ulg)s->strstart <= s->window_size - MIN_LOOKAHEAD,\n//    \"not enough room for search\");\n}\n\n/* ===========================================================================\n * Copy without compression as much as possible from the input stream, return\n * the current block state.\n * This function does not insert new strings in the dictionary since\n * uncompressible data is probably not useful. This function is used\n * only for the level=0 compression option.\n * NOTE: this function should be optimized to avoid extra copying from\n * window to pending_buf.\n */\nfunction deflate_stored(s, flush) {\n  /* Stored blocks are limited to 0xffff bytes, pending_buf is limited\n   * to pending_buf_size, and each stored block has a 5 byte header:\n   */\n  var max_block_size = 0xffff;\n\n  if (max_block_size > s.pending_buf_size - 5) {\n    max_block_size = s.pending_buf_size - 5;\n  }\n\n  /* Copy as much as possible from input to output: */\n  for (;;) {\n    /* Fill the window as much as possible: */\n    if (s.lookahead <= 1) {\n\n      //Assert(s->strstart < s->w_size+MAX_DIST(s) ||\n      //  s->block_start >= (long)s->w_size, \"slide too late\");\n//      if (!(s.strstart < s.w_size + (s.w_size - MIN_LOOKAHEAD) ||\n//        s.block_start >= s.w_size)) {\n//        throw  new Error(\"slide too late\");\n//      }\n\n      fill_window(s);\n      if (s.lookahead === 0 && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n\n      if (s.lookahead === 0) {\n        break;\n      }\n      /* flush the current block */\n    }\n    //Assert(s->block_start >= 0L, \"block gone\");\n//    if (s.block_start < 0) throw new Error(\"block gone\");\n\n    s.strstart += s.lookahead;\n    s.lookahead = 0;\n\n    /* Emit a stored block if pending_buf will be full: */\n    var max_start = s.block_start + max_block_size;\n\n    if (s.strstart === 0 || s.strstart >= max_start) {\n      /* strstart == 0 is possible when wraparound on 16-bit machine */\n      s.lookahead = s.strstart - max_start;\n      s.strstart = max_start;\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n\n\n    }\n    /* Flush if we may have to slide, otherwise block_start may become\n     * negative and the data will be gone:\n     */\n    if (s.strstart - s.block_start >= (s.w_size - MIN_LOOKAHEAD)) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n\n  s.insert = 0;\n\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n\n  if (s.strstart > s.block_start) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_NEED_MORE;\n}\n\n/* ===========================================================================\n * Compress as much as possible from the input stream, return the current\n * block state.\n * This function does not perform lazy evaluation of matches and inserts\n * new strings in the dictionary only for unmatched strings or for short\n * matches. It is used only for the fast compression options.\n */\nfunction deflate_fast(s, flush) {\n  var hash_head;        /* head of the hash chain */\n  var bflush;           /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) {\n        break; /* flush the current block */\n      }\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     * At this point we have always match_length < MIN_MATCH\n     */\n    if (hash_head !== 0/*NIL*/ && ((s.strstart - hash_head) <= (s.w_size - MIN_LOOKAHEAD))) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n    }\n    if (s.match_length >= MIN_MATCH) {\n      // check_match(s, s.strstart, s.match_start, s.match_length); // for debug only\n\n      /*** _tr_tally_dist(s, s.strstart - s.match_start,\n                     s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, s.strstart - s.match_start, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n\n      /* Insert new strings in the hash table only if the match length\n       * is not too large. This saves time but degrades compression.\n       */\n      if (s.match_length <= s.max_lazy_match/*max_insert_length*/ && s.lookahead >= MIN_MATCH) {\n        s.match_length--; /* string at strstart already in table */\n        do {\n          s.strstart++;\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n          /* strstart never exceeds WSIZE-MAX_MATCH, so there are\n           * always MIN_MATCH bytes ahead.\n           */\n        } while (--s.match_length !== 0);\n        s.strstart++;\n      } else\n      {\n        s.strstart += s.match_length;\n        s.match_length = 0;\n        s.ins_h = s.window[s.strstart];\n        /* UPDATE_HASH(s, s.ins_h, s.window[s.strstart+1]); */\n        s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + 1]) & s.hash_mask;\n\n//#if MIN_MATCH != 3\n//                Call UPDATE_HASH() MIN_MATCH-3 more times\n//#endif\n        /* If lookahead < MIN_MATCH, ins_h is garbage, but it does not\n         * matter since it will be recomputed at next deflate call.\n         */\n      }\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s.window[s.strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = ((s.strstart < (MIN_MATCH - 1)) ? s.strstart : MIN_MATCH - 1);\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * Same as above, but achieves better compression. We use a lazy\n * evaluation for matches: a match is finally adopted only if there is\n * no better match at the next window position.\n */\nfunction deflate_slow(s, flush) {\n  var hash_head;          /* head of hash chain */\n  var bflush;              /* set if current block must be flushed */\n\n  var max_insert;\n\n  /* Process the input block. */\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the next match, plus MIN_MATCH bytes to insert the\n     * string following the next match.\n     */\n    if (s.lookahead < MIN_LOOKAHEAD) {\n      fill_window(s);\n      if (s.lookahead < MIN_LOOKAHEAD && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* Insert the string window[strstart .. strstart+2] in the\n     * dictionary, and set hash_head to the head of the hash chain:\n     */\n    hash_head = 0/*NIL*/;\n    if (s.lookahead >= MIN_MATCH) {\n      /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n      hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n      s.head[s.ins_h] = s.strstart;\n      /***/\n    }\n\n    /* Find the longest match, discarding those <= prev_length.\n     */\n    s.prev_length = s.match_length;\n    s.prev_match = s.match_start;\n    s.match_length = MIN_MATCH - 1;\n\n    if (hash_head !== 0/*NIL*/ && s.prev_length < s.max_lazy_match &&\n        s.strstart - hash_head <= (s.w_size - MIN_LOOKAHEAD)/*MAX_DIST(s)*/) {\n      /* To simplify the code, we prevent matches with the string\n       * of window index 0 (in particular we have to avoid a match\n       * of the string with itself at the start of the input file).\n       */\n      s.match_length = longest_match(s, hash_head);\n      /* longest_match() sets match_start */\n\n      if (s.match_length <= 5 &&\n         (s.strategy === Z_FILTERED || (s.match_length === MIN_MATCH && s.strstart - s.match_start > 4096/*TOO_FAR*/))) {\n\n        /* If prev_match is also MIN_MATCH, match_start is garbage\n         * but we will ignore the current match anyway.\n         */\n        s.match_length = MIN_MATCH - 1;\n      }\n    }\n    /* If there was a match at the previous step and the current\n     * match is not better, output the previous match:\n     */\n    if (s.prev_length >= MIN_MATCH && s.match_length <= s.prev_length) {\n      max_insert = s.strstart + s.lookahead - MIN_MATCH;\n      /* Do not insert strings in hash table beyond this. */\n\n      //check_match(s, s.strstart-1, s.prev_match, s.prev_length);\n\n      /***_tr_tally_dist(s, s.strstart - 1 - s.prev_match,\n                     s.prev_length - MIN_MATCH, bflush);***/\n      bflush = trees._tr_tally(s, s.strstart - 1 - s.prev_match, s.prev_length - MIN_MATCH);\n      /* Insert in hash table all strings up to the end of the match.\n       * strstart-1 and strstart are already inserted. If there is not\n       * enough lookahead, the last two strings are not inserted in\n       * the hash table.\n       */\n      s.lookahead -= s.prev_length - 1;\n      s.prev_length -= 2;\n      do {\n        if (++s.strstart <= max_insert) {\n          /*** INSERT_STRING(s, s.strstart, hash_head); ***/\n          s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[s.strstart + MIN_MATCH - 1]) & s.hash_mask;\n          hash_head = s.prev[s.strstart & s.w_mask] = s.head[s.ins_h];\n          s.head[s.ins_h] = s.strstart;\n          /***/\n        }\n      } while (--s.prev_length !== 0);\n      s.match_available = 0;\n      s.match_length = MIN_MATCH - 1;\n      s.strstart++;\n\n      if (bflush) {\n        /*** FLUSH_BLOCK(s, 0); ***/\n        flush_block_only(s, false);\n        if (s.strm.avail_out === 0) {\n          return BS_NEED_MORE;\n        }\n        /***/\n      }\n\n    } else if (s.match_available) {\n      /* If there was no match at the previous position, output a\n       * single literal. If there was a match but the current match\n       * is longer, truncate the previous match to a single literal.\n       */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n      /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n\n      if (bflush) {\n        /*** FLUSH_BLOCK_ONLY(s, 0) ***/\n        flush_block_only(s, false);\n        /***/\n      }\n      s.strstart++;\n      s.lookahead--;\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n    } else {\n      /* There is no previous match to compare with, wait for\n       * the next step to decide.\n       */\n      s.match_available = 1;\n      s.strstart++;\n      s.lookahead--;\n    }\n  }\n  //Assert (flush != Z_NO_FLUSH, \"no flush?\");\n  if (s.match_available) {\n    //Tracevv((stderr,\"%c\", s->window[s->strstart-1]));\n    /*** _tr_tally_lit(s, s.window[s.strstart-1], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart - 1]);\n\n    s.match_available = 0;\n  }\n  s.insert = s.strstart < MIN_MATCH - 1 ? s.strstart : MIN_MATCH - 1;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n\n  return BS_BLOCK_DONE;\n}\n\n\n/* ===========================================================================\n * For Z_RLE, simply look for runs of bytes, generate matches only of distance\n * one.  Do not maintain a hash table.  (It will be regenerated if this run of\n * deflate switches away from Z_RLE.)\n */\nfunction deflate_rle(s, flush) {\n  var bflush;            /* set if current block must be flushed */\n  var prev;              /* byte at distance one to match */\n  var scan, strend;      /* scan goes up to strend for length of run */\n\n  var _win = s.window;\n\n  for (;;) {\n    /* Make sure that we always have enough lookahead, except\n     * at the end of the input file. We need MAX_MATCH bytes\n     * for the longest run, plus one for the unrolled loop.\n     */\n    if (s.lookahead <= MAX_MATCH) {\n      fill_window(s);\n      if (s.lookahead <= MAX_MATCH && flush === Z_NO_FLUSH) {\n        return BS_NEED_MORE;\n      }\n      if (s.lookahead === 0) { break; } /* flush the current block */\n    }\n\n    /* See how many times the previous byte repeats */\n    s.match_length = 0;\n    if (s.lookahead >= MIN_MATCH && s.strstart > 0) {\n      scan = s.strstart - 1;\n      prev = _win[scan];\n      if (prev === _win[++scan] && prev === _win[++scan] && prev === _win[++scan]) {\n        strend = s.strstart + MAX_MATCH;\n        do {\n          /*jshint noempty:false*/\n        } while (prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 prev === _win[++scan] && prev === _win[++scan] &&\n                 scan < strend);\n        s.match_length = MAX_MATCH - (strend - scan);\n        if (s.match_length > s.lookahead) {\n          s.match_length = s.lookahead;\n        }\n      }\n      //Assert(scan <= s->window+(uInt)(s->window_size-1), \"wild scan\");\n    }\n\n    /* Emit match if have run of MIN_MATCH or longer, else emit literal */\n    if (s.match_length >= MIN_MATCH) {\n      //check_match(s, s.strstart, s.strstart - 1, s.match_length);\n\n      /*** _tr_tally_dist(s, 1, s.match_length - MIN_MATCH, bflush); ***/\n      bflush = trees._tr_tally(s, 1, s.match_length - MIN_MATCH);\n\n      s.lookahead -= s.match_length;\n      s.strstart += s.match_length;\n      s.match_length = 0;\n    } else {\n      /* No match, output a literal byte */\n      //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n      /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n      bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n\n      s.lookahead--;\n      s.strstart++;\n    }\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* ===========================================================================\n * For Z_HUFFMAN_ONLY, do not look for matches.  Do not maintain a hash table.\n * (It will be regenerated if this run of deflate switches away from Huffman.)\n */\nfunction deflate_huff(s, flush) {\n  var bflush;             /* set if current block must be flushed */\n\n  for (;;) {\n    /* Make sure that we have a literal to write. */\n    if (s.lookahead === 0) {\n      fill_window(s);\n      if (s.lookahead === 0) {\n        if (flush === Z_NO_FLUSH) {\n          return BS_NEED_MORE;\n        }\n        break;      /* flush the current block */\n      }\n    }\n\n    /* Output a literal byte */\n    s.match_length = 0;\n    //Tracevv((stderr,\"%c\", s->window[s->strstart]));\n    /*** _tr_tally_lit(s, s.window[s.strstart], bflush); ***/\n    bflush = trees._tr_tally(s, 0, s.window[s.strstart]);\n    s.lookahead--;\n    s.strstart++;\n    if (bflush) {\n      /*** FLUSH_BLOCK(s, 0); ***/\n      flush_block_only(s, false);\n      if (s.strm.avail_out === 0) {\n        return BS_NEED_MORE;\n      }\n      /***/\n    }\n  }\n  s.insert = 0;\n  if (flush === Z_FINISH) {\n    /*** FLUSH_BLOCK(s, 1); ***/\n    flush_block_only(s, true);\n    if (s.strm.avail_out === 0) {\n      return BS_FINISH_STARTED;\n    }\n    /***/\n    return BS_FINISH_DONE;\n  }\n  if (s.last_lit) {\n    /*** FLUSH_BLOCK(s, 0); ***/\n    flush_block_only(s, false);\n    if (s.strm.avail_out === 0) {\n      return BS_NEED_MORE;\n    }\n    /***/\n  }\n  return BS_BLOCK_DONE;\n}\n\n/* Values for max_lazy_match, good_match and max_chain_length, depending on\n * the desired pack level (0..9). The values given below have been tuned to\n * exclude worst case performance for pathological files. Better values may be\n * found for specific files.\n */\nfunction Config(good_length, max_lazy, nice_length, max_chain, func) {\n  this.good_length = good_length;\n  this.max_lazy = max_lazy;\n  this.nice_length = nice_length;\n  this.max_chain = max_chain;\n  this.func = func;\n}\n\nvar configuration_table;\n\nconfiguration_table = [\n  /*      good lazy nice chain */\n  new Config(0, 0, 0, 0, deflate_stored),          /* 0 store only */\n  new Config(4, 4, 8, 4, deflate_fast),            /* 1 max speed, no lazy matches */\n  new Config(4, 5, 16, 8, deflate_fast),           /* 2 */\n  new Config(4, 6, 32, 32, deflate_fast),          /* 3 */\n\n  new Config(4, 4, 16, 16, deflate_slow),          /* 4 lazy matches */\n  new Config(8, 16, 32, 32, deflate_slow),         /* 5 */\n  new Config(8, 16, 128, 128, deflate_slow),       /* 6 */\n  new Config(8, 32, 128, 256, deflate_slow),       /* 7 */\n  new Config(32, 128, 258, 1024, deflate_slow),    /* 8 */\n  new Config(32, 258, 258, 4096, deflate_slow)     /* 9 max compression */\n];\n\n\n/* ===========================================================================\n * Initialize the \"longest match\" routines for a new zlib stream\n */\nfunction lm_init(s) {\n  s.window_size = 2 * s.w_size;\n\n  /*** CLEAR_HASH(s); ***/\n  zero(s.head); // Fill with NIL (= 0);\n\n  /* Set the default configuration parameters:\n   */\n  s.max_lazy_match = configuration_table[s.level].max_lazy;\n  s.good_match = configuration_table[s.level].good_length;\n  s.nice_match = configuration_table[s.level].nice_length;\n  s.max_chain_length = configuration_table[s.level].max_chain;\n\n  s.strstart = 0;\n  s.block_start = 0;\n  s.lookahead = 0;\n  s.insert = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  s.ins_h = 0;\n}\n\n\nfunction DeflateState() {\n  this.strm = null;            /* pointer back to this zlib stream */\n  this.status = 0;            /* as the name implies */\n  this.pending_buf = null;      /* output still pending */\n  this.pending_buf_size = 0;  /* size of pending_buf */\n  this.pending_out = 0;       /* next pending byte to output to the stream */\n  this.pending = 0;           /* nb of bytes in the pending buffer */\n  this.wrap = 0;              /* bit 0 true for zlib, bit 1 true for gzip */\n  this.gzhead = null;         /* gzip header information to write */\n  this.gzindex = 0;           /* where in extra, name, or comment */\n  this.method = Z_DEFLATED; /* can only be DEFLATED */\n  this.last_flush = -1;   /* value of flush param for previous deflate call */\n\n  this.w_size = 0;  /* LZ77 window size (32K by default) */\n  this.w_bits = 0;  /* log2(w_size)  (8..16) */\n  this.w_mask = 0;  /* w_size - 1 */\n\n  this.window = null;\n  /* Sliding window. Input bytes are read into the second half of the window,\n   * and move to the first half later to keep a dictionary of at least wSize\n   * bytes. With this organization, matches are limited to a distance of\n   * wSize-MAX_MATCH bytes, but this ensures that IO is always\n   * performed with a length multiple of the block size.\n   */\n\n  this.window_size = 0;\n  /* Actual size of window: 2*wSize, except when the user input buffer\n   * is directly used as sliding window.\n   */\n\n  this.prev = null;\n  /* Link to older string with same hash index. To limit the size of this\n   * array to 64K, this link is maintained only for the last 32K strings.\n   * An index in this array is thus a window index modulo 32K.\n   */\n\n  this.head = null;   /* Heads of the hash chains or NIL. */\n\n  this.ins_h = 0;       /* hash index of string to be inserted */\n  this.hash_size = 0;   /* number of elements in hash table */\n  this.hash_bits = 0;   /* log2(hash_size) */\n  this.hash_mask = 0;   /* hash_size-1 */\n\n  this.hash_shift = 0;\n  /* Number of bits by which ins_h must be shifted at each input\n   * step. It must be such that after MIN_MATCH steps, the oldest\n   * byte no longer takes part in the hash key, that is:\n   *   hash_shift * MIN_MATCH >= hash_bits\n   */\n\n  this.block_start = 0;\n  /* Window position at the beginning of the current output block. Gets\n   * negative when the window is moved backwards.\n   */\n\n  this.match_length = 0;      /* length of best match */\n  this.prev_match = 0;        /* previous match */\n  this.match_available = 0;   /* set if previous match exists */\n  this.strstart = 0;          /* start of string to insert */\n  this.match_start = 0;       /* start of matching string */\n  this.lookahead = 0;         /* number of valid bytes ahead in window */\n\n  this.prev_length = 0;\n  /* Length of the best match at previous step. Matches not greater than this\n   * are discarded. This is used in the lazy match evaluation.\n   */\n\n  this.max_chain_length = 0;\n  /* To speed up deflation, hash chains are never searched beyond this\n   * length.  A higher limit improves compression ratio but degrades the\n   * speed.\n   */\n\n  this.max_lazy_match = 0;\n  /* Attempt to find a better match only when the current match is strictly\n   * smaller than this value. This mechanism is used only for compression\n   * levels >= 4.\n   */\n  // That's alias to max_lazy_match, don't use directly\n  //this.max_insert_length = 0;\n  /* Insert new strings in the hash table only if the match length is not\n   * greater than this length. This saves time but degrades compression.\n   * max_insert_length is used only for compression levels <= 3.\n   */\n\n  this.level = 0;     /* compression level (1..9) */\n  this.strategy = 0;  /* favor or force Huffman coding*/\n\n  this.good_match = 0;\n  /* Use a faster search when the previous match is longer than this */\n\n  this.nice_match = 0; /* Stop searching when current match exceeds this */\n\n              /* used by trees.c: */\n\n  /* Didn't use ct_data typedef below to suppress compiler warning */\n\n  // struct ct_data_s dyn_ltree[HEAP_SIZE];   /* literal and length tree */\n  // struct ct_data_s dyn_dtree[2*D_CODES+1]; /* distance tree */\n  // struct ct_data_s bl_tree[2*BL_CODES+1];  /* Huffman tree for bit lengths */\n\n  // Use flat array of DOUBLE size, with interleaved fata,\n  // because JS does not support effective\n  this.dyn_ltree  = new utils.Buf16(HEAP_SIZE * 2);\n  this.dyn_dtree  = new utils.Buf16((2 * D_CODES + 1) * 2);\n  this.bl_tree    = new utils.Buf16((2 * BL_CODES + 1) * 2);\n  zero(this.dyn_ltree);\n  zero(this.dyn_dtree);\n  zero(this.bl_tree);\n\n  this.l_desc   = null;         /* desc. for literal tree */\n  this.d_desc   = null;         /* desc. for distance tree */\n  this.bl_desc  = null;         /* desc. for bit length tree */\n\n  //ush bl_count[MAX_BITS+1];\n  this.bl_count = new utils.Buf16(MAX_BITS + 1);\n  /* number of codes at each bit length for an optimal tree */\n\n  //int heap[2*L_CODES+1];      /* heap used to build the Huffman trees */\n  this.heap = new utils.Buf16(2 * L_CODES + 1);  /* heap used to build the Huffman trees */\n  zero(this.heap);\n\n  this.heap_len = 0;               /* number of elements in the heap */\n  this.heap_max = 0;               /* element of largest frequency */\n  /* The sons of heap[n] are heap[2*n] and heap[2*n+1]. heap[0] is not used.\n   * The same heap array is used to build all trees.\n   */\n\n  this.depth = new utils.Buf16(2 * L_CODES + 1); //uch depth[2*L_CODES+1];\n  zero(this.depth);\n  /* Depth of each subtree used as tie breaker for trees of equal frequency\n   */\n\n  this.l_buf = 0;          /* buffer index for literals or lengths */\n\n  this.lit_bufsize = 0;\n  /* Size of match buffer for literals/lengths.  There are 4 reasons for\n   * limiting lit_bufsize to 64K:\n   *   - frequencies can be kept in 16 bit counters\n   *   - if compression is not successful for the first block, all input\n   *     data is still in the window so we can still emit a stored block even\n   *     when input comes from standard input.  (This can also be done for\n   *     all blocks if lit_bufsize is not greater than 32K.)\n   *   - if compression is not successful for a file smaller than 64K, we can\n   *     even emit a stored file instead of a stored block (saving 5 bytes).\n   *     This is applicable only for zip (not gzip or zlib).\n   *   - creating new Huffman trees less frequently may not provide fast\n   *     adaptation to changes in the input data statistics. (Take for\n   *     example a binary file with poorly compressible code followed by\n   *     a highly compressible string table.) Smaller buffer sizes give\n   *     fast adaptation but have of course the overhead of transmitting\n   *     trees more frequently.\n   *   - I can't count above 4\n   */\n\n  this.last_lit = 0;      /* running index in l_buf */\n\n  this.d_buf = 0;\n  /* Buffer index for distances. To simplify the code, d_buf and l_buf have\n   * the same number of elements. To use different lengths, an extra flag\n   * array would be necessary.\n   */\n\n  this.opt_len = 0;       /* bit length of current block with optimal trees */\n  this.static_len = 0;    /* bit length of current block with static trees */\n  this.matches = 0;       /* number of string matches in current block */\n  this.insert = 0;        /* bytes at end of window left to insert */\n\n\n  this.bi_buf = 0;\n  /* Output buffer. bits are inserted starting at the bottom (least\n   * significant bits).\n   */\n  this.bi_valid = 0;\n  /* Number of valid bits in bi_buf.  All bits above the last valid bit\n   * are always zero.\n   */\n\n  // Used for window memory init. We safely ignore it for JS. That makes\n  // sense only for pointers and memory check tools.\n  //this.high_water = 0;\n  /* High water mark offset in window for initialized bytes -- bytes above\n   * this are set to zero in order to avoid memory check warnings when\n   * longest match routines access bytes past the input.  This is then\n   * updated to the new high water mark.\n   */\n}\n\n\nfunction deflateResetKeep(strm) {\n  var s;\n\n  if (!strm || !strm.state) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.total_in = strm.total_out = 0;\n  strm.data_type = Z_UNKNOWN;\n\n  s = strm.state;\n  s.pending = 0;\n  s.pending_out = 0;\n\n  if (s.wrap < 0) {\n    s.wrap = -s.wrap;\n    /* was made negative by deflate(..., Z_FINISH); */\n  }\n  s.status = (s.wrap ? INIT_STATE : BUSY_STATE);\n  strm.adler = (s.wrap === 2) ?\n    0  // crc32(0, Z_NULL, 0)\n  :\n    1; // adler32(0, Z_NULL, 0)\n  s.last_flush = Z_NO_FLUSH;\n  trees._tr_init(s);\n  return Z_OK;\n}\n\n\nfunction deflateReset(strm) {\n  var ret = deflateResetKeep(strm);\n  if (ret === Z_OK) {\n    lm_init(strm.state);\n  }\n  return ret;\n}\n\n\nfunction deflateSetHeader(strm, head) {\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  if (strm.state.wrap !== 2) { return Z_STREAM_ERROR; }\n  strm.state.gzhead = head;\n  return Z_OK;\n}\n\n\nfunction deflateInit2(strm, level, method, windowBits, memLevel, strategy) {\n  if (!strm) { // === Z_NULL\n    return Z_STREAM_ERROR;\n  }\n  var wrap = 1;\n\n  if (level === Z_DEFAULT_COMPRESSION) {\n    level = 6;\n  }\n\n  if (windowBits < 0) { /* suppress zlib wrapper */\n    wrap = 0;\n    windowBits = -windowBits;\n  }\n\n  else if (windowBits > 15) {\n    wrap = 2;           /* write gzip wrapper instead */\n    windowBits -= 16;\n  }\n\n\n  if (memLevel < 1 || memLevel > MAX_MEM_LEVEL || method !== Z_DEFLATED ||\n    windowBits < 8 || windowBits > 15 || level < 0 || level > 9 ||\n    strategy < 0 || strategy > Z_FIXED) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n\n  if (windowBits === 8) {\n    windowBits = 9;\n  }\n  /* until 256-byte window bug fixed */\n\n  var s = new DeflateState();\n\n  strm.state = s;\n  s.strm = strm;\n\n  s.wrap = wrap;\n  s.gzhead = null;\n  s.w_bits = windowBits;\n  s.w_size = 1 << s.w_bits;\n  s.w_mask = s.w_size - 1;\n\n  s.hash_bits = memLevel + 7;\n  s.hash_size = 1 << s.hash_bits;\n  s.hash_mask = s.hash_size - 1;\n  s.hash_shift = ~~((s.hash_bits + MIN_MATCH - 1) / MIN_MATCH);\n\n  s.window = new utils.Buf8(s.w_size * 2);\n  s.head = new utils.Buf16(s.hash_size);\n  s.prev = new utils.Buf16(s.w_size);\n\n  // Don't need mem init magic for JS.\n  //s.high_water = 0;  /* nothing written to s->window yet */\n\n  s.lit_bufsize = 1 << (memLevel + 6); /* 16K elements by default */\n\n  s.pending_buf_size = s.lit_bufsize * 4;\n\n  //overlay = (ushf *) ZALLOC(strm, s->lit_bufsize, sizeof(ush)+2);\n  //s->pending_buf = (uchf *) overlay;\n  s.pending_buf = new utils.Buf8(s.pending_buf_size);\n\n  // It is offset from `s.pending_buf` (size is `s.lit_bufsize * 2`)\n  //s->d_buf = overlay + s->lit_bufsize/sizeof(ush);\n  s.d_buf = 1 * s.lit_bufsize;\n\n  //s->l_buf = s->pending_buf + (1+sizeof(ush))*s->lit_bufsize;\n  s.l_buf = (1 + 2) * s.lit_bufsize;\n\n  s.level = level;\n  s.strategy = strategy;\n  s.method = method;\n\n  return deflateReset(strm);\n}\n\nfunction deflateInit(strm, level) {\n  return deflateInit2(strm, level, Z_DEFLATED, MAX_WBITS, DEF_MEM_LEVEL, Z_DEFAULT_STRATEGY);\n}\n\n\nfunction deflate(strm, flush) {\n  var old_flush, s;\n  var beg, val; // for gzip header write only\n\n  if (!strm || !strm.state ||\n    flush > Z_BLOCK || flush < 0) {\n    return strm ? err(strm, Z_STREAM_ERROR) : Z_STREAM_ERROR;\n  }\n\n  s = strm.state;\n\n  if (!strm.output ||\n      (!strm.input && strm.avail_in !== 0) ||\n      (s.status === FINISH_STATE && flush !== Z_FINISH)) {\n    return err(strm, (strm.avail_out === 0) ? Z_BUF_ERROR : Z_STREAM_ERROR);\n  }\n\n  s.strm = strm; /* just in case */\n  old_flush = s.last_flush;\n  s.last_flush = flush;\n\n  /* Write the header */\n  if (s.status === INIT_STATE) {\n\n    if (s.wrap === 2) { // GZIP header\n      strm.adler = 0;  //crc32(0L, Z_NULL, 0);\n      put_byte(s, 31);\n      put_byte(s, 139);\n      put_byte(s, 8);\n      if (!s.gzhead) { // s->gzhead == Z_NULL\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, 0);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, OS_CODE);\n        s.status = BUSY_STATE;\n      }\n      else {\n        put_byte(s, (s.gzhead.text ? 1 : 0) +\n                    (s.gzhead.hcrc ? 2 : 0) +\n                    (!s.gzhead.extra ? 0 : 4) +\n                    (!s.gzhead.name ? 0 : 8) +\n                    (!s.gzhead.comment ? 0 : 16)\n        );\n        put_byte(s, s.gzhead.time & 0xff);\n        put_byte(s, (s.gzhead.time >> 8) & 0xff);\n        put_byte(s, (s.gzhead.time >> 16) & 0xff);\n        put_byte(s, (s.gzhead.time >> 24) & 0xff);\n        put_byte(s, s.level === 9 ? 2 :\n                    (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2 ?\n                     4 : 0));\n        put_byte(s, s.gzhead.os & 0xff);\n        if (s.gzhead.extra && s.gzhead.extra.length) {\n          put_byte(s, s.gzhead.extra.length & 0xff);\n          put_byte(s, (s.gzhead.extra.length >> 8) & 0xff);\n        }\n        if (s.gzhead.hcrc) {\n          strm.adler = crc32(strm.adler, s.pending_buf, s.pending, 0);\n        }\n        s.gzindex = 0;\n        s.status = EXTRA_STATE;\n      }\n    }\n    else // DEFLATE header\n    {\n      var header = (Z_DEFLATED + ((s.w_bits - 8) << 4)) << 8;\n      var level_flags = -1;\n\n      if (s.strategy >= Z_HUFFMAN_ONLY || s.level < 2) {\n        level_flags = 0;\n      } else if (s.level < 6) {\n        level_flags = 1;\n      } else if (s.level === 6) {\n        level_flags = 2;\n      } else {\n        level_flags = 3;\n      }\n      header |= (level_flags << 6);\n      if (s.strstart !== 0) { header |= PRESET_DICT; }\n      header += 31 - (header % 31);\n\n      s.status = BUSY_STATE;\n      putShortMSB(s, header);\n\n      /* Save the adler32 of the preset dictionary: */\n      if (s.strstart !== 0) {\n        putShortMSB(s, strm.adler >>> 16);\n        putShortMSB(s, strm.adler & 0xffff);\n      }\n      strm.adler = 1; // adler32(0L, Z_NULL, 0);\n    }\n  }\n\n//#ifdef GZIP\n  if (s.status === EXTRA_STATE) {\n    if (s.gzhead.extra/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n\n      while (s.gzindex < (s.gzhead.extra.length & 0xffff)) {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            break;\n          }\n        }\n        put_byte(s, s.gzhead.extra[s.gzindex] & 0xff);\n        s.gzindex++;\n      }\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (s.gzindex === s.gzhead.extra.length) {\n        s.gzindex = 0;\n        s.status = NAME_STATE;\n      }\n    }\n    else {\n      s.status = NAME_STATE;\n    }\n  }\n  if (s.status === NAME_STATE) {\n    if (s.gzhead.name/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.name.length) {\n          val = s.gzhead.name.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.gzindex = 0;\n        s.status = COMMENT_STATE;\n      }\n    }\n    else {\n      s.status = COMMENT_STATE;\n    }\n  }\n  if (s.status === COMMENT_STATE) {\n    if (s.gzhead.comment/* != Z_NULL*/) {\n      beg = s.pending;  /* start of bytes to update crc */\n      //int val;\n\n      do {\n        if (s.pending === s.pending_buf_size) {\n          if (s.gzhead.hcrc && s.pending > beg) {\n            strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n          }\n          flush_pending(strm);\n          beg = s.pending;\n          if (s.pending === s.pending_buf_size) {\n            val = 1;\n            break;\n          }\n        }\n        // JS specific: little magic to add zero terminator to end of string\n        if (s.gzindex < s.gzhead.comment.length) {\n          val = s.gzhead.comment.charCodeAt(s.gzindex++) & 0xff;\n        } else {\n          val = 0;\n        }\n        put_byte(s, val);\n      } while (val !== 0);\n\n      if (s.gzhead.hcrc && s.pending > beg) {\n        strm.adler = crc32(strm.adler, s.pending_buf, s.pending - beg, beg);\n      }\n      if (val === 0) {\n        s.status = HCRC_STATE;\n      }\n    }\n    else {\n      s.status = HCRC_STATE;\n    }\n  }\n  if (s.status === HCRC_STATE) {\n    if (s.gzhead.hcrc) {\n      if (s.pending + 2 > s.pending_buf_size) {\n        flush_pending(strm);\n      }\n      if (s.pending + 2 <= s.pending_buf_size) {\n        put_byte(s, strm.adler & 0xff);\n        put_byte(s, (strm.adler >> 8) & 0xff);\n        strm.adler = 0; //crc32(0L, Z_NULL, 0);\n        s.status = BUSY_STATE;\n      }\n    }\n    else {\n      s.status = BUSY_STATE;\n    }\n  }\n//#endif\n\n  /* Flush as much pending output as possible */\n  if (s.pending !== 0) {\n    flush_pending(strm);\n    if (strm.avail_out === 0) {\n      /* Since avail_out is 0, deflate will be called again with\n       * more output space, but possibly with both pending and\n       * avail_in equal to zero. There won't be anything to do,\n       * but this is not an error situation so make sure we\n       * return OK instead of BUF_ERROR at next call of deflate:\n       */\n      s.last_flush = -1;\n      return Z_OK;\n    }\n\n    /* Make sure there is something to do and avoid duplicate consecutive\n     * flushes. For repeated and useless calls with Z_FINISH, we keep\n     * returning Z_STREAM_END instead of Z_BUF_ERROR.\n     */\n  } else if (strm.avail_in === 0 && rank(flush) <= rank(old_flush) &&\n    flush !== Z_FINISH) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* User must not provide more input after the first FINISH: */\n  if (s.status === FINISH_STATE && strm.avail_in !== 0) {\n    return err(strm, Z_BUF_ERROR);\n  }\n\n  /* Start a new block or continue the current one.\n   */\n  if (strm.avail_in !== 0 || s.lookahead !== 0 ||\n    (flush !== Z_NO_FLUSH && s.status !== FINISH_STATE)) {\n    var bstate = (s.strategy === Z_HUFFMAN_ONLY) ? deflate_huff(s, flush) :\n      (s.strategy === Z_RLE ? deflate_rle(s, flush) :\n        configuration_table[s.level].func(s, flush));\n\n    if (bstate === BS_FINISH_STARTED || bstate === BS_FINISH_DONE) {\n      s.status = FINISH_STATE;\n    }\n    if (bstate === BS_NEED_MORE || bstate === BS_FINISH_STARTED) {\n      if (strm.avail_out === 0) {\n        s.last_flush = -1;\n        /* avoid BUF_ERROR next call, see above */\n      }\n      return Z_OK;\n      /* If flush != Z_NO_FLUSH && avail_out == 0, the next call\n       * of deflate should use the same flush parameter to make sure\n       * that the flush is complete. So we don't have to output an\n       * empty block here, this will be done at next call. This also\n       * ensures that for a very small output buffer, we emit at most\n       * one empty block.\n       */\n    }\n    if (bstate === BS_BLOCK_DONE) {\n      if (flush === Z_PARTIAL_FLUSH) {\n        trees._tr_align(s);\n      }\n      else if (flush !== Z_BLOCK) { /* FULL_FLUSH or SYNC_FLUSH */\n\n        trees._tr_stored_block(s, 0, 0, false);\n        /* For a full flush, this empty block will be recognized\n         * as a special marker by inflate_sync().\n         */\n        if (flush === Z_FULL_FLUSH) {\n          /*** CLEAR_HASH(s); ***/             /* forget history */\n          zero(s.head); // Fill with NIL (= 0);\n\n          if (s.lookahead === 0) {\n            s.strstart = 0;\n            s.block_start = 0;\n            s.insert = 0;\n          }\n        }\n      }\n      flush_pending(strm);\n      if (strm.avail_out === 0) {\n        s.last_flush = -1; /* avoid BUF_ERROR at next call, see above */\n        return Z_OK;\n      }\n    }\n  }\n  //Assert(strm->avail_out > 0, \"bug2\");\n  //if (strm.avail_out <= 0) { throw new Error(\"bug2\");}\n\n  if (flush !== Z_FINISH) { return Z_OK; }\n  if (s.wrap <= 0) { return Z_STREAM_END; }\n\n  /* Write the trailer */\n  if (s.wrap === 2) {\n    put_byte(s, strm.adler & 0xff);\n    put_byte(s, (strm.adler >> 8) & 0xff);\n    put_byte(s, (strm.adler >> 16) & 0xff);\n    put_byte(s, (strm.adler >> 24) & 0xff);\n    put_byte(s, strm.total_in & 0xff);\n    put_byte(s, (strm.total_in >> 8) & 0xff);\n    put_byte(s, (strm.total_in >> 16) & 0xff);\n    put_byte(s, (strm.total_in >> 24) & 0xff);\n  }\n  else\n  {\n    putShortMSB(s, strm.adler >>> 16);\n    putShortMSB(s, strm.adler & 0xffff);\n  }\n\n  flush_pending(strm);\n  /* If avail_out is zero, the application will call deflate again\n   * to flush the rest.\n   */\n  if (s.wrap > 0) { s.wrap = -s.wrap; }\n  /* write the trailer only once! */\n  return s.pending !== 0 ? Z_OK : Z_STREAM_END;\n}\n\nfunction deflateEnd(strm) {\n  var status;\n\n  if (!strm/*== Z_NULL*/ || !strm.state/*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  status = strm.state.status;\n  if (status !== INIT_STATE &&\n    status !== EXTRA_STATE &&\n    status !== NAME_STATE &&\n    status !== COMMENT_STATE &&\n    status !== HCRC_STATE &&\n    status !== BUSY_STATE &&\n    status !== FINISH_STATE\n  ) {\n    return err(strm, Z_STREAM_ERROR);\n  }\n\n  strm.state = null;\n\n  return status === BUSY_STATE ? err(strm, Z_DATA_ERROR) : Z_OK;\n}\n\n\n/* =========================================================================\n * Initializes the compression dictionary from the given byte\n * sequence without producing any compressed output.\n */\nfunction deflateSetDictionary(strm, dictionary) {\n  var dictLength = dictionary.length;\n\n  var s;\n  var str, n;\n  var wrap;\n  var avail;\n  var next;\n  var input;\n  var tmpDict;\n\n  if (!strm/*== Z_NULL*/ || !strm.state/*== Z_NULL*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  s = strm.state;\n  wrap = s.wrap;\n\n  if (wrap === 2 || (wrap === 1 && s.status !== INIT_STATE) || s.lookahead) {\n    return Z_STREAM_ERROR;\n  }\n\n  /* when using zlib wrappers, compute Adler-32 for provided dictionary */\n  if (wrap === 1) {\n    /* adler32(strm->adler, dictionary, dictLength); */\n    strm.adler = adler32(strm.adler, dictionary, dictLength, 0);\n  }\n\n  s.wrap = 0;   /* avoid computing Adler-32 in read_buf */\n\n  /* if dictionary would fill window, just replace the history */\n  if (dictLength >= s.w_size) {\n    if (wrap === 0) {            /* already empty otherwise */\n      /*** CLEAR_HASH(s); ***/\n      zero(s.head); // Fill with NIL (= 0);\n      s.strstart = 0;\n      s.block_start = 0;\n      s.insert = 0;\n    }\n    /* use the tail */\n    // dictionary = dictionary.slice(dictLength - s.w_size);\n    tmpDict = new utils.Buf8(s.w_size);\n    utils.arraySet(tmpDict, dictionary, dictLength - s.w_size, s.w_size, 0);\n    dictionary = tmpDict;\n    dictLength = s.w_size;\n  }\n  /* insert dictionary into window and hash */\n  avail = strm.avail_in;\n  next = strm.next_in;\n  input = strm.input;\n  strm.avail_in = dictLength;\n  strm.next_in = 0;\n  strm.input = dictionary;\n  fill_window(s);\n  while (s.lookahead >= MIN_MATCH) {\n    str = s.strstart;\n    n = s.lookahead - (MIN_MATCH - 1);\n    do {\n      /* UPDATE_HASH(s, s->ins_h, s->window[str + MIN_MATCH-1]); */\n      s.ins_h = ((s.ins_h << s.hash_shift) ^ s.window[str + MIN_MATCH - 1]) & s.hash_mask;\n\n      s.prev[str & s.w_mask] = s.head[s.ins_h];\n\n      s.head[s.ins_h] = str;\n      str++;\n    } while (--n);\n    s.strstart = str;\n    s.lookahead = MIN_MATCH - 1;\n    fill_window(s);\n  }\n  s.strstart += s.lookahead;\n  s.block_start = s.strstart;\n  s.insert = s.lookahead;\n  s.lookahead = 0;\n  s.match_length = s.prev_length = MIN_MATCH - 1;\n  s.match_available = 0;\n  strm.next_in = next;\n  strm.input = input;\n  strm.avail_in = avail;\n  s.wrap = wrap;\n  return Z_OK;\n}\n\n\nexports.deflateInit = deflateInit;\nexports.deflateInit2 = deflateInit2;\nexports.deflateReset = deflateReset;\nexports.deflateResetKeep = deflateResetKeep;\nexports.deflateSetHeader = deflateSetHeader;\nexports.deflate = deflate;\nexports.deflateEnd = deflateEnd;\nexports.deflateSetDictionary = deflateSetDictionary;\nexports.deflateInfo = 'pako deflate (from Nodeca project)';\n\n/* Not implemented\nexports.deflateBound = deflateBound;\nexports.deflateCopy = deflateCopy;\nexports.deflateParams = deflateParams;\nexports.deflatePending = deflatePending;\nexports.deflatePrime = deflatePrime;\nexports.deflateTune = deflateTune;\n*/\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,8EAA8E,GAC9E,8EAA8E,GAG9E,uEAAuE,GACvE,IAAI,aAAkB;AACtB,IAAI,kBAAkB;AACtB,0BAA0B;AAC1B,IAAI,eAAkB;AACtB,IAAI,WAAkB;AACtB,IAAI,UAAkB;AACtB,0BAA0B;AAG1B;;CAEC,GACD,IAAI,OAAkB;AACtB,IAAI,eAAkB;AACtB,0BAA0B;AAC1B,2BAA2B;AAC3B,IAAI,iBAAkB,CAAC;AACvB,IAAI,eAAkB,CAAC;AACvB,2BAA2B;AAC3B,IAAI,cAAkB,CAAC;AACvB,2BAA2B;AAG3B,sBAAsB,GACtB,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,IAAI,wBAAwB,CAAC;AAG7B,IAAI,aAAwB;AAC5B,IAAI,iBAAwB;AAC5B,IAAI,QAAwB;AAC5B,IAAI,UAAwB;AAC5B,IAAI,qBAAwB;AAE5B,iEAAiE,GACjE,gCAAgC;AAChC,gCAAgC;AAChC,4CAA4C;AAC5C,IAAI,YAAwB;AAG5B,kCAAkC,GAClC,IAAI,aAAc;AAElB,8EAA8E,GAG9E,IAAI,gBAAgB;AACpB,8CAA8C,GAC9C,IAAI,YAAY;AAChB,mBAAmB,GACnB,IAAI,gBAAgB;AAGpB,IAAI,eAAgB;AACpB,mEAAmE,GACnE,IAAI,WAAgB;AACpB,kCAAkC,GAClC,IAAI,UAAgB,WAAW,IAAI;AACnC,mEAAmE,GACnE,IAAI,UAAgB;AACpB,4BAA4B,GAC5B,IAAI,WAAgB;AACpB,oDAAoD,GACpD,IAAI,YAAgB,IAAI,UAAU;AAClC,qBAAqB,GACrB,IAAI,WAAY;AAChB,2CAA2C,GAE3C,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,gBAAiB,YAAY,YAAY;AAE7C,IAAI,cAAc;AAElB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,eAAe;AAEnB,IAAI,eAAoB,GAAG,uDAAuD;AAClF,IAAI,gBAAoB,GAAG,yBAAyB;AACpD,IAAI,oBAAoB,GAAG,yDAAyD;AACpF,IAAI,iBAAoB,GAAG,+CAA+C;AAE1E,IAAI,UAAU,MAAM,4CAA4C;AAEhE,SAAS,IAAI,IAAI,EAAE,SAAS;IAC1B,KAAK,GAAG,GAAG,GAAG,CAAC,UAAU;IACzB,OAAO;AACT;AAEA,SAAS,KAAK,CAAC;IACb,OAAO,CAAC,AAAC,KAAM,CAAC,IAAI,CAAC,AAAC,IAAK,IAAI,IAAI,CAAC;AACtC;AAEA,SAAS,KAAK,GAAG;IAAI,IAAI,MAAM,IAAI,MAAM;IAAE,MAAO,EAAE,OAAO,EAAG;QAAE,GAAG,CAAC,IAAI,GAAG;IAAG;AAAE;AAGhF;;;;;CAKC,GACD,SAAS,cAAc,IAAI;IACzB,IAAI,IAAI,KAAK,KAAK;IAElB,oBAAoB;IACpB,IAAI,MAAM,EAAE,OAAO;IACnB,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM,KAAK,SAAS;IACtB;IACA,IAAI,QAAQ,GAAG;QAAE;IAAQ;IAEzB,MAAM,QAAQ,CAAC,KAAK,MAAM,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,EAAE,KAAK,KAAK,QAAQ;IAC5E,KAAK,QAAQ,IAAI;IACjB,EAAE,WAAW,IAAI;IACjB,KAAK,SAAS,IAAI;IAClB,KAAK,SAAS,IAAI;IAClB,EAAE,OAAO,IAAI;IACb,IAAI,EAAE,OAAO,KAAK,GAAG;QACnB,EAAE,WAAW,GAAG;IAClB;AACF;AAGA,SAAS,iBAAiB,CAAC,EAAE,IAAI;IAC/B,MAAM,eAAe,CAAC,GAAI,EAAE,WAAW,IAAI,IAAI,EAAE,WAAW,GAAG,CAAC,GAAI,EAAE,QAAQ,GAAG,EAAE,WAAW,EAAE;IAChG,EAAE,WAAW,GAAG,EAAE,QAAQ;IAC1B,cAAc,EAAE,IAAI;AACtB;AAGA,SAAS,SAAS,CAAC,EAAE,CAAC;IACpB,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG;AAC/B;AAGA;;;;CAIC,GACD,SAAS,YAAY,CAAC,EAAE,CAAC;IACzB,gCAAgC;IAChC,kCAAkC;IAChC,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,AAAC,MAAM,IAAK;IACzC,EAAE,WAAW,CAAC,EAAE,OAAO,GAAG,GAAG,IAAI;AACnC;AAGA;;;;;;CAMC,GACD,SAAS,SAAS,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI;IACtC,IAAI,MAAM,KAAK,QAAQ;IAEvB,IAAI,MAAM,MAAM;QAAE,MAAM;IAAM;IAC9B,IAAI,QAAQ,GAAG;QAAE,OAAO;IAAG;IAE3B,KAAK,QAAQ,IAAI;IAEjB,oCAAoC;IACpC,MAAM,QAAQ,CAAC,KAAK,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE,KAAK;IACnD,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;QACzB,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;IAC7C,OAEK,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;QAC9B,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,KAAK,KAAK;IAC3C;IAEA,KAAK,OAAO,IAAI;IAChB,KAAK,QAAQ,IAAI;IAEjB,OAAO;AACT;AAGA;;;;;;;;CAQC,GACD,SAAS,cAAc,CAAC,EAAE,SAAS;IACjC,IAAI,eAAe,EAAE,gBAAgB,EAAO,yBAAyB;IACrE,IAAI,OAAO,EAAE,QAAQ,EAAE,kBAAkB;IACzC,IAAI,OAA6B,kBAAkB;IACnD,IAAI,KAA+B,2BAA2B;IAC9D,IAAI,WAAW,EAAE,WAAW,EAAe,4BAA4B;IACvE,IAAI,aAAa,EAAE,UAAU,EAAc,6BAA6B;IACxE,IAAI,QAAQ,AAAC,EAAE,QAAQ,GAAI,EAAE,MAAM,GAAG,gBAClC,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,GAAG,aAAa,IAAI,EAAC,KAAK;IAEpD,IAAI,OAAO,EAAE,MAAM,EAAE,WAAW;IAEhC,IAAI,QAAQ,EAAE,MAAM;IACpB,IAAI,OAAQ,EAAE,IAAI;IAElB;;GAEC,GAED,IAAI,SAAS,EAAE,QAAQ,GAAG;IAC1B,IAAI,YAAa,IAAI,CAAC,OAAO,WAAW,EAAE;IAC1C,IAAI,WAAa,IAAI,CAAC,OAAO,SAAS;IAEtC;;GAEC,GACD,oEAAoE;IAEpE,+DAA+D,GAC/D,IAAI,EAAE,WAAW,IAAI,EAAE,UAAU,EAAE;QACjC,iBAAiB;IACnB;IACA;;GAEC,GACD,IAAI,aAAa,EAAE,SAAS,EAAE;QAAE,aAAa,EAAE,SAAS;IAAE;IAE1D,8EAA8E;IAE9E,GAAG;QACD,gDAAgD;QAChD,QAAQ;QAER;;;;;;;KAOC,GAED,IAAI,IAAI,CAAC,QAAQ,SAAS,KAAS,YAC/B,IAAI,CAAC,QAAQ,WAAW,EAAE,KAAK,aAC/B,IAAI,CAAC,MAAM,KAAoB,IAAI,CAAC,KAAK,IACzC,IAAI,CAAC,EAAE,MAAM,KAAkB,IAAI,CAAC,OAAO,EAAE,EAAE;YACjD;QACF;QAEA;;;;;KAKC,GACD,QAAQ;QACR;QACA,wCAAwC;QAExC;;KAEC,GACD,GAAG;QACD,sBAAsB,GACxB,QAAS,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAChE,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAChE,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAChE,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,MAAM,IAChE,OAAO,OAAQ;QAExB,uEAAuE;QAEvE,MAAM,YAAY,CAAC,SAAS,IAAI;QAChC,OAAO,SAAS;QAEhB,IAAI,MAAM,UAAU;YAClB,EAAE,WAAW,GAAG;YAChB,WAAW;YACX,IAAI,OAAO,YAAY;gBACrB;YACF;YACA,YAAa,IAAI,CAAC,OAAO,WAAW,EAAE;YACtC,WAAa,IAAI,CAAC,OAAO,SAAS;QACpC;IACF,QAAS,CAAC,YAAY,IAAI,CAAC,YAAY,MAAM,IAAI,SAAS,EAAE,iBAAiB,EAAG;IAEhF,IAAI,YAAY,EAAE,SAAS,EAAE;QAC3B,OAAO;IACT;IACA,OAAO,EAAE,SAAS;AACpB;AAGA;;;;;;;;;CASC,GACD,SAAS,YAAY,CAAC;IACpB,IAAI,UAAU,EAAE,MAAM;IACtB,IAAI,GAAG,GAAG,GAAG,MAAM;IAEnB,mEAAmE;IAEnE,GAAG;QACD,OAAO,EAAE,WAAW,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ;QAE/C,8CAA8C;QAC9C,8BAA8B,GAC9B,yBAAyB;QACzB,+DAA+D;QAC/D,uBAAuB;QACvB,EAAE;QACF,wCAAwC;QACxC,6DAA6D;QAC7D,wEAAwE;QACxE,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,GAAG;QAGH;;KAEC,GACD,IAAI,EAAE,QAAQ,IAAI,UAAU,CAAC,UAAU,aAAa,GAAG;YAErD,MAAM,QAAQ,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,SAAS,SAAS;YACrD,EAAE,WAAW,IAAI;YACjB,EAAE,QAAQ,IAAI;YACd,oCAAoC,GACpC,EAAE,WAAW,IAAI;YAEjB;;;;;OAKC,GAED,IAAI,EAAE,SAAS;YACf,IAAI;YACJ,GAAG;gBACD,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,EAAE,IAAI,CAAC,EAAE,GAAI,KAAK,UAAU,IAAI,UAAU;YAC5C,QAAS,EAAE,EAAG;YAEd,IAAI;YACJ,IAAI;YACJ,GAAG;gBACD,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACf,EAAE,IAAI,CAAC,EAAE,GAAI,KAAK,UAAU,IAAI,UAAU;YAC1C;;SAEC,GACH,QAAS,EAAE,EAAG;YAEd,QAAQ;QACV;QACA,IAAI,EAAE,IAAI,CAAC,QAAQ,KAAK,GAAG;YACzB;QACF;QAEA;;;;;;;;;;KAUC,GACD,gCAAgC;QAChC,IAAI,SAAS,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,QAAQ,GAAG,EAAE,SAAS,EAAE;QACzD,EAAE,SAAS,IAAI;QAEf,0DAA0D,GAC1D,IAAI,EAAE,SAAS,GAAG,EAAE,MAAM,IAAI,WAAW;YACvC,MAAM,EAAE,QAAQ,GAAG,EAAE,MAAM;YAC3B,EAAE,KAAK,GAAG,EAAE,MAAM,CAAC,IAAI;YAEvB,iDAAiD,GACjD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS;YAC7E,oBAAoB;YACpB,mDAAmD;YACnD,QAAQ;YACF,MAAO,EAAE,MAAM,CAAE;gBACf,2DAA2D,GAC3D,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,MAAM,YAAY,EAAE,IAAI,EAAE,SAAS;gBAEnF,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;gBACxC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG;gBAClB;gBACA,EAAE,MAAM;gBACR,IAAI,EAAE,SAAS,GAAG,EAAE,MAAM,GAAG,WAAW;oBACtC;gBACF;YACF;QACF;IACA;;KAEC,GAEH,QAAS,EAAE,SAAS,GAAG,iBAAiB,EAAE,IAAI,CAAC,QAAQ,KAAK,EAAG;AAE/D;;;;;;GAMC,GACH,uCAAuC;AACvC,0CAA0C;AAC1C,mBAAmB;AACnB,EAAE;AACF,gCAAgC;AAChC,uEAAuE;AACvE,2DAA2D;AAC3D,WAAW;AACX,oCAAoC;AACpC,4BAA4B;AAC5B,0BAA0B;AAC1B,mDAAmD;AACnD,oCAAoC;AACpC,OAAO;AACP,sDAAsD;AACtD,2EAA2E;AAC3E,yEAAyE;AACzE,+CAA+C;AAC/C,WAAW;AACX,oDAAoD;AACpD,kDAAkD;AAClD,gDAAgD;AAChD,4DAA4D;AAC5D,8BAA8B;AAC9B,OAAO;AACP,KAAK;AACL,EAAE;AACF,8DAA8D;AAC9D,oCAAoC;AACpC;AAEA;;;;;;;;CAQC,GACD,SAAS,eAAe,CAAC,EAAE,KAAK;IAC9B;;GAEC,GACD,IAAI,iBAAiB;IAErB,IAAI,iBAAiB,EAAE,gBAAgB,GAAG,GAAG;QAC3C,iBAAiB,EAAE,gBAAgB,GAAG;IACxC;IAEA,kDAAkD,GAClD,OAAS;QACP,wCAAwC,GACxC,IAAI,EAAE,SAAS,IAAI,GAAG;YAEpB,+CAA+C;YAC/C,yDAAyD;YAC/D,mEAAmE;YACnE,uCAAuC;YACvC,6CAA6C;YAC7C,SAAS;YAEH,YAAY;YACZ,IAAI,EAAE,SAAS,KAAK,KAAK,UAAU,YAAY;gBAC7C,OAAO;YACT;YAEA,IAAI,EAAE,SAAS,KAAK,GAAG;gBACrB;YACF;QACA,2BAA2B,GAC7B;QACA,6CAA6C;QACjD,2DAA2D;QAEvD,EAAE,QAAQ,IAAI,EAAE,SAAS;QACzB,EAAE,SAAS,GAAG;QAEd,oDAAoD,GACpD,IAAI,YAAY,EAAE,WAAW,GAAG;QAEhC,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,WAAW;YAC/C,+DAA+D,GAC/D,EAAE,SAAS,GAAG,EAAE,QAAQ,GAAG;YAC3B,EAAE,QAAQ,GAAG;YACb,0BAA0B,GAC1B,iBAAiB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACA,GAAG,GAGL;QACA;;KAEC,GACD,IAAI,EAAE,QAAQ,GAAG,EAAE,WAAW,IAAK,EAAE,MAAM,GAAG,eAAgB;YAC5D,0BAA0B,GAC1B,iBAAiB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACA,GAAG,GACL;IACF;IAEA,EAAE,MAAM,GAAG;IAEX,IAAI,UAAU,UAAU;QACtB,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;QACA,GAAG,GACH,OAAO;IACT;IAEA,IAAI,EAAE,QAAQ,GAAG,EAAE,WAAW,EAAE;QAC9B,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;IACA,GAAG,GACL;IAEA,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAAS,aAAa,CAAC,EAAE,KAAK;IAC5B,IAAI,WAAkB,0BAA0B;IAChD,IAAI,QAAkB,wCAAwC;IAE9D,OAAS;QACP;;;;KAIC,GACD,IAAI,EAAE,SAAS,GAAG,eAAe;YAC/B,YAAY;YACZ,IAAI,EAAE,SAAS,GAAG,iBAAiB,UAAU,YAAY;gBACvD,OAAO;YACT;YACA,IAAI,EAAE,SAAS,KAAK,GAAG;gBACrB,OAAO,2BAA2B;YACpC;QACF;QAEA;;KAEC,GACD,YAAY,EAAC,KAAK;QAClB,IAAI,EAAE,SAAS,IAAI,WAAW;YAC5B,gDAAgD,GAChD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,YAAY,EAAE,IAAI,EAAE,SAAS;YAC1F,YAAY,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;YAC3D,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ;QAC5B,GAAG,GACL;QAEA;;KAEC,GACD,IAAI,cAAc,EAAC,KAAK,OAAO,AAAC,EAAE,QAAQ,GAAG,aAAe,EAAE,MAAM,GAAG,eAAiB;YACtF;;;OAGC,GACD,EAAE,YAAY,GAAG,cAAc,GAAG;QAClC,oCAAoC,GACtC;QACA,IAAI,EAAE,YAAY,IAAI,WAAW;YAC/B,+EAA+E;YAE/E;4DACsD,GACtD,SAAS,MAAM,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAG,EAAE,WAAW,EAAE,EAAE,YAAY,GAAG;YAEzE,EAAE,SAAS,IAAI,EAAE,YAAY;YAE7B;;OAEC,GACD,IAAI,EAAE,YAAY,IAAI,EAAE,cAAc,CAAA,mBAAmB,OAAM,EAAE,SAAS,IAAI,WAAW;gBACvF,EAAE,YAAY,IAAI,uCAAuC;gBACzD,GAAG;oBACD,EAAE,QAAQ;oBACV,gDAAgD,GAChD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,YAAY,EAAE,IAAI,EAAE,SAAS;oBAC1F,YAAY,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;oBAC3D,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ;gBAC5B,GAAG,GACH;;WAEC,GACH,QAAS,EAAE,EAAE,YAAY,KAAK,EAAG;gBACjC,EAAE,QAAQ;YACZ,OACA;gBACE,EAAE,QAAQ,IAAI,EAAE,YAAY;gBAC5B,EAAE,YAAY,GAAG;gBACjB,EAAE,KAAK,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC;gBAC9B,oDAAoD,GACpD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE,IAAI,EAAE,SAAS;YAEtF,oBAAoB;YACpB,2DAA2D;YAC3D,QAAQ;YACA;;SAEC,GACH;QACF,OAAO;YACL,mCAAmC,GACnC,+CAA+C;YAC/C,uDAAuD,GACvD,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC;YAEnD,EAAE,SAAS;YACX,EAAE,QAAQ;QACZ;QACA,IAAI,QAAQ;YACV,0BAA0B,GAC1B,iBAAiB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACA,GAAG,GACL;IACF;IACA,EAAE,MAAM,GAAI,AAAC,EAAE,QAAQ,GAAI,YAAY,IAAM,EAAE,QAAQ,GAAG,YAAY;IACtE,IAAI,UAAU,UAAU;QACtB,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;QACA,GAAG,GACH,OAAO;IACT;IACA,IAAI,EAAE,QAAQ,EAAE;QACd,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;IACA,GAAG,GACL;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,aAAa,CAAC,EAAE,KAAK;IAC5B,IAAI,WAAoB,sBAAsB;IAC9C,IAAI,QAAqB,wCAAwC;IAEjE,IAAI;IAEJ,4BAA4B,GAC5B,OAAS;QACP;;;;KAIC,GACD,IAAI,EAAE,SAAS,GAAG,eAAe;YAC/B,YAAY;YACZ,IAAI,EAAE,SAAS,GAAG,iBAAiB,UAAU,YAAY;gBACvD,OAAO;YACT;YACA,IAAI,EAAE,SAAS,KAAK,GAAG;gBAAE;YAAO,EAAE,2BAA2B;QAC/D;QAEA;;KAEC,GACD,YAAY,EAAC,KAAK;QAClB,IAAI,EAAE,SAAS,IAAI,WAAW;YAC5B,gDAAgD,GAChD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,YAAY,EAAE,IAAI,EAAE,SAAS;YAC1F,YAAY,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;YAC3D,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ;QAC5B,GAAG,GACL;QAEA;KACC,GACD,EAAE,WAAW,GAAG,EAAE,YAAY;QAC9B,EAAE,UAAU,GAAG,EAAE,WAAW;QAC5B,EAAE,YAAY,GAAG,YAAY;QAE7B,IAAI,cAAc,EAAC,KAAK,OAAM,EAAE,WAAW,GAAG,EAAE,cAAc,IAC1D,EAAE,QAAQ,GAAG,aAAc,EAAE,MAAM,GAAG,eAA+B;YACvE;;;OAGC,GACD,EAAE,YAAY,GAAG,cAAc,GAAG;YAClC,oCAAoC,GAEpC,IAAI,EAAE,YAAY,IAAI,KACnB,CAAC,EAAE,QAAQ,KAAK,cAAe,EAAE,YAAY,KAAK,aAAa,EAAE,QAAQ,GAAG,EAAE,WAAW,GAAG,KAAI,SAAS,GAAG,GAAG;gBAEhH;;SAEC,GACD,EAAE,YAAY,GAAG,YAAY;YAC/B;QACF;QACA;;KAEC,GACD,IAAI,EAAE,WAAW,IAAI,aAAa,EAAE,YAAY,IAAI,EAAE,WAAW,EAAE;YACjE,aAAa,EAAE,QAAQ,GAAG,EAAE,SAAS,GAAG;YACxC,oDAAoD,GAEpD,4DAA4D;YAE5D;0DACoD,GACpD,SAAS,MAAM,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAG,IAAI,EAAE,UAAU,EAAE,EAAE,WAAW,GAAG;YAC3E;;;;OAIC,GACD,EAAE,SAAS,IAAI,EAAE,WAAW,GAAG;YAC/B,EAAE,WAAW,IAAI;YACjB,GAAG;gBACD,IAAI,EAAE,EAAE,QAAQ,IAAI,YAAY;oBAC9B,gDAAgD,GAChD,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,YAAY,EAAE,IAAI,EAAE,SAAS;oBAC1F,YAAY,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;oBAC3D,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ;gBAC5B,GAAG,GACL;YACF,QAAS,EAAE,EAAE,WAAW,KAAK,EAAG;YAChC,EAAE,eAAe,GAAG;YACpB,EAAE,YAAY,GAAG,YAAY;YAC7B,EAAE,QAAQ;YAEV,IAAI,QAAQ;gBACV,0BAA0B,GAC1B,iBAAiB,GAAG;gBACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;oBAC1B,OAAO;gBACT;YACA,GAAG,GACL;QAEF,OAAO,IAAI,EAAE,eAAe,EAAE;YAC5B;;;OAGC,GACD,mDAAmD;YACnD,yDAAyD,GACzD,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE;YAEvD,IAAI,QAAQ;gBACV,8BAA8B,GAC9B,iBAAiB,GAAG;YACpB,GAAG,GACL;YACA,EAAE,QAAQ;YACV,EAAE,SAAS;YACX,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACF,OAAO;YACL;;OAEC,GACD,EAAE,eAAe,GAAG;YACpB,EAAE,QAAQ;YACV,EAAE,SAAS;QACb;IACF;IACA,4CAA4C;IAC5C,IAAI,EAAE,eAAe,EAAE;QACrB,mDAAmD;QACnD,yDAAyD,GACzD,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,GAAG,EAAE;QAEvD,EAAE,eAAe,GAAG;IACtB;IACA,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,YAAY,IAAI,EAAE,QAAQ,GAAG,YAAY;IACjE,IAAI,UAAU,UAAU;QACtB,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;QACA,GAAG,GACH,OAAO;IACT;IACA,IAAI,EAAE,QAAQ,EAAE;QACd,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;IACA,GAAG,GACL;IAEA,OAAO;AACT;AAGA;;;;CAIC,GACD,SAAS,YAAY,CAAC,EAAE,KAAK;IAC3B,IAAI,QAAmB,wCAAwC;IAC/D,IAAI,MAAmB,iCAAiC;IACxD,IAAI,MAAM,QAAa,4CAA4C;IAEnE,IAAI,OAAO,EAAE,MAAM;IAEnB,OAAS;QACP;;;KAGC,GACD,IAAI,EAAE,SAAS,IAAI,WAAW;YAC5B,YAAY;YACZ,IAAI,EAAE,SAAS,IAAI,aAAa,UAAU,YAAY;gBACpD,OAAO;YACT;YACA,IAAI,EAAE,SAAS,KAAK,GAAG;gBAAE;YAAO,EAAE,2BAA2B;QAC/D;QAEA,gDAAgD,GAChD,EAAE,YAAY,GAAG;QACjB,IAAI,EAAE,SAAS,IAAI,aAAa,EAAE,QAAQ,GAAG,GAAG;YAC9C,OAAO,EAAE,QAAQ,GAAG;YACpB,OAAO,IAAI,CAAC,KAAK;YACjB,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,EAAE;gBAC3E,SAAS,EAAE,QAAQ,GAAG;gBACtB,GAAG;gBACD,sBAAsB,GACxB,QAAS,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAC9C,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAC9C,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAC9C,SAAS,IAAI,CAAC,EAAE,KAAK,IAAI,SAAS,IAAI,CAAC,EAAE,KAAK,IAC9C,OAAO,OAAQ;gBACxB,EAAE,YAAY,GAAG,YAAY,CAAC,SAAS,IAAI;gBAC3C,IAAI,EAAE,YAAY,GAAG,EAAE,SAAS,EAAE;oBAChC,EAAE,YAAY,GAAG,EAAE,SAAS;gBAC9B;YACF;QACA,kEAAkE;QACpE;QAEA,oEAAoE,GACpE,IAAI,EAAE,YAAY,IAAI,WAAW;YAC/B,6DAA6D;YAE7D,iEAAiE,GACjE,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,YAAY,GAAG;YAEhD,EAAE,SAAS,IAAI,EAAE,YAAY;YAC7B,EAAE,QAAQ,IAAI,EAAE,YAAY;YAC5B,EAAE,YAAY,GAAG;QACnB,OAAO;YACL,mCAAmC,GACnC,iDAAiD;YACjD,uDAAuD,GACvD,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC;YAEnD,EAAE,SAAS;YACX,EAAE,QAAQ;QACZ;QACA,IAAI,QAAQ;YACV,0BAA0B,GAC1B,iBAAiB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACA,GAAG,GACL;IACF;IACA,EAAE,MAAM,GAAG;IACX,IAAI,UAAU,UAAU;QACtB,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;QACA,GAAG,GACH,OAAO;IACT;IACA,IAAI,EAAE,QAAQ,EAAE;QACd,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;IACA,GAAG,GACL;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,aAAa,CAAC,EAAE,KAAK;IAC5B,IAAI,QAAoB,wCAAwC;IAEhE,OAAS;QACP,8CAA8C,GAC9C,IAAI,EAAE,SAAS,KAAK,GAAG;YACrB,YAAY;YACZ,IAAI,EAAE,SAAS,KAAK,GAAG;gBACrB,IAAI,UAAU,YAAY;oBACxB,OAAO;gBACT;gBACA,OAAY,2BAA2B;YACzC;QACF;QAEA,yBAAyB,GACzB,EAAE,YAAY,GAAG;QACjB,iDAAiD;QACjD,uDAAuD,GACvD,SAAS,MAAM,SAAS,CAAC,GAAG,GAAG,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC;QACnD,EAAE,SAAS;QACX,EAAE,QAAQ;QACV,IAAI,QAAQ;YACV,0BAA0B,GAC1B,iBAAiB,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC1B,OAAO;YACT;QACA,GAAG,GACL;IACF;IACA,EAAE,MAAM,GAAG;IACX,IAAI,UAAU,UAAU;QACtB,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;QACA,GAAG,GACH,OAAO;IACT;IACA,IAAI,EAAE,QAAQ,EAAE;QACd,0BAA0B,GAC1B,iBAAiB,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1B,OAAO;QACT;IACA,GAAG,GACL;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,OAAO,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI;IACjE,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG;IACnB,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,IAAI,GAAG;AACd;AAEA,IAAI;AAEJ,sBAAsB;IACpB,6BAA6B,GAC7B,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG;IAA0B,gBAAgB,GACjE,IAAI,OAAO,GAAG,GAAG,GAAG,GAAG;IAA0B,gCAAgC,GACjF,IAAI,OAAO,GAAG,GAAG,IAAI,GAAG;IAAyB,KAAK,GACtD,IAAI,OAAO,GAAG,GAAG,IAAI,IAAI;IAAwB,KAAK,GAEtD,IAAI,OAAO,GAAG,GAAG,IAAI,IAAI;IAAwB,kBAAkB,GACnE,IAAI,OAAO,GAAG,IAAI,IAAI,IAAI;IAAuB,KAAK,GACtD,IAAI,OAAO,GAAG,IAAI,KAAK,KAAK;IAAqB,KAAK,GACtD,IAAI,OAAO,GAAG,IAAI,KAAK,KAAK;IAAqB,KAAK,GACtD,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;IAAkB,KAAK,GACtD,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM;CAChC;AAGD;;CAEC,GACD,SAAS,QAAQ,CAAC;IAChB,EAAE,WAAW,GAAG,IAAI,EAAE,MAAM;IAE5B,sBAAsB,GACtB,KAAK,EAAE,IAAI,GAAG,uBAAuB;IAErC;GACC,GACD,EAAE,cAAc,GAAG,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,QAAQ;IACxD,EAAE,UAAU,GAAG,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,WAAW;IACvD,EAAE,UAAU,GAAG,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,WAAW;IACvD,EAAE,gBAAgB,GAAG,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,SAAS;IAE3D,EAAE,QAAQ,GAAG;IACb,EAAE,WAAW,GAAG;IAChB,EAAE,SAAS,GAAG;IACd,EAAE,MAAM,GAAG;IACX,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG,YAAY;IAC7C,EAAE,eAAe,GAAG;IACpB,EAAE,KAAK,GAAG;AACZ;AAGA,SAAS;IACP,IAAI,CAAC,IAAI,GAAG,MAAiB,oCAAoC;IACjE,IAAI,CAAC,MAAM,GAAG,GAAc,uBAAuB;IACnD,IAAI,CAAC,WAAW,GAAG,MAAW,wBAAwB;IACtD,IAAI,CAAC,gBAAgB,GAAG,GAAI,uBAAuB;IACnD,IAAI,CAAC,WAAW,GAAG,GAAS,6CAA6C;IACzE,IAAI,CAAC,OAAO,GAAG,GAAa,qCAAqC;IACjE,IAAI,CAAC,IAAI,GAAG,GAAgB,4CAA4C;IACxE,IAAI,CAAC,MAAM,GAAG,MAAc,oCAAoC;IAChE,IAAI,CAAC,OAAO,GAAG,GAAa,oCAAoC;IAChE,IAAI,CAAC,MAAM,GAAG,YAAY,wBAAwB;IAClD,IAAI,CAAC,UAAU,GAAG,CAAC,GAAK,kDAAkD;IAE1E,IAAI,CAAC,MAAM,GAAG,GAAI,qCAAqC;IACvD,IAAI,CAAC,MAAM,GAAG,GAAI,yBAAyB;IAC3C,IAAI,CAAC,MAAM,GAAG,GAAI,cAAc;IAEhC,IAAI,CAAC,MAAM,GAAG;IACd;;;;;GAKC,GAED,IAAI,CAAC,WAAW,GAAG;IACnB;;GAEC,GAED,IAAI,CAAC,IAAI,GAAG;IACZ;;;GAGC,GAED,IAAI,CAAC,IAAI,GAAG,MAAQ,oCAAoC;IAExD,IAAI,CAAC,KAAK,GAAG,GAAS,uCAAuC;IAC7D,IAAI,CAAC,SAAS,GAAG,GAAK,oCAAoC;IAC1D,IAAI,CAAC,SAAS,GAAG,GAAK,mBAAmB;IACzC,IAAI,CAAC,SAAS,GAAG,GAAK,eAAe;IAErC,IAAI,CAAC,UAAU,GAAG;IAClB;;;;GAIC,GAED,IAAI,CAAC,WAAW,GAAG;IACnB;;GAEC,GAED,IAAI,CAAC,YAAY,GAAG,GAAQ,wBAAwB;IACpD,IAAI,CAAC,UAAU,GAAG,GAAU,kBAAkB;IAC9C,IAAI,CAAC,eAAe,GAAG,GAAK,gCAAgC;IAC5D,IAAI,CAAC,QAAQ,GAAG,GAAY,6BAA6B;IACzD,IAAI,CAAC,WAAW,GAAG,GAAS,4BAA4B;IACxD,IAAI,CAAC,SAAS,GAAG,GAAW,yCAAyC;IAErE,IAAI,CAAC,WAAW,GAAG;IACnB;;GAEC,GAED,IAAI,CAAC,gBAAgB,GAAG;IACxB;;;GAGC,GAED,IAAI,CAAC,cAAc,GAAG;IACtB;;;GAGC,GACD,qDAAqD;IACrD,6BAA6B;IAC7B;;;GAGC,GAED,IAAI,CAAC,KAAK,GAAG,GAAO,4BAA4B;IAChD,IAAI,CAAC,QAAQ,GAAG,GAAI,gCAAgC;IAEpD,IAAI,CAAC,UAAU,GAAG;IAClB,mEAAmE,GAEnE,IAAI,CAAC,UAAU,GAAG,GAAG,kDAAkD;IAE3D,oBAAoB,GAEhC,iEAAiE,GAEjE,yEAAyE;IACzE,+DAA+D;IAC/D,8EAA8E;IAE9E,wDAAwD;IACxD,wCAAwC;IACxC,IAAI,CAAC,SAAS,GAAI,IAAI,MAAM,KAAK,CAAC,YAAY;IAC9C,IAAI,CAAC,SAAS,GAAI,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI;IACtD,IAAI,CAAC,OAAO,GAAM,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,IAAI;IACvD,KAAK,IAAI,CAAC,SAAS;IACnB,KAAK,IAAI,CAAC,SAAS;IACnB,KAAK,IAAI,CAAC,OAAO;IAEjB,IAAI,CAAC,MAAM,GAAK,MAAc,0BAA0B;IACxD,IAAI,CAAC,MAAM,GAAK,MAAc,2BAA2B;IACzD,IAAI,CAAC,OAAO,GAAI,MAAc,6BAA6B;IAE3D,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,KAAK,CAAC,WAAW;IAC3C,0DAA0D,GAE1D,wEAAwE;IACxE,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,IAAI,UAAU,IAAK,wCAAwC;IACvF,KAAK,IAAI,CAAC,IAAI;IAEd,IAAI,CAAC,QAAQ,GAAG,GAAiB,kCAAkC;IACnE,IAAI,CAAC,QAAQ,GAAG,GAAiB,gCAAgC;IACjE;;GAEC,GAED,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC,IAAI,UAAU,IAAI,yBAAyB;IACxE,KAAK,IAAI,CAAC,KAAK;IACf;GACC,GAED,IAAI,CAAC,KAAK,GAAG,GAAY,wCAAwC;IAEjE,IAAI,CAAC,WAAW,GAAG;IACnB;;;;;;;;;;;;;;;;;GAiBC,GAED,IAAI,CAAC,QAAQ,GAAG,GAAQ,0BAA0B;IAElD,IAAI,CAAC,KAAK,GAAG;IACb;;;GAGC,GAED,IAAI,CAAC,OAAO,GAAG,GAAS,kDAAkD;IAC1E,IAAI,CAAC,UAAU,GAAG,GAAM,iDAAiD;IACzE,IAAI,CAAC,OAAO,GAAG,GAAS,6CAA6C;IACrE,IAAI,CAAC,MAAM,GAAG,GAAU,yCAAyC;IAGjE,IAAI,CAAC,MAAM,GAAG;IACd;;GAEC,GACD,IAAI,CAAC,QAAQ,GAAG;AAChB;;GAEC,GAED,sEAAsE;AACtE,kDAAkD;AAClD,sBAAsB;AACtB;;;;GAIC,GACH;AAGA,SAAS,iBAAiB,IAAI;IAC5B,IAAI;IAEJ,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QACxB,OAAO,IAAI,MAAM;IACnB;IAEA,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG;IACjC,KAAK,SAAS,GAAG;IAEjB,IAAI,KAAK,KAAK;IACd,EAAE,OAAO,GAAG;IACZ,EAAE,WAAW,GAAG;IAEhB,IAAI,EAAE,IAAI,GAAG,GAAG;QACd,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI;IAChB,gDAAgD,GAClD;IACA,EAAE,MAAM,GAAI,EAAE,IAAI,GAAG,aAAa;IAClC,KAAK,KAAK,GAAG,AAAC,EAAE,IAAI,KAAK,IACvB,EAAG,sBAAsB;OAEzB,GAAG,wBAAwB;IAC7B,EAAE,UAAU,GAAG;IACf,MAAM,QAAQ,CAAC;IACf,OAAO;AACT;AAGA,SAAS,aAAa,IAAI;IACxB,IAAI,MAAM,iBAAiB;IAC3B,IAAI,QAAQ,MAAM;QAChB,QAAQ,KAAK,KAAK;IACpB;IACA,OAAO;AACT;AAGA,SAAS,iBAAiB,IAAI,EAAE,IAAI;IAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QAAE,OAAO;IAAgB;IACnD,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;QAAE,OAAO;IAAgB;IACpD,KAAK,KAAK,CAAC,MAAM,GAAG;IACpB,OAAO;AACT;AAGA,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ;IACvE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI,OAAO;IAEX,IAAI,UAAU,uBAAuB;QACnC,QAAQ;IACV;IAEA,IAAI,aAAa,GAAG;QAClB,OAAO;QACP,aAAa,CAAC;IAChB,OAEK,IAAI,aAAa,IAAI;QACxB,OAAO,GAAa,8BAA8B;QAClD,cAAc;IAChB;IAGA,IAAI,WAAW,KAAK,WAAW,iBAAiB,WAAW,cACzD,aAAa,KAAK,aAAa,MAAM,QAAQ,KAAK,QAAQ,KAC1D,WAAW,KAAK,WAAW,SAAS;QACpC,OAAO,IAAI,MAAM;IACnB;IAGA,IAAI,eAAe,GAAG;QACpB,aAAa;IACf;IACA,mCAAmC,GAEnC,IAAI,IAAI,IAAI;IAEZ,KAAK,KAAK,GAAG;IACb,EAAE,IAAI,GAAG;IAET,EAAE,IAAI,GAAG;IACT,EAAE,MAAM,GAAG;IACX,EAAE,MAAM,GAAG;IACX,EAAE,MAAM,GAAG,KAAK,EAAE,MAAM;IACxB,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG;IAEtB,EAAE,SAAS,GAAG,WAAW;IACzB,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS;IAC9B,EAAE,SAAS,GAAG,EAAE,SAAS,GAAG;IAC5B,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,IAAI,SAAS;IAE3D,EAAE,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG;IACrC,EAAE,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE,SAAS;IACpC,EAAE,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE,MAAM;IAEjC,oCAAoC;IACpC,2DAA2D;IAE3D,EAAE,WAAW,GAAG,KAAM,WAAW,GAAI,2BAA2B;IAEhE,EAAE,gBAAgB,GAAG,EAAE,WAAW,GAAG;IAErC,iEAAiE;IACjE,oCAAoC;IACpC,EAAE,WAAW,GAAG,IAAI,MAAM,IAAI,CAAC,EAAE,gBAAgB;IAEjD,kEAAkE;IAClE,kDAAkD;IAClD,EAAE,KAAK,GAAG,IAAI,EAAE,WAAW;IAE3B,6DAA6D;IAC7D,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW;IAEjC,EAAE,KAAK,GAAG;IACV,EAAE,QAAQ,GAAG;IACb,EAAE,MAAM,GAAG;IAEX,OAAO,aAAa;AACtB;AAEA,SAAS,YAAY,IAAI,EAAE,KAAK;IAC9B,OAAO,aAAa,MAAM,OAAO,YAAY,WAAW,eAAe;AACzE;AAGA,SAAS,QAAQ,IAAI,EAAE,KAAK;IAC1B,IAAI,WAAW;IACf,IAAI,KAAK,KAAK,6BAA6B;IAE3C,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACtB,QAAQ,WAAW,QAAQ,GAAG;QAC9B,OAAO,OAAO,IAAI,MAAM,kBAAkB;IAC5C;IAEA,IAAI,KAAK,KAAK;IAEd,IAAI,CAAC,KAAK,MAAM,IACX,CAAC,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,KACjC,EAAE,MAAM,KAAK,gBAAgB,UAAU,UAAW;QACrD,OAAO,IAAI,MAAM,AAAC,KAAK,SAAS,KAAK,IAAK,cAAc;IAC1D;IAEA,EAAE,IAAI,GAAG,MAAM,gBAAgB;IAC/B,YAAY,EAAE,UAAU;IACxB,EAAE,UAAU,GAAG;IAEf,oBAAoB,GACpB,IAAI,EAAE,MAAM,KAAK,YAAY;QAE3B,IAAI,EAAE,IAAI,KAAK,GAAG;YAChB,KAAK,KAAK,GAAG,GAAI,uBAAuB;YACxC,SAAS,GAAG;YACZ,SAAS,GAAG;YACZ,SAAS,GAAG;YACZ,IAAI,CAAC,EAAE,MAAM,EAAE;gBACb,SAAS,GAAG;gBACZ,SAAS,GAAG;gBACZ,SAAS,GAAG;gBACZ,SAAS,GAAG;gBACZ,SAAS,GAAG;gBACZ,SAAS,GAAG,EAAE,KAAK,KAAK,IAAI,IACf,EAAE,QAAQ,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAC1C,IAAI;gBACjB,SAAS,GAAG;gBACZ,EAAE,MAAM,GAAG;YACb,OACK;gBACH,SAAS,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IACtB,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IACtB,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IACxB,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IACvB,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GAAG,IAAI,EAAE;gBAEvC,SAAS,GAAG,EAAE,MAAM,CAAC,IAAI,GAAG;gBAC5B,SAAS,GAAG,AAAC,EAAE,MAAM,CAAC,IAAI,IAAI,IAAK;gBACnC,SAAS,GAAG,AAAC,EAAE,MAAM,CAAC,IAAI,IAAI,KAAM;gBACpC,SAAS,GAAG,AAAC,EAAE,MAAM,CAAC,IAAI,IAAI,KAAM;gBACpC,SAAS,GAAG,EAAE,KAAK,KAAK,IAAI,IACf,EAAE,QAAQ,IAAI,kBAAkB,EAAE,KAAK,GAAG,IAC1C,IAAI;gBACjB,SAAS,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG;gBAC1B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;oBAC3C,SAAS,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;oBACpC,SAAS,GAAG,AAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,IAAK;gBAC7C;gBACA,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;oBACjB,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,EAAE;gBAC3D;gBACA,EAAE,OAAO,GAAG;gBACZ,EAAE,MAAM,GAAG;YACb;QACF,OAEA;YACE,IAAI,SAAS,AAAC,aAAa,CAAC,AAAC,EAAE,MAAM,GAAG,KAAM,CAAC,KAAM;YACrD,IAAI,cAAc,CAAC;YAEnB,IAAI,EAAE,QAAQ,IAAI,kBAAkB,EAAE,KAAK,GAAG,GAAG;gBAC/C,cAAc;YAChB,OAAO,IAAI,EAAE,KAAK,GAAG,GAAG;gBACtB,cAAc;YAChB,OAAO,IAAI,EAAE,KAAK,KAAK,GAAG;gBACxB,cAAc;YAChB,OAAO;gBACL,cAAc;YAChB;YACA,UAAW,eAAe;YAC1B,IAAI,EAAE,QAAQ,KAAK,GAAG;gBAAE,UAAU;YAAa;YAC/C,UAAU,KAAM,SAAS;YAEzB,EAAE,MAAM,GAAG;YACX,YAAY,GAAG;YAEf,8CAA8C,GAC9C,IAAI,EAAE,QAAQ,KAAK,GAAG;gBACpB,YAAY,GAAG,KAAK,KAAK,KAAK;gBAC9B,YAAY,GAAG,KAAK,KAAK,GAAG;YAC9B;YACA,KAAK,KAAK,GAAG,GAAG,0BAA0B;QAC5C;IACF;IAEF,aAAa;IACX,IAAI,EAAE,MAAM,KAAK,aAAa;QAC5B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAA,YAAY,KAAI;YAChC,MAAM,EAAE,OAAO,EAAG,gCAAgC;YAElD,MAAO,EAAE,OAAO,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,EAAG;gBACnD,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;oBACpC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;wBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;oBACjE;oBACA,cAAc;oBACd,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;wBACpC;oBACF;gBACF;gBACA,SAAS,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,GAAG;gBACxC,EAAE,OAAO;YACX;YACA,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;gBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;YACjE;YACA,IAAI,EAAE,OAAO,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;gBACvC,EAAE,OAAO,GAAG;gBACZ,EAAE,MAAM,GAAG;YACb;QACF,OACK;YACH,EAAE,MAAM,GAAG;QACb;IACF;IACA,IAAI,EAAE,MAAM,KAAK,YAAY;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,CAAA,YAAY,KAAI;YAC/B,MAAM,EAAE,OAAO,EAAG,gCAAgC;YAClD,UAAU;YAEV,GAAG;gBACD,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;oBACpC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;wBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;oBACjE;oBACA,cAAc;oBACd,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;wBACpC,MAAM;wBACN;oBACF;gBACF;gBACA,oEAAoE;gBACpE,IAAI,EAAE,OAAO,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;oBACpC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,OAAO,MAAM;gBAChD,OAAO;oBACL,MAAM;gBACR;gBACA,SAAS,GAAG;YACd,QAAS,QAAQ,EAAG;YAEpB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;gBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;YACjE;YACA,IAAI,QAAQ,GAAG;gBACb,EAAE,OAAO,GAAG;gBACZ,EAAE,MAAM,GAAG;YACb;QACF,OACK;YACH,EAAE,MAAM,GAAG;QACb;IACF;IACA,IAAI,EAAE,MAAM,KAAK,eAAe;QAC9B,IAAI,EAAE,MAAM,CAAC,OAAO,CAAA,YAAY,KAAI;YAClC,MAAM,EAAE,OAAO,EAAG,gCAAgC;YAClD,UAAU;YAEV,GAAG;gBACD,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;oBACpC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;wBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;oBACjE;oBACA,cAAc;oBACd,MAAM,EAAE,OAAO;oBACf,IAAI,EAAE,OAAO,KAAK,EAAE,gBAAgB,EAAE;wBACpC,MAAM;wBACN;oBACF;gBACF;gBACA,oEAAoE;gBACpE,IAAI,EAAE,OAAO,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE;oBACvC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,OAAO,MAAM;gBACnD,OAAO;oBACL,MAAM;gBACR;gBACA,SAAS,GAAG;YACd,QAAS,QAAQ,EAAG;YAEpB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK;gBACpC,KAAK,KAAK,GAAG,MAAM,KAAK,KAAK,EAAE,EAAE,WAAW,EAAE,EAAE,OAAO,GAAG,KAAK;YACjE;YACA,IAAI,QAAQ,GAAG;gBACb,EAAE,MAAM,GAAG;YACb;QACF,OACK;YACH,EAAE,MAAM,GAAG;QACb;IACF;IACA,IAAI,EAAE,MAAM,KAAK,YAAY;QAC3B,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;YACjB,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,gBAAgB,EAAE;gBACtC,cAAc;YAChB;YACA,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,gBAAgB,EAAE;gBACvC,SAAS,GAAG,KAAK,KAAK,GAAG;gBACzB,SAAS,GAAG,AAAC,KAAK,KAAK,IAAI,IAAK;gBAChC,KAAK,KAAK,GAAG,GAAG,uBAAuB;gBACvC,EAAE,MAAM,GAAG;YACb;QACF,OACK;YACH,EAAE,MAAM,GAAG;QACb;IACF;IACF,QAAQ;IAEN,4CAA4C,GAC5C,IAAI,EAAE,OAAO,KAAK,GAAG;QACnB,cAAc;QACd,IAAI,KAAK,SAAS,KAAK,GAAG;YACxB;;;;;OAKC,GACD,EAAE,UAAU,GAAG,CAAC;YAChB,OAAO;QACT;IAEA;;;KAGC,GACH,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,UAAU,KAAK,cACpD,UAAU,UAAU;QACpB,OAAO,IAAI,MAAM;IACnB;IAEA,4DAA4D,GAC5D,IAAI,EAAE,MAAM,KAAK,gBAAgB,KAAK,QAAQ,KAAK,GAAG;QACpD,OAAO,IAAI,MAAM;IACnB;IAEA;GACC,GACD,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAE,SAAS,KAAK,KACxC,UAAU,cAAc,EAAE,MAAM,KAAK,cAAe;QACrD,IAAI,SAAS,AAAC,EAAE,QAAQ,KAAK,iBAAkB,aAAa,GAAG,SAC5D,EAAE,QAAQ,KAAK,QAAQ,YAAY,GAAG,SACrC,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG;QAEzC,IAAI,WAAW,qBAAqB,WAAW,gBAAgB;YAC7D,EAAE,MAAM,GAAG;QACb;QACA,IAAI,WAAW,gBAAgB,WAAW,mBAAmB;YAC3D,IAAI,KAAK,SAAS,KAAK,GAAG;gBACxB,EAAE,UAAU,GAAG,CAAC;YAChB,wCAAwC,GAC1C;YACA,OAAO;QACP;;;;;;OAMC,GACH;QACA,IAAI,WAAW,eAAe;YAC5B,IAAI,UAAU,iBAAiB;gBAC7B,MAAM,SAAS,CAAC;YAClB,OACK,IAAI,UAAU,SAAS;gBAE1B,MAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG;gBAChC;;SAEC,GACD,IAAI,UAAU,cAAc;oBAC1B,sBAAsB,GAAe,kBAAkB,GACvD,KAAK,EAAE,IAAI,GAAG,uBAAuB;oBAErC,IAAI,EAAE,SAAS,KAAK,GAAG;wBACrB,EAAE,QAAQ,GAAG;wBACb,EAAE,WAAW,GAAG;wBAChB,EAAE,MAAM,GAAG;oBACb;gBACF;YACF;YACA,cAAc;YACd,IAAI,KAAK,SAAS,KAAK,GAAG;gBACxB,EAAE,UAAU,GAAG,CAAC,GAAG,2CAA2C;gBAC9D,OAAO;YACT;QACF;IACF;IACA,sCAAsC;IACtC,sDAAsD;IAEtD,IAAI,UAAU,UAAU;QAAE,OAAO;IAAM;IACvC,IAAI,EAAE,IAAI,IAAI,GAAG;QAAE,OAAO;IAAc;IAExC,qBAAqB,GACrB,IAAI,EAAE,IAAI,KAAK,GAAG;QAChB,SAAS,GAAG,KAAK,KAAK,GAAG;QACzB,SAAS,GAAG,AAAC,KAAK,KAAK,IAAI,IAAK;QAChC,SAAS,GAAG,AAAC,KAAK,KAAK,IAAI,KAAM;QACjC,SAAS,GAAG,AAAC,KAAK,KAAK,IAAI,KAAM;QACjC,SAAS,GAAG,KAAK,QAAQ,GAAG;QAC5B,SAAS,GAAG,AAAC,KAAK,QAAQ,IAAI,IAAK;QACnC,SAAS,GAAG,AAAC,KAAK,QAAQ,IAAI,KAAM;QACpC,SAAS,GAAG,AAAC,KAAK,QAAQ,IAAI,KAAM;IACtC,OAEA;QACE,YAAY,GAAG,KAAK,KAAK,KAAK;QAC9B,YAAY,GAAG,KAAK,KAAK,GAAG;IAC9B;IAEA,cAAc;IACd;;GAEC,GACD,IAAI,EAAE,IAAI,GAAG,GAAG;QAAE,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI;IAAE;IACpC,gCAAgC,GAChC,OAAO,EAAE,OAAO,KAAK,IAAI,OAAO;AAClC;AAEA,SAAS,WAAW,IAAI;IACtB,IAAI;IAEJ,IAAI,CAAC,KAAI,WAAW,OAAM,CAAC,KAAK,KAAK,CAAA,WAAW,KAAI;QAClD,OAAO;IACT;IAEA,SAAS,KAAK,KAAK,CAAC,MAAM;IAC1B,IAAI,WAAW,cACb,WAAW,eACX,WAAW,cACX,WAAW,iBACX,WAAW,cACX,WAAW,cACX,WAAW,cACX;QACA,OAAO,IAAI,MAAM;IACnB;IAEA,KAAK,KAAK,GAAG;IAEb,OAAO,WAAW,aAAa,IAAI,MAAM,gBAAgB;AAC3D;AAGA;;;CAGC,GACD,SAAS,qBAAqB,IAAI,EAAE,UAAU;IAC5C,IAAI,aAAa,WAAW,MAAM;IAElC,IAAI;IACJ,IAAI,KAAK;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,IAAI,CAAC,KAAI,WAAW,OAAM,CAAC,KAAK,KAAK,CAAA,WAAW,KAAI;QAClD,OAAO;IACT;IAEA,IAAI,KAAK,KAAK;IACd,OAAO,EAAE,IAAI;IAEb,IAAI,SAAS,KAAM,SAAS,KAAK,EAAE,MAAM,KAAK,cAAe,EAAE,SAAS,EAAE;QACxE,OAAO;IACT;IAEA,sEAAsE,GACtE,IAAI,SAAS,GAAG;QACd,iDAAiD,GACjD,KAAK,KAAK,GAAG,QAAQ,KAAK,KAAK,EAAE,YAAY,YAAY;IAC3D;IAEA,EAAE,IAAI,GAAG,GAAK,wCAAwC;IAEtD,6DAA6D,GAC7D,IAAI,cAAc,EAAE,MAAM,EAAE;QAC1B,IAAI,SAAS,GAAG;YACd,sBAAsB,GACtB,KAAK,EAAE,IAAI,GAAG,uBAAuB;YACrC,EAAE,QAAQ,GAAG;YACb,EAAE,WAAW,GAAG;YAChB,EAAE,MAAM,GAAG;QACb;QACA,gBAAgB,GAChB,wDAAwD;QACxD,UAAU,IAAI,MAAM,IAAI,CAAC,EAAE,MAAM;QACjC,MAAM,QAAQ,CAAC,SAAS,YAAY,aAAa,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE;QACrE,aAAa;QACb,aAAa,EAAE,MAAM;IACvB;IACA,0CAA0C,GAC1C,QAAQ,KAAK,QAAQ;IACrB,OAAO,KAAK,OAAO;IACnB,QAAQ,KAAK,KAAK;IAClB,KAAK,QAAQ,GAAG;IAChB,KAAK,OAAO,GAAG;IACf,KAAK,KAAK,GAAG;IACb,YAAY;IACZ,MAAO,EAAE,SAAS,IAAI,UAAW;QAC/B,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,SAAS,GAAG,CAAC,YAAY,CAAC;QAChC,GAAG;YACD,2DAA2D,GAC3D,EAAE,KAAK,GAAG,CAAC,AAAC,EAAE,KAAK,IAAI,EAAE,UAAU,GAAI,EAAE,MAAM,CAAC,MAAM,YAAY,EAAE,IAAI,EAAE,SAAS;YAEnF,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC;YAExC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG;YAClB;QACF,QAAS,EAAE,EAAG;QACd,EAAE,QAAQ,GAAG;QACb,EAAE,SAAS,GAAG,YAAY;QAC1B,YAAY;IACd;IACA,EAAE,QAAQ,IAAI,EAAE,SAAS;IACzB,EAAE,WAAW,GAAG,EAAE,QAAQ;IAC1B,EAAE,MAAM,GAAG,EAAE,SAAS;IACtB,EAAE,SAAS,GAAG;IACd,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG,YAAY;IAC7C,EAAE,eAAe,GAAG;IACpB,KAAK,OAAO,GAAG;IACf,KAAK,KAAK,GAAG;IACb,KAAK,QAAQ,GAAG;IAChB,EAAE,IAAI,GAAG;IACT,OAAO;AACT;AAGA,QAAQ,WAAW,GAAG;AACtB,QAAQ,YAAY,GAAG;AACvB,QAAQ,YAAY,GAAG;AACvB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,WAAW,GAAG,sCAEtB;;;;;;;AAOA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/utils/strings.js"], "sourcesContent": ["// String encode/decode helpers\n'use strict';\n\n\nvar utils = require('./common');\n\n\n// Quick check if we can use fast array to bin string conversion\n//\n// - apply(Array) can fail on Android 2.2\n// - apply(Uint8Array) can fail on iOS 5.1 Safari\n//\nvar STR_APPLY_OK = true;\nvar STR_APPLY_UIA_OK = true;\n\ntry { String.fromCharCode.apply(null, [ 0 ]); } catch (__) { STR_APPLY_OK = false; }\ntry { String.fromCharCode.apply(null, new Uint8Array(1)); } catch (__) { STR_APPLY_UIA_OK = false; }\n\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new utils.Buf8(256);\nfor (var q = 0; q < 256; q++) {\n  _utf8len[q] = (q >= 252 ? 6 : q >= 248 ? 5 : q >= 240 ? 4 : q >= 224 ? 3 : q >= 192 ? 2 : 1);\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n\n\n// convert string to array (typed, when possible)\nexports.string2buf = function (str) {\n  var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n\n  // count binary size\n  for (m_pos = 0; m_pos < str_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos + 1 < str_len)) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  buf = new utils.Buf8(buf_len);\n\n  // convert\n  for (i = 0, m_pos = 0; i < buf_len; m_pos++) {\n    c = str.charCodeAt(m_pos);\n    if ((c & 0xfc00) === 0xd800 && (m_pos + 1 < str_len)) {\n      c2 = str.charCodeAt(m_pos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + ((c - 0xd800) << 10) + (c2 - 0xdc00);\n        m_pos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xC0 | (c >>> 6);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xE0 | (c >>> 12);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | (c >>> 18);\n      buf[i++] = 0x80 | (c >>> 12 & 0x3f);\n      buf[i++] = 0x80 | (c >>> 6 & 0x3f);\n      buf[i++] = 0x80 | (c & 0x3f);\n    }\n  }\n\n  return buf;\n};\n\n// Helper (used in 2 places)\nfunction buf2binstring(buf, len) {\n  // On Chrome, the arguments in a function call that are allowed is `65534`.\n  // If the length of the buffer is smaller than that, we can use this optimization,\n  // otherwise we will take a slower path.\n  if (len < 65534) {\n    if ((buf.subarray && STR_APPLY_UIA_OK) || (!buf.subarray && STR_APPLY_OK)) {\n      return String.fromCharCode.apply(null, utils.shrinkBuf(buf, len));\n    }\n  }\n\n  var result = '';\n  for (var i = 0; i < len; i++) {\n    result += String.fromCharCode(buf[i]);\n  }\n  return result;\n}\n\n\n// Convert byte array to binary string\nexports.buf2binstring = function (buf) {\n  return buf2binstring(buf, buf.length);\n};\n\n\n// Convert binary string (typed, when possible)\nexports.binstring2buf = function (str) {\n  var buf = new utils.Buf8(str.length);\n  for (var i = 0, len = buf.length; i < len; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n};\n\n\n// convert array to string\nexports.buf2string = function (buf, max) {\n  var i, out, c, c_len;\n  var len = max || buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len * 2);\n\n  for (out = 0, i = 0; i < len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) { utf16buf[out++] = c; continue; }\n\n    c_len = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (c_len > 4) { utf16buf[out++] = 0xfffd; i += c_len - 1; continue; }\n\n    // apply mask on first byte\n    c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (c_len > 1 && i < len) {\n      c = (c << 6) | (buf[i++] & 0x3f);\n      c_len--;\n    }\n\n    // terminated by end of string?\n    if (c_len > 1) { utf16buf[out++] = 0xfffd; continue; }\n\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | ((c >> 10) & 0x3ff);\n      utf16buf[out++] = 0xdc00 | (c & 0x3ff);\n    }\n  }\n\n  return buf2binstring(utf16buf, out);\n};\n\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nexports.utf8border = function (buf, max) {\n  var pos;\n\n  max = max || buf.length;\n  if (max > buf.length) { max = buf.length; }\n\n  // go back from last position, until start of sequence found\n  pos = max - 1;\n  while (pos >= 0 && (buf[pos] & 0xC0) === 0x80) { pos--; }\n\n  // Very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) { return max; }\n\n  // If we came to start of buffer - that means buffer is too small,\n  // return max too.\n  if (pos === 0) { return max; }\n\n  return (pos + _utf8len[buf[pos]] > max) ? pos : max;\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B;AAGA,IAAI;AAGJ,gEAAgE;AAChE,EAAE;AACF,yCAAyC;AACzC,iDAAiD;AACjD,EAAE;AACF,IAAI,eAAe;AACnB,IAAI,mBAAmB;AAEvB,IAAI;IAAE,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM;QAAE;KAAG;AAAG,EAAE,OAAO,IAAI;IAAE,eAAe;AAAO;AACnF,IAAI;IAAE,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,WAAW;AAAK,EAAE,OAAO,IAAI;IAAE,mBAAmB;AAAO;AAGnG,iEAAiE;AACjE,oFAAoF;AACpF,6CAA6C;AAC7C,IAAI,WAAW,IAAI,MAAM,IAAI,CAAC;AAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;IAC5B,QAAQ,CAAC,EAAE,GAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI;AAC5F;AACA,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,yBAAyB;AAG5D,iDAAiD;AACjD,QAAQ,UAAU,GAAG,SAAU,GAAG;IAChC,IAAI,KAAK,GAAG,IAAI,OAAO,GAAG,UAAU,IAAI,MAAM,EAAE,UAAU;IAE1D,oBAAoB;IACpB,IAAK,QAAQ,GAAG,QAAQ,SAAS,QAAS;QACxC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,CAAC,IAAI,MAAM,MAAM,UAAW,QAAQ,IAAI,SAAU;YACpD,KAAK,IAAI,UAAU,CAAC,QAAQ;YAC5B,IAAI,CAAC,KAAK,MAAM,MAAM,QAAQ;gBAC5B,IAAI,UAAU,CAAC,AAAC,IAAI,UAAW,EAAE,IAAI,CAAC,KAAK,MAAM;gBACjD;YACF;QACF;QACA,WAAW,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,UAAU,IAAI;IAC9D;IAEA,kBAAkB;IAClB,MAAM,IAAI,MAAM,IAAI,CAAC;IAErB,UAAU;IACV,IAAK,IAAI,GAAG,QAAQ,GAAG,IAAI,SAAS,QAAS;QAC3C,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,CAAC,IAAI,MAAM,MAAM,UAAW,QAAQ,IAAI,SAAU;YACpD,KAAK,IAAI,UAAU,CAAC,QAAQ;YAC5B,IAAI,CAAC,KAAK,MAAM,MAAM,QAAQ;gBAC5B,IAAI,UAAU,CAAC,AAAC,IAAI,UAAW,EAAE,IAAI,CAAC,KAAK,MAAM;gBACjD;YACF;QACF;QACA,IAAI,IAAI,MAAM;YACZ,YAAY,GACZ,GAAG,CAAC,IAAI,GAAG;QACb,OAAO,IAAI,IAAI,OAAO;YACpB,aAAa,GACb,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QACzB,OAAO,IAAI,IAAI,SAAS;YACtB,eAAe,GACf,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,IAAI;YAC7B,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QACzB,OAAO;YACL,cAAc,GACd,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM;YACzB,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,KAAK;YAC9B,GAAG,CAAC,IAAI,GAAG,OAAQ,MAAM,IAAI;YAC7B,GAAG,CAAC,IAAI,GAAG,OAAQ,IAAI;QACzB;IACF;IAEA,OAAO;AACT;AAEA,4BAA4B;AAC5B,SAAS,cAAc,GAAG,EAAE,GAAG;IAC7B,2EAA2E;IAC3E,kFAAkF;IAClF,wCAAwC;IACxC,IAAI,MAAM,OAAO;QACf,IAAI,AAAC,IAAI,QAAQ,IAAI,oBAAsB,CAAC,IAAI,QAAQ,IAAI,cAAe;YACzE,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK;QAC9D;IACF;IAEA,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,UAAU,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE;IACtC;IACA,OAAO;AACT;AAGA,sCAAsC;AACtC,QAAQ,aAAa,GAAG,SAAU,GAAG;IACnC,OAAO,cAAc,KAAK,IAAI,MAAM;AACtC;AAGA,+CAA+C;AAC/C,QAAQ,aAAa,GAAG,SAAU,GAAG;IACnC,IAAI,MAAM,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM;IACnC,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;QAC9C,GAAG,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC;IAC1B;IACA,OAAO;AACT;AAGA,0BAA0B;AAC1B,QAAQ,UAAU,GAAG,SAAU,GAAG,EAAE,GAAG;IACrC,IAAI,GAAG,KAAK,GAAG;IACf,IAAI,MAAM,OAAO,IAAI,MAAM;IAE3B,iDAAiD;IACjD,4DAA4D;IAC5D,kDAAkD;IAClD,IAAI,WAAW,IAAI,MAAM,MAAM;IAE/B,IAAK,MAAM,GAAG,IAAI,GAAG,IAAI,KAAM;QAC7B,IAAI,GAAG,CAAC,IAAI;QACZ,sBAAsB;QACtB,IAAI,IAAI,MAAM;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAG;QAAU;QAE/C,QAAQ,QAAQ,CAAC,EAAE;QACnB,wBAAwB;QACxB,IAAI,QAAQ,GAAG;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAQ,KAAK,QAAQ;YAAG;QAAU;QAErE,2BAA2B;QAC3B,KAAK,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO;QAC/C,gBAAgB;QAChB,MAAO,QAAQ,KAAK,IAAI,IAAK;YAC3B,IAAI,AAAC,KAAK,IAAM,GAAG,CAAC,IAAI,GAAG;YAC3B;QACF;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,GAAG;YAAE,QAAQ,CAAC,MAAM,GAAG;YAAQ;QAAU;QAErD,IAAI,IAAI,SAAS;YACf,QAAQ,CAAC,MAAM,GAAG;QACpB,OAAO;YACL,KAAK;YACL,QAAQ,CAAC,MAAM,GAAG,SAAU,AAAC,KAAK,KAAM;YACxC,QAAQ,CAAC,MAAM,GAAG,SAAU,IAAI;QAClC;IACF;IAEA,OAAO,cAAc,UAAU;AACjC;AAGA,kDAAkD;AAClD,uDAAuD;AACvD,+CAA+C;AAC/C,EAAE;AACF,2BAA2B;AAC3B,oCAAoC;AACpC,QAAQ,UAAU,GAAG,SAAU,GAAG,EAAE,GAAG;IACrC,IAAI;IAEJ,MAAM,OAAO,IAAI,MAAM;IACvB,IAAI,MAAM,IAAI,MAAM,EAAE;QAAE,MAAM,IAAI,MAAM;IAAE;IAE1C,4DAA4D;IAC5D,MAAM,MAAM;IACZ,MAAO,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,MAAM,KAAM;QAAE;IAAO;IAExD,kCAAkC;IAClC,yDAAyD;IACzD,IAAI,MAAM,GAAG;QAAE,OAAO;IAAK;IAE3B,kEAAkE;IAClE,kBAAkB;IAClB,IAAI,QAAQ,GAAG;QAAE,OAAO;IAAK;IAE7B,OAAO,AAAC,MAAM,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAO,MAAM;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/zstream.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nfunction ZStream() {\n  /* next input byte */\n  this.input = null; // JS specific, because we have no pointers\n  this.next_in = 0;\n  /* number of bytes available at input */\n  this.avail_in = 0;\n  /* total number of input bytes read so far */\n  this.total_in = 0;\n  /* next output byte should be put there */\n  this.output = null; // JS specific, because we have no pointers\n  this.next_out = 0;\n  /* remaining free space at output */\n  this.avail_out = 0;\n  /* total number of bytes output so far */\n  this.total_out = 0;\n  /* last error message, NULL if no error */\n  this.msg = ''/*Z_NULL*/;\n  /* not visible by applications */\n  this.state = null;\n  /* best guess about the data type: binary or text */\n  this.data_type = 2/*Z_UNKNOWN*/;\n  /* adler32 value of the uncompressed data */\n  this.adler = 0;\n}\n\nmodule.exports = ZStream;\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,SAAS;IACP,mBAAmB,GACnB,IAAI,CAAC,KAAK,GAAG,MAAM,2CAA2C;IAC9D,IAAI,CAAC,OAAO,GAAG;IACf,sCAAsC,GACtC,IAAI,CAAC,QAAQ,GAAG;IAChB,2CAA2C,GAC3C,IAAI,CAAC,QAAQ,GAAG;IAChB,wCAAwC,GACxC,IAAI,CAAC,MAAM,GAAG,MAAM,2CAA2C;IAC/D,IAAI,CAAC,QAAQ,GAAG;IAChB,kCAAkC,GAClC,IAAI,CAAC,SAAS,GAAG;IACjB,uCAAuC,GACvC,IAAI,CAAC,SAAS,GAAG;IACjB,wCAAwC,GACxC,IAAI,CAAC,GAAG,GAAG,GAAE,QAAQ;IACrB,+BAA+B,GAC/B,IAAI,CAAC,KAAK,GAAG;IACb,kDAAkD,GAClD,IAAI,CAAC,SAAS,GAAG,EAAC,WAAW;IAC7B,0CAA0C,GAC1C,IAAI,CAAC,KAAK,GAAG;AACf;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/deflate.js"], "sourcesContent": ["'use strict';\n\n\nvar zlib_deflate = require('./zlib/deflate');\nvar utils        = require('./utils/common');\nvar strings      = require('./utils/strings');\nvar msg          = require('./zlib/messages');\nvar ZStream      = require('./zlib/zstream');\n\nvar toString = Object.prototype.toString;\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\nvar Z_NO_FLUSH      = 0;\nvar Z_FINISH        = 4;\n\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\nvar Z_SYNC_FLUSH    = 2;\n\nvar Z_DEFAULT_COMPRESSION = -1;\n\nvar Z_DEFAULT_STRATEGY    = 0;\n\nvar Z_DEFLATED  = 8;\n\n/* ===========================================================================*/\n\n\n/**\n * class Deflate\n *\n * Generic JS-style wrapper for zlib calls. If you don't need\n * streaming behaviour - use more simple functions: [[deflate]],\n * [[deflateRaw]] and [[gzip]].\n **/\n\n/* internal\n * Deflate.chunks -> Array\n *\n * Chunks of output data, if [[Deflate#onData]] not overridden.\n **/\n\n/**\n * Deflate.result -> Uint8Array|Array\n *\n * Compressed result, generated by default [[Deflate#onData]]\n * and [[Deflate#onEnd]] handlers. Filled after you push last chunk\n * (call [[Deflate#push]] with `Z_FINISH` / `true` param)  or if you\n * push a chunk with explicit flush (call [[Deflate#push]] with\n * `Z_SYNC_FLUSH` param).\n **/\n\n/**\n * Deflate.err -> Number\n *\n * Error code after deflate finished. 0 (Z_OK) on success.\n * You will not need it in real life, because deflate errors\n * are possible only on wrong options or bad `onData` / `onEnd`\n * custom handlers.\n **/\n\n/**\n * Deflate.msg -> String\n *\n * Error message, if [[Deflate.err]] != 0\n **/\n\n\n/**\n * new Deflate(options)\n * - options (Object): zlib deflate options.\n *\n * Creates new deflator instance with specified params. Throws exception\n * on bad params. Supported options:\n *\n * - `level`\n * - `windowBits`\n * - `memLevel`\n * - `strategy`\n * - `dictionary`\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Additional options, for internal needs:\n *\n * - `chunkSize` - size of generated data chunks (16K by default)\n * - `raw` (Boolean) - do raw deflate\n * - `gzip` (Boolean) - create gzip wrapper\n * - `to` (String) - if equal to 'string', then result will be \"binary string\"\n *    (each char code [0..255])\n * - `header` (Object) - custom header for gzip\n *   - `text` (Boolean) - true if compressed data believed to be text\n *   - `time` (Number) - modification time, unix timestamp\n *   - `os` (Number) - operation system code\n *   - `extra` (Array) - array of bytes with extra data (max 65536)\n *   - `name` (String) - file name (binary string)\n *   - `comment` (String) - comment (binary string)\n *   - `hcrc` (Boolean) - true if header crc should be added\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , chunk1 = Uint8Array([1,2,3,4,5,6,7,8,9])\n *   , chunk2 = Uint8Array([10,11,12,13,14,15,16,17,18,19]);\n *\n * var deflate = new pako.Deflate({ level: 3});\n *\n * deflate.push(chunk1, false);\n * deflate.push(chunk2, true);  // true -> last chunk\n *\n * if (deflate.err) { throw new Error(deflate.err); }\n *\n * console.log(deflate.result);\n * ```\n **/\nfunction Deflate(options) {\n  if (!(this instanceof Deflate)) return new Deflate(options);\n\n  this.options = utils.assign({\n    level: Z_DEFAULT_COMPRESSION,\n    method: Z_DEFLATED,\n    chunkSize: 16384,\n    windowBits: 15,\n    memLevel: 8,\n    strategy: Z_DEFAULT_STRATEGY,\n    to: ''\n  }, options || {});\n\n  var opt = this.options;\n\n  if (opt.raw && (opt.windowBits > 0)) {\n    opt.windowBits = -opt.windowBits;\n  }\n\n  else if (opt.gzip && (opt.windowBits > 0) && (opt.windowBits < 16)) {\n    opt.windowBits += 16;\n  }\n\n  this.err    = 0;      // error code, if happens (0 = Z_OK)\n  this.msg    = '';     // error message\n  this.ended  = false;  // used to avoid multiple onEnd() calls\n  this.chunks = [];     // chunks of compressed data\n\n  this.strm = new ZStream();\n  this.strm.avail_out = 0;\n\n  var status = zlib_deflate.deflateInit2(\n    this.strm,\n    opt.level,\n    opt.method,\n    opt.windowBits,\n    opt.memLevel,\n    opt.strategy\n  );\n\n  if (status !== Z_OK) {\n    throw new Error(msg[status]);\n  }\n\n  if (opt.header) {\n    zlib_deflate.deflateSetHeader(this.strm, opt.header);\n  }\n\n  if (opt.dictionary) {\n    var dict;\n    // Convert data if needed\n    if (typeof opt.dictionary === 'string') {\n      // If we need to compress text, change encoding to utf8.\n      dict = strings.string2buf(opt.dictionary);\n    } else if (toString.call(opt.dictionary) === '[object ArrayBuffer]') {\n      dict = new Uint8Array(opt.dictionary);\n    } else {\n      dict = opt.dictionary;\n    }\n\n    status = zlib_deflate.deflateSetDictionary(this.strm, dict);\n\n    if (status !== Z_OK) {\n      throw new Error(msg[status]);\n    }\n\n    this._dict_set = true;\n  }\n}\n\n/**\n * Deflate#push(data[, mode]) -> Boolean\n * - data (Uint8Array|Array|ArrayBuffer|String): input data. Strings will be\n *   converted to utf8 byte sequence.\n * - mode (Number|Boolean): 0..6 for corresponding Z_NO_FLUSH..Z_TREE modes.\n *   See constants. Skipped or `false` means Z_NO_FLUSH, `true` means Z_FINISH.\n *\n * Sends input data to deflate pipe, generating [[Deflate#onData]] calls with\n * new compressed chunks. Returns `true` on success. The last data block must have\n * mode Z_FINISH (or `true`). That will flush internal pending buffers and call\n * [[Deflate#onEnd]]. For interim explicit flushes (without ending the stream) you\n * can use mode Z_SYNC_FLUSH, keeping the compression context.\n *\n * On fail call [[Deflate#onEnd]] with error code and return false.\n *\n * We strongly recommend to use `Uint8Array` on input for best speed (output\n * array format is detected automatically). Also, don't skip last param and always\n * use the same type in your code (boolean or number). That will improve JS speed.\n *\n * For regular `Array`-s make sure all elements are [0..255].\n *\n * ##### Example\n *\n * ```javascript\n * push(chunk, false); // push one of data chunks\n * ...\n * push(chunk, true);  // push last chunk\n * ```\n **/\nDeflate.prototype.push = function (data, mode) {\n  var strm = this.strm;\n  var chunkSize = this.options.chunkSize;\n  var status, _mode;\n\n  if (this.ended) { return false; }\n\n  _mode = (mode === ~~mode) ? mode : ((mode === true) ? Z_FINISH : Z_NO_FLUSH);\n\n  // Convert data if needed\n  if (typeof data === 'string') {\n    // If we need to compress text, change encoding to utf8.\n    strm.input = strings.string2buf(data);\n  } else if (toString.call(data) === '[object ArrayBuffer]') {\n    strm.input = new Uint8Array(data);\n  } else {\n    strm.input = data;\n  }\n\n  strm.next_in = 0;\n  strm.avail_in = strm.input.length;\n\n  do {\n    if (strm.avail_out === 0) {\n      strm.output = new utils.Buf8(chunkSize);\n      strm.next_out = 0;\n      strm.avail_out = chunkSize;\n    }\n    status = zlib_deflate.deflate(strm, _mode);    /* no bad return value */\n\n    if (status !== Z_STREAM_END && status !== Z_OK) {\n      this.onEnd(status);\n      this.ended = true;\n      return false;\n    }\n    if (strm.avail_out === 0 || (strm.avail_in === 0 && (_mode === Z_FINISH || _mode === Z_SYNC_FLUSH))) {\n      if (this.options.to === 'string') {\n        this.onData(strings.buf2binstring(utils.shrinkBuf(strm.output, strm.next_out)));\n      } else {\n        this.onData(utils.shrinkBuf(strm.output, strm.next_out));\n      }\n    }\n  } while ((strm.avail_in > 0 || strm.avail_out === 0) && status !== Z_STREAM_END);\n\n  // Finalize on the last chunk.\n  if (_mode === Z_FINISH) {\n    status = zlib_deflate.deflateEnd(this.strm);\n    this.onEnd(status);\n    this.ended = true;\n    return status === Z_OK;\n  }\n\n  // callback interim results if Z_SYNC_FLUSH.\n  if (_mode === Z_SYNC_FLUSH) {\n    this.onEnd(Z_OK);\n    strm.avail_out = 0;\n    return true;\n  }\n\n  return true;\n};\n\n\n/**\n * Deflate#onData(chunk) -> Void\n * - chunk (Uint8Array|Array|String): output data. Type of array depends\n *   on js engine support. When string output requested, each chunk\n *   will be string.\n *\n * By default, stores data blocks in `chunks[]` property and glue\n * those in `onEnd`. Override this handler, if you need another behaviour.\n **/\nDeflate.prototype.onData = function (chunk) {\n  this.chunks.push(chunk);\n};\n\n\n/**\n * Deflate#onEnd(status) -> Void\n * - status (Number): deflate status. 0 (Z_OK) on success,\n *   other if not.\n *\n * Called once after you tell deflate that the input stream is\n * complete (Z_FINISH) or should be flushed (Z_SYNC_FLUSH)\n * or if an error happened. By default - join collected chunks,\n * free memory and fill `results` / `err` properties.\n **/\nDeflate.prototype.onEnd = function (status) {\n  // On success - join\n  if (status === Z_OK) {\n    if (this.options.to === 'string') {\n      this.result = this.chunks.join('');\n    } else {\n      this.result = utils.flattenChunks(this.chunks);\n    }\n  }\n  this.chunks = [];\n  this.err = status;\n  this.msg = this.strm.msg;\n};\n\n\n/**\n * deflate(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * Compress `data` with deflate algorithm and `options`.\n *\n * Supported options are:\n *\n * - level\n * - windowBits\n * - memLevel\n * - strategy\n * - dictionary\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Sugar (options):\n *\n * - `raw` (Boolean) - say that we work with raw stream, if you don't wish to specify\n *   negative windowBits implicitly.\n * - `to` (String) - if equal to 'string', then result will be \"binary string\"\n *    (each char code [0..255])\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , data = Uint8Array([1,2,3,4,5,6,7,8,9]);\n *\n * console.log(pako.deflate(data));\n * ```\n **/\nfunction deflate(input, options) {\n  var deflator = new Deflate(options);\n\n  deflator.push(input, true);\n\n  // That will never happens, if you don't cheat with options :)\n  if (deflator.err) { throw deflator.msg || msg[deflator.err]; }\n\n  return deflator.result;\n}\n\n\n/**\n * deflateRaw(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * The same as [[deflate]], but creates raw data, without wrapper\n * (header and adler32 crc).\n **/\nfunction deflateRaw(input, options) {\n  options = options || {};\n  options.raw = true;\n  return deflate(input, options);\n}\n\n\n/**\n * gzip(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to compress.\n * - options (Object): zlib deflate options.\n *\n * The same as [[deflate]], but create gzip wrapper instead of\n * deflate one.\n **/\nfunction gzip(input, options) {\n  options = options || {};\n  options.gzip = true;\n  return deflate(input, options);\n}\n\n\nexports.Deflate = Deflate;\nexports.deflate = deflate;\nexports.deflateRaw = deflateRaw;\nexports.gzip = gzip;\n"], "names": [], "mappings": "AAAA;AAGA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,WAAW,OAAO,SAAS,CAAC,QAAQ;AAExC,8EAA8E,GAC9E,8EAA8E,GAE9E,IAAI,aAAkB;AACtB,IAAI,WAAkB;AAEtB,IAAI,OAAkB;AACtB,IAAI,eAAkB;AACtB,IAAI,eAAkB;AAEtB,IAAI,wBAAwB,CAAC;AAE7B,IAAI,qBAAwB;AAE5B,IAAI,aAAc;AAElB,8EAA8E,GAG9E;;;;;;EAME,GAEF;;;;EAIE,GAEF;;;;;;;;EAQE,GAEF;;;;;;;EAOE,GAEF;;;;EAIE,GAGF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgDE,GACF,SAAS,QAAQ,OAAO;IACtB,IAAI,CAAC,CAAC,IAAI,YAAY,OAAO,GAAG,OAAO,IAAI,QAAQ;IAEnD,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC;QAC1B,OAAO;QACP,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,UAAU;QACV,UAAU;QACV,IAAI;IACN,GAAG,WAAW,CAAC;IAEf,IAAI,MAAM,IAAI,CAAC,OAAO;IAEtB,IAAI,IAAI,GAAG,IAAK,IAAI,UAAU,GAAG,GAAI;QACnC,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU;IAClC,OAEK,IAAI,IAAI,IAAI,IAAK,IAAI,UAAU,GAAG,KAAO,IAAI,UAAU,GAAG,IAAK;QAClE,IAAI,UAAU,IAAI;IACpB;IAEA,IAAI,CAAC,GAAG,GAAM,GAAQ,oCAAoC;IAC1D,IAAI,CAAC,GAAG,GAAM,IAAQ,gBAAgB;IACtC,IAAI,CAAC,KAAK,GAAI,OAAQ,uCAAuC;IAC7D,IAAI,CAAC,MAAM,GAAG,EAAE,EAAM,4BAA4B;IAElD,IAAI,CAAC,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG;IAEtB,IAAI,SAAS,aAAa,YAAY,CACpC,IAAI,CAAC,IAAI,EACT,IAAI,KAAK,EACT,IAAI,MAAM,EACV,IAAI,UAAU,EACd,IAAI,QAAQ,EACZ,IAAI,QAAQ;IAGd,IAAI,WAAW,MAAM;QACnB,MAAM,IAAI,MAAM,GAAG,CAAC,OAAO;IAC7B;IAEA,IAAI,IAAI,MAAM,EAAE;QACd,aAAa,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;IACrD;IAEA,IAAI,IAAI,UAAU,EAAE;QAClB,IAAI;QACJ,yBAAyB;QACzB,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU;YACtC,wDAAwD;YACxD,OAAO,QAAQ,UAAU,CAAC,IAAI,UAAU;QAC1C,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,UAAU,MAAM,wBAAwB;YACnE,OAAO,IAAI,WAAW,IAAI,UAAU;QACtC,OAAO;YACL,OAAO,IAAI,UAAU;QACvB;QAEA,SAAS,aAAa,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE;QAEtD,IAAI,WAAW,MAAM;YACnB,MAAM,IAAI,MAAM,GAAG,CAAC,OAAO;QAC7B;QAEA,IAAI,CAAC,SAAS,GAAG;IACnB;AACF;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BE,GACF,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,IAAI;IAC3C,IAAI,OAAO,IAAI,CAAC,IAAI;IACpB,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;IACtC,IAAI,QAAQ;IAEZ,IAAI,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO;IAAO;IAEhC,QAAQ,AAAC,SAAS,CAAC,CAAC,OAAQ,OAAQ,AAAC,SAAS,OAAQ,WAAW;IAEjE,yBAAyB;IACzB,IAAI,OAAO,SAAS,UAAU;QAC5B,wDAAwD;QACxD,KAAK,KAAK,GAAG,QAAQ,UAAU,CAAC;IAClC,OAAO,IAAI,SAAS,IAAI,CAAC,UAAU,wBAAwB;QACzD,KAAK,KAAK,GAAG,IAAI,WAAW;IAC9B,OAAO;QACL,KAAK,KAAK,GAAG;IACf;IAEA,KAAK,OAAO,GAAG;IACf,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,MAAM;IAEjC,GAAG;QACD,IAAI,KAAK,SAAS,KAAK,GAAG;YACxB,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC;YAC7B,KAAK,QAAQ,GAAG;YAChB,KAAK,SAAS,GAAG;QACnB;QACA,SAAS,aAAa,OAAO,CAAC,MAAM,QAAW,uBAAuB;QAEtE,IAAI,WAAW,gBAAgB,WAAW,MAAM;YAC9C,IAAI,CAAC,KAAK,CAAC;YACX,IAAI,CAAC,KAAK,GAAG;YACb,OAAO;QACT;QACA,IAAI,KAAK,SAAS,KAAK,KAAM,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,YAAY,UAAU,YAAY,GAAI;YACnG,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,UAAU;gBAChC,IAAI,CAAC,MAAM,CAAC,QAAQ,aAAa,CAAC,MAAM,SAAS,CAAC,KAAK,MAAM,EAAE,KAAK,QAAQ;YAC9E,OAAO;gBACL,IAAI,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,MAAM,EAAE,KAAK,QAAQ;YACxD;QACF;IACF,QAAS,CAAC,KAAK,QAAQ,GAAG,KAAK,KAAK,SAAS,KAAK,CAAC,KAAK,WAAW,aAAc;IAEjF,8BAA8B;IAC9B,IAAI,UAAU,UAAU;QACtB,SAAS,aAAa,UAAU,CAAC,IAAI,CAAC,IAAI;QAC1C,IAAI,CAAC,KAAK,CAAC;QACX,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,WAAW;IACpB;IAEA,4CAA4C;IAC5C,IAAI,UAAU,cAAc;QAC1B,IAAI,CAAC,KAAK,CAAC;QACX,KAAK,SAAS,GAAG;QACjB,OAAO;IACT;IAEA,OAAO;AACT;AAGA;;;;;;;;EAQE,GACF,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;IACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACnB;AAGA;;;;;;;;;EASE,GACF,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAU,MAAM;IACxC,oBAAoB;IACpB,IAAI,WAAW,MAAM;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,UAAU;YAChC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC,OAAO;YACL,IAAI,CAAC,MAAM,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,MAAM;QAC/C;IACF;IACA,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG;AAC1B;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCE,GACF,SAAS,QAAQ,KAAK,EAAE,OAAO;IAC7B,IAAI,WAAW,IAAI,QAAQ;IAE3B,SAAS,IAAI,CAAC,OAAO;IAErB,8DAA8D;IAC9D,IAAI,SAAS,GAAG,EAAE;QAAE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC;IAAE;IAE7D,OAAO,SAAS,MAAM;AACxB;AAGA;;;;;;;EAOE,GACF,SAAS,WAAW,KAAK,EAAE,OAAO;IAChC,UAAU,WAAW,CAAC;IACtB,QAAQ,GAAG,GAAG;IACd,OAAO,QAAQ,OAAO;AACxB;AAGA;;;;;;;EAOE,GACF,SAAS,KAAK,KAAK,EAAE,OAAO;IAC1B,UAAU,WAAW,CAAC;IACtB,QAAQ,IAAI,GAAG;IACf,OAAO,QAAQ,OAAO;AACxB;AAGA,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/inffast.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\n// See state defs from inflate.js\nvar BAD = 30;       /* got a data error -- remain here until reset */\nvar TYPE = 12;      /* i: waiting for type bits, including last-flag bit */\n\n/*\n   Decode literal, length, and distance codes and write out the resulting\n   literal and match bytes until either not enough input or output is\n   available, an end-of-block is encountered, or a data error is encountered.\n   When large enough input and output buffers are supplied to inflate(), for\n   example, a 16K input buffer and a 64K output buffer, more than 95% of the\n   inflate execution time is spent in this routine.\n\n   Entry assumptions:\n\n        state.mode === LEN\n        strm.avail_in >= 6\n        strm.avail_out >= 258\n        start >= strm.avail_out\n        state.bits < 8\n\n   On return, state.mode is one of:\n\n        LEN -- ran out of enough output space or enough available input\n        TYPE -- reached end of block code, inflate() to interpret next block\n        BAD -- error in block data\n\n   Notes:\n\n    - The maximum input bits used by a length/distance pair is 15 bits for the\n      length code, 5 bits for the length extra, 15 bits for the distance code,\n      and 13 bits for the distance extra.  This totals 48 bits, or six bytes.\n      Therefore if strm.avail_in >= 6, then there is enough input to avoid\n      checking for available input while decoding.\n\n    - The maximum bytes that a single length/distance pair can output is 258\n      bytes, which is the maximum length that can be coded.  inflate_fast()\n      requires strm.avail_out >= 258 for each loop to avoid checking for\n      output space.\n */\nmodule.exports = function inflate_fast(strm, start) {\n  var state;\n  var _in;                    /* local strm.input */\n  var last;                   /* have enough input while in < last */\n  var _out;                   /* local strm.output */\n  var beg;                    /* inflate()'s initial strm.output */\n  var end;                    /* while out < end, enough space available */\n//#ifdef INFLATE_STRICT\n  var dmax;                   /* maximum distance from zlib header */\n//#endif\n  var wsize;                  /* window size or zero if not using window */\n  var whave;                  /* valid bytes in the window */\n  var wnext;                  /* window write index */\n  // Use `s_window` instead `window`, avoid conflict with instrumentation tools\n  var s_window;               /* allocated sliding window, if wsize != 0 */\n  var hold;                   /* local strm.hold */\n  var bits;                   /* local strm.bits */\n  var lcode;                  /* local strm.lencode */\n  var dcode;                  /* local strm.distcode */\n  var lmask;                  /* mask for first level of length codes */\n  var dmask;                  /* mask for first level of distance codes */\n  var here;                   /* retrieved table entry */\n  var op;                     /* code bits, operation, extra bits, or */\n                              /*  window position, window bytes to copy */\n  var len;                    /* match length, unused bytes */\n  var dist;                   /* match distance */\n  var from;                   /* where to copy match from */\n  var from_source;\n\n\n  var input, output; // JS specific, because we have no pointers\n\n  /* copy state to local variables */\n  state = strm.state;\n  //here = state.here;\n  _in = strm.next_in;\n  input = strm.input;\n  last = _in + (strm.avail_in - 5);\n  _out = strm.next_out;\n  output = strm.output;\n  beg = _out - (start - strm.avail_out);\n  end = _out + (strm.avail_out - 257);\n//#ifdef INFLATE_STRICT\n  dmax = state.dmax;\n//#endif\n  wsize = state.wsize;\n  whave = state.whave;\n  wnext = state.wnext;\n  s_window = state.window;\n  hold = state.hold;\n  bits = state.bits;\n  lcode = state.lencode;\n  dcode = state.distcode;\n  lmask = (1 << state.lenbits) - 1;\n  dmask = (1 << state.distbits) - 1;\n\n\n  /* decode literals and length/distances until end-of-block or not enough\n     input data or output space */\n\n  top:\n  do {\n    if (bits < 15) {\n      hold += input[_in++] << bits;\n      bits += 8;\n      hold += input[_in++] << bits;\n      bits += 8;\n    }\n\n    here = lcode[hold & lmask];\n\n    dolen:\n    for (;;) { // Goto emulation\n      op = here >>> 24/*here.bits*/;\n      hold >>>= op;\n      bits -= op;\n      op = (here >>> 16) & 0xff/*here.op*/;\n      if (op === 0) {                          /* literal */\n        //Tracevv((stderr, here.val >= 0x20 && here.val < 0x7f ?\n        //        \"inflate:         literal '%c'\\n\" :\n        //        \"inflate:         literal 0x%02x\\n\", here.val));\n        output[_out++] = here & 0xffff/*here.val*/;\n      }\n      else if (op & 16) {                     /* length base */\n        len = here & 0xffff/*here.val*/;\n        op &= 15;                           /* number of extra bits */\n        if (op) {\n          if (bits < op) {\n            hold += input[_in++] << bits;\n            bits += 8;\n          }\n          len += hold & ((1 << op) - 1);\n          hold >>>= op;\n          bits -= op;\n        }\n        //Tracevv((stderr, \"inflate:         length %u\\n\", len));\n        if (bits < 15) {\n          hold += input[_in++] << bits;\n          bits += 8;\n          hold += input[_in++] << bits;\n          bits += 8;\n        }\n        here = dcode[hold & dmask];\n\n        dodist:\n        for (;;) { // goto emulation\n          op = here >>> 24/*here.bits*/;\n          hold >>>= op;\n          bits -= op;\n          op = (here >>> 16) & 0xff/*here.op*/;\n\n          if (op & 16) {                      /* distance base */\n            dist = here & 0xffff/*here.val*/;\n            op &= 15;                       /* number of extra bits */\n            if (bits < op) {\n              hold += input[_in++] << bits;\n              bits += 8;\n              if (bits < op) {\n                hold += input[_in++] << bits;\n                bits += 8;\n              }\n            }\n            dist += hold & ((1 << op) - 1);\n//#ifdef INFLATE_STRICT\n            if (dist > dmax) {\n              strm.msg = 'invalid distance too far back';\n              state.mode = BAD;\n              break top;\n            }\n//#endif\n            hold >>>= op;\n            bits -= op;\n            //Tracevv((stderr, \"inflate:         distance %u\\n\", dist));\n            op = _out - beg;                /* max distance in output */\n            if (dist > op) {                /* see if copy from window */\n              op = dist - op;               /* distance back in window */\n              if (op > whave) {\n                if (state.sane) {\n                  strm.msg = 'invalid distance too far back';\n                  state.mode = BAD;\n                  break top;\n                }\n\n// (!) This block is disabled in zlib defaults,\n// don't enable it for binary compatibility\n//#ifdef INFLATE_ALLOW_INVALID_DISTANCE_TOOFAR_ARRR\n//                if (len <= op - whave) {\n//                  do {\n//                    output[_out++] = 0;\n//                  } while (--len);\n//                  continue top;\n//                }\n//                len -= op - whave;\n//                do {\n//                  output[_out++] = 0;\n//                } while (--op > whave);\n//                if (op === 0) {\n//                  from = _out - dist;\n//                  do {\n//                    output[_out++] = output[from++];\n//                  } while (--len);\n//                  continue top;\n//                }\n//#endif\n              }\n              from = 0; // window index\n              from_source = s_window;\n              if (wnext === 0) {           /* very common case */\n                from += wsize - op;\n                if (op < len) {         /* some from window */\n                  len -= op;\n                  do {\n                    output[_out++] = s_window[from++];\n                  } while (--op);\n                  from = _out - dist;  /* rest from output */\n                  from_source = output;\n                }\n              }\n              else if (wnext < op) {      /* wrap around window */\n                from += wsize + wnext - op;\n                op -= wnext;\n                if (op < len) {         /* some from end of window */\n                  len -= op;\n                  do {\n                    output[_out++] = s_window[from++];\n                  } while (--op);\n                  from = 0;\n                  if (wnext < len) {  /* some from start of window */\n                    op = wnext;\n                    len -= op;\n                    do {\n                      output[_out++] = s_window[from++];\n                    } while (--op);\n                    from = _out - dist;      /* rest from output */\n                    from_source = output;\n                  }\n                }\n              }\n              else {                      /* contiguous in window */\n                from += wnext - op;\n                if (op < len) {         /* some from window */\n                  len -= op;\n                  do {\n                    output[_out++] = s_window[from++];\n                  } while (--op);\n                  from = _out - dist;  /* rest from output */\n                  from_source = output;\n                }\n              }\n              while (len > 2) {\n                output[_out++] = from_source[from++];\n                output[_out++] = from_source[from++];\n                output[_out++] = from_source[from++];\n                len -= 3;\n              }\n              if (len) {\n                output[_out++] = from_source[from++];\n                if (len > 1) {\n                  output[_out++] = from_source[from++];\n                }\n              }\n            }\n            else {\n              from = _out - dist;          /* copy direct from output */\n              do {                        /* minimum length is three */\n                output[_out++] = output[from++];\n                output[_out++] = output[from++];\n                output[_out++] = output[from++];\n                len -= 3;\n              } while (len > 2);\n              if (len) {\n                output[_out++] = output[from++];\n                if (len > 1) {\n                  output[_out++] = output[from++];\n                }\n              }\n            }\n          }\n          else if ((op & 64) === 0) {          /* 2nd level distance code */\n            here = dcode[(here & 0xffff)/*here.val*/ + (hold & ((1 << op) - 1))];\n            continue dodist;\n          }\n          else {\n            strm.msg = 'invalid distance code';\n            state.mode = BAD;\n            break top;\n          }\n\n          break; // need to emulate goto via \"continue\"\n        }\n      }\n      else if ((op & 64) === 0) {              /* 2nd level length code */\n        here = lcode[(here & 0xffff)/*here.val*/ + (hold & ((1 << op) - 1))];\n        continue dolen;\n      }\n      else if (op & 32) {                     /* end-of-block */\n        //Tracevv((stderr, \"inflate:         end of block\\n\"));\n        state.mode = TYPE;\n        break top;\n      }\n      else {\n        strm.msg = 'invalid literal/length code';\n        state.mode = BAD;\n        break top;\n      }\n\n      break; // need to emulate goto via \"continue\"\n    }\n  } while (_in < last && _out < end);\n\n  /* return unused bytes (on entry, bits < 8, so in won't go too far back) */\n  len = bits >> 3;\n  _in -= len;\n  bits -= len << 3;\n  hold &= (1 << bits) - 1;\n\n  /* update state and return */\n  strm.next_in = _in;\n  strm.next_out = _out;\n  strm.avail_in = (_in < last ? 5 + (last - _in) : 5 - (_in - last));\n  strm.avail_out = (_out < end ? 257 + (end - _out) : 257 - (_out - end));\n  state.hold = hold;\n  state.bits = bits;\n  return;\n};\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,iCAAiC;AACjC,IAAI,MAAM,IAAU,+CAA+C;AACnE,IAAI,OAAO,IAAS,qDAAqD;AAEzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC,GACD,OAAO,OAAO,GAAG,SAAS,aAAa,IAAI,EAAE,KAAK;IAChD,IAAI;IACJ,IAAI,KAAwB,oBAAoB;IAChD,IAAI,MAAwB,qCAAqC;IACjE,IAAI,MAAwB,qBAAqB;IACjD,IAAI,KAAwB,mCAAmC;IAC/D,IAAI,KAAwB,2CAA2C;IACzE,uBAAuB;IACrB,IAAI,MAAwB,qCAAqC;IACnE,QAAQ;IACN,IAAI,OAAwB,2CAA2C;IACvE,IAAI,OAAwB,6BAA6B;IACzD,IAAI,OAAwB,sBAAsB;IAClD,6EAA6E;IAC7E,IAAI,UAAwB,2CAA2C;IACvE,IAAI,MAAwB,mBAAmB;IAC/C,IAAI,MAAwB,mBAAmB;IAC/C,IAAI,OAAwB,sBAAsB;IAClD,IAAI,OAAwB,uBAAuB;IACnD,IAAI,OAAwB,wCAAwC;IACpE,IAAI,OAAwB,0CAA0C;IACtE,IAAI,MAAwB,yBAAyB;IACrD,IAAI,IAAwB,wCAAwC;IACxC,0CAA0C,GACtE,IAAI,KAAwB,8BAA8B;IAC1D,IAAI,MAAwB,kBAAkB;IAC9C,IAAI,MAAwB,4BAA4B;IACxD,IAAI;IAGJ,IAAI,OAAO,QAAQ,2CAA2C;IAE9D,iCAAiC,GACjC,QAAQ,KAAK,KAAK;IAClB,oBAAoB;IACpB,MAAM,KAAK,OAAO;IAClB,QAAQ,KAAK,KAAK;IAClB,OAAO,MAAM,CAAC,KAAK,QAAQ,GAAG,CAAC;IAC/B,OAAO,KAAK,QAAQ;IACpB,SAAS,KAAK,MAAM;IACpB,MAAM,OAAO,CAAC,QAAQ,KAAK,SAAS;IACpC,MAAM,OAAO,CAAC,KAAK,SAAS,GAAG,GAAG;IACpC,uBAAuB;IACrB,OAAO,MAAM,IAAI;IACnB,QAAQ;IACN,QAAQ,MAAM,KAAK;IACnB,QAAQ,MAAM,KAAK;IACnB,QAAQ,MAAM,KAAK;IACnB,WAAW,MAAM,MAAM;IACvB,OAAO,MAAM,IAAI;IACjB,OAAO,MAAM,IAAI;IACjB,QAAQ,MAAM,OAAO;IACrB,QAAQ,MAAM,QAAQ;IACtB,QAAQ,CAAC,KAAK,MAAM,OAAO,IAAI;IAC/B,QAAQ,CAAC,KAAK,MAAM,QAAQ,IAAI;IAGhC;gCAC8B,GAE9B,KACA,GAAG;QACD,IAAI,OAAO,IAAI;YACb,QAAQ,KAAK,CAAC,MAAM,IAAI;YACxB,QAAQ;YACR,QAAQ,KAAK,CAAC,MAAM,IAAI;YACxB,QAAQ;QACV;QAEA,OAAO,KAAK,CAAC,OAAO,MAAM;QAE1B,OACA,OAAS;YACP,KAAK,SAAS,GAAE,WAAW;YAC3B,UAAU;YACV,QAAQ;YACR,KAAK,AAAC,SAAS,KAAM,KAAI,SAAS;YAClC,IAAI,OAAO,GAAG;gBACZ,wDAAwD;gBACxD,6CAA6C;gBAC7C,0DAA0D;gBAC1D,MAAM,CAAC,OAAO,GAAG,OAAO,OAAM,UAAU;YAC1C,OACK,IAAI,KAAK,IAAI;gBAChB,MAAM,OAAO,OAAM,UAAU;gBAC7B,MAAM,IAA8B,wBAAwB;gBAC5D,IAAI,IAAI;oBACN,IAAI,OAAO,IAAI;wBACb,QAAQ,KAAK,CAAC,MAAM,IAAI;wBACxB,QAAQ;oBACV;oBACA,OAAO,OAAQ,CAAC,KAAK,EAAE,IAAI;oBAC3B,UAAU;oBACV,QAAQ;gBACV;gBACA,yDAAyD;gBACzD,IAAI,OAAO,IAAI;oBACb,QAAQ,KAAK,CAAC,MAAM,IAAI;oBACxB,QAAQ;oBACR,QAAQ,KAAK,CAAC,MAAM,IAAI;oBACxB,QAAQ;gBACV;gBACA,OAAO,KAAK,CAAC,OAAO,MAAM;gBAE1B,QACA,OAAS;oBACP,KAAK,SAAS,GAAE,WAAW;oBAC3B,UAAU;oBACV,QAAQ;oBACR,KAAK,AAAC,SAAS,KAAM,KAAI,SAAS;oBAElC,IAAI,KAAK,IAAI;wBACX,OAAO,OAAO,OAAM,UAAU;wBAC9B,MAAM,IAA0B,wBAAwB;wBACxD,IAAI,OAAO,IAAI;4BACb,QAAQ,KAAK,CAAC,MAAM,IAAI;4BACxB,QAAQ;4BACR,IAAI,OAAO,IAAI;gCACb,QAAQ,KAAK,CAAC,MAAM,IAAI;gCACxB,QAAQ;4BACV;wBACF;wBACA,QAAQ,OAAQ,CAAC,KAAK,EAAE,IAAI;wBACxC,uBAAuB;wBACX,IAAI,OAAO,MAAM;4BACf,KAAK,GAAG,GAAG;4BACX,MAAM,IAAI,GAAG;4BACb,MAAM;wBACR;wBACZ,QAAQ;wBACI,UAAU;wBACV,QAAQ;wBACR,4DAA4D;wBAC5D,KAAK,OAAO,KAAoB,0BAA0B;wBAC1D,IAAI,OAAO,IAAI;4BACb,KAAK,OAAO,IAAkB,2BAA2B;4BACzD,IAAI,KAAK,OAAO;gCACd,IAAI,MAAM,IAAI,EAAE;oCACd,KAAK,GAAG,GAAG;oCACX,MAAM,IAAI,GAAG;oCACb,MAAM;gCACR;4BAEhB,+CAA+C;4BAC/C,2CAA2C;4BAC3C,mDAAmD;4BACnD,0CAA0C;4BAC1C,wBAAwB;4BACxB,yCAAyC;4BACzC,oCAAoC;4BACpC,iCAAiC;4BACjC,mBAAmB;4BACnB,oCAAoC;4BACpC,sBAAsB;4BACtB,uCAAuC;4BACvC,yCAAyC;4BACzC,iCAAiC;4BACjC,uCAAuC;4BACvC,wBAAwB;4BACxB,sDAAsD;4BACtD,oCAAoC;4BACpC,iCAAiC;4BACjC,mBAAmB;4BACnB,QAAQ;4BACM;4BACA,OAAO,GAAG,eAAe;4BACzB,cAAc;4BACd,IAAI,UAAU,GAAG;gCACf,QAAQ,QAAQ;gCAChB,IAAI,KAAK,KAAK;oCACZ,OAAO;oCACP,GAAG;wCACD,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO;oCACnC,QAAS,EAAE,GAAI;oCACf,OAAO,OAAO,MAAO,oBAAoB;oCACzC,cAAc;gCAChB;4BACF,OACK,IAAI,QAAQ,IAAI;gCACnB,QAAQ,QAAQ,QAAQ;gCACxB,MAAM;gCACN,IAAI,KAAK,KAAK;oCACZ,OAAO;oCACP,GAAG;wCACD,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO;oCACnC,QAAS,EAAE,GAAI;oCACf,OAAO;oCACP,IAAI,QAAQ,KAAK;wCACf,KAAK;wCACL,OAAO;wCACP,GAAG;4CACD,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO;wCACnC,QAAS,EAAE,GAAI;wCACf,OAAO,OAAO,MAAW,oBAAoB;wCAC7C,cAAc;oCAChB;gCACF;4BACF,OACK;gCACH,QAAQ,QAAQ;gCAChB,IAAI,KAAK,KAAK;oCACZ,OAAO;oCACP,GAAG;wCACD,MAAM,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO;oCACnC,QAAS,EAAE,GAAI;oCACf,OAAO,OAAO,MAAO,oBAAoB;oCACzC,cAAc;gCAChB;4BACF;4BACA,MAAO,MAAM,EAAG;gCACd,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gCACpC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gCACpC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gCACpC,OAAO;4BACT;4BACA,IAAI,KAAK;gCACP,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gCACpC,IAAI,MAAM,GAAG;oCACX,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO;gCACtC;4BACF;wBACF,OACK;4BACH,OAAO,OAAO,MAAe,2BAA2B;4BACxD,GAAG;gCACD,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;gCAC/B,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;gCAC/B,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;gCAC/B,OAAO;4BACT,QAAS,MAAM,EAAG;4BAClB,IAAI,KAAK;gCACP,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;gCAC/B,IAAI,MAAM,GAAG;oCACX,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;gCACjC;4BACF;wBACF;oBACF,OACK,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG;wBACxB,OAAO,KAAK,CAAC,CAAC,OAAO,MAAM,IAAgB,CAAC,OAAQ,CAAC,KAAK,EAAE,IAAI,CAAE,EAAE;wBACpE,SAAS;oBACX,OACK;wBACH,KAAK,GAAG,GAAG;wBACX,MAAM,IAAI,GAAG;wBACb,MAAM;oBACR;oBAEA,OAAO,sCAAsC;gBAC/C;YACF,OACK,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG;gBACxB,OAAO,KAAK,CAAC,CAAC,OAAO,MAAM,IAAgB,CAAC,OAAQ,CAAC,KAAK,EAAE,IAAI,CAAE,EAAE;gBACpE,SAAS;YACX,OACK,IAAI,KAAK,IAAI;gBAChB,uDAAuD;gBACvD,MAAM,IAAI,GAAG;gBACb,MAAM;YACR,OACK;gBACH,KAAK,GAAG,GAAG;gBACX,MAAM,IAAI,GAAG;gBACb,MAAM;YACR;YAEA,OAAO,sCAAsC;QAC/C;IACF,QAAS,MAAM,QAAQ,OAAO,IAAK;IAEnC,yEAAyE,GACzE,MAAM,QAAQ;IACd,OAAO;IACP,QAAQ,OAAO;IACf,QAAQ,CAAC,KAAK,IAAI,IAAI;IAEtB,2BAA2B,GAC3B,KAAK,OAAO,GAAG;IACf,KAAK,QAAQ,GAAG;IAChB,KAAK,QAAQ,GAAI,MAAM,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI;IAChE,KAAK,SAAS,GAAI,OAAO,MAAM,MAAM,CAAC,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,GAAG;IACrE,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/inftrees.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nvar utils = require('../utils/common');\n\nvar MAXBITS = 15;\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH = (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\n\nvar lbase = [ /* Length codes 257..285 base */\n  3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31,\n  35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0\n];\n\nvar lext = [ /* Length codes 257..285 extra */\n  16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18,\n  19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78\n];\n\nvar dbase = [ /* Distance codes 0..29 base */\n  1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193,\n  257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145,\n  8193, 12289, 16385, 24577, 0, 0\n];\n\nvar dext = [ /* Distance codes 0..29 extra */\n  16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22,\n  23, 23, 24, 24, 25, 25, 26, 26, 27, 27,\n  28, 28, 29, 29, 64, 64\n];\n\nmodule.exports = function inflate_table(type, lens, lens_index, codes, table, table_index, work, opts)\n{\n  var bits = opts.bits;\n      //here = opts.here; /* table entry for duplication */\n\n  var len = 0;               /* a code's length in bits */\n  var sym = 0;               /* index of code symbols */\n  var min = 0, max = 0;          /* minimum and maximum code lengths */\n  var root = 0;              /* number of index bits for root table */\n  var curr = 0;              /* number of index bits for current table */\n  var drop = 0;              /* code bits to drop for sub-table */\n  var left = 0;                   /* number of prefix codes available */\n  var used = 0;              /* code entries in table used */\n  var huff = 0;              /* Huffman code */\n  var incr;              /* for incrementing code, index */\n  var fill;              /* index for replicating entries */\n  var low;               /* low bits for current root entry */\n  var mask;              /* mask for low root bits */\n  var next;             /* next available space in table */\n  var base = null;     /* base value table to use */\n  var base_index = 0;\n//  var shoextra;    /* extra bits table to use */\n  var end;                    /* use base and extra for symbol > end */\n  var count = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];    /* number of codes of each length */\n  var offs = new utils.Buf16(MAXBITS + 1); //[MAXBITS+1];     /* offsets in table for each length */\n  var extra = null;\n  var extra_index = 0;\n\n  var here_bits, here_op, here_val;\n\n  /*\n   Process a set of code lengths to create a canonical Huffman code.  The\n   code lengths are lens[0..codes-1].  Each length corresponds to the\n   symbols 0..codes-1.  The Huffman code is generated by first sorting the\n   symbols by length from short to long, and retaining the symbol order\n   for codes with equal lengths.  Then the code starts with all zero bits\n   for the first code of the shortest length, and the codes are integer\n   increments for the same length, and zeros are appended as the length\n   increases.  For the deflate format, these bits are stored backwards\n   from their more natural integer increment ordering, and so when the\n   decoding tables are built in the large loop below, the integer codes\n   are incremented backwards.\n\n   This routine assumes, but does not check, that all of the entries in\n   lens[] are in the range 0..MAXBITS.  The caller must assure this.\n   1..MAXBITS is interpreted as that code length.  zero means that that\n   symbol does not occur in this code.\n\n   The codes are sorted by computing a count of codes for each length,\n   creating from that a table of starting indices for each length in the\n   sorted table, and then entering the symbols in order in the sorted\n   table.  The sorted table is work[], with that space being provided by\n   the caller.\n\n   The length counts are used for other purposes as well, i.e. finding\n   the minimum and maximum length codes, determining if there are any\n   codes at all, checking for a valid set of lengths, and looking ahead\n   at length counts to determine sub-table sizes when building the\n   decoding tables.\n   */\n\n  /* accumulate lengths for codes (assumes lens[] all in 0..MAXBITS) */\n  for (len = 0; len <= MAXBITS; len++) {\n    count[len] = 0;\n  }\n  for (sym = 0; sym < codes; sym++) {\n    count[lens[lens_index + sym]]++;\n  }\n\n  /* bound code lengths, force root to be within code lengths */\n  root = bits;\n  for (max = MAXBITS; max >= 1; max--) {\n    if (count[max] !== 0) { break; }\n  }\n  if (root > max) {\n    root = max;\n  }\n  if (max === 0) {                     /* no symbols to code at all */\n    //table.op[opts.table_index] = 64;  //here.op = (var char)64;    /* invalid code marker */\n    //table.bits[opts.table_index] = 1;   //here.bits = (var char)1;\n    //table.val[opts.table_index++] = 0;   //here.val = (var short)0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n\n    //table.op[opts.table_index] = 64;\n    //table.bits[opts.table_index] = 1;\n    //table.val[opts.table_index++] = 0;\n    table[table_index++] = (1 << 24) | (64 << 16) | 0;\n\n    opts.bits = 1;\n    return 0;     /* no symbols, but wait for decoding to report error */\n  }\n  for (min = 1; min < max; min++) {\n    if (count[min] !== 0) { break; }\n  }\n  if (root < min) {\n    root = min;\n  }\n\n  /* check for an over-subscribed or incomplete set of lengths */\n  left = 1;\n  for (len = 1; len <= MAXBITS; len++) {\n    left <<= 1;\n    left -= count[len];\n    if (left < 0) {\n      return -1;\n    }        /* over-subscribed */\n  }\n  if (left > 0 && (type === CODES || max !== 1)) {\n    return -1;                      /* incomplete set */\n  }\n\n  /* generate offsets into symbol table for each length for sorting */\n  offs[1] = 0;\n  for (len = 1; len < MAXBITS; len++) {\n    offs[len + 1] = offs[len] + count[len];\n  }\n\n  /* sort symbols by length, by symbol order within each length */\n  for (sym = 0; sym < codes; sym++) {\n    if (lens[lens_index + sym] !== 0) {\n      work[offs[lens[lens_index + sym]]++] = sym;\n    }\n  }\n\n  /*\n   Create and fill in decoding tables.  In this loop, the table being\n   filled is at next and has curr index bits.  The code being used is huff\n   with length len.  That code is converted to an index by dropping drop\n   bits off of the bottom.  For codes where len is less than drop + curr,\n   those top drop + curr - len bits are incremented through all values to\n   fill the table with replicated entries.\n\n   root is the number of index bits for the root table.  When len exceeds\n   root, sub-tables are created pointed to by the root entry with an index\n   of the low root bits of huff.  This is saved in low to check for when a\n   new sub-table should be started.  drop is zero when the root table is\n   being filled, and drop is root when sub-tables are being filled.\n\n   When a new sub-table is needed, it is necessary to look ahead in the\n   code lengths to determine what size sub-table is needed.  The length\n   counts are used for this, and so count[] is decremented as codes are\n   entered in the tables.\n\n   used keeps track of how many table entries have been allocated from the\n   provided *table space.  It is checked for LENS and DIST tables against\n   the constants ENOUGH_LENS and ENOUGH_DISTS to guard against changes in\n   the initial root table size constants.  See the comments in inftrees.h\n   for more information.\n\n   sym increments through all symbols, and the loop terminates when\n   all codes of length max, i.e. all codes, have been processed.  This\n   routine permits incomplete codes, so another loop after this one fills\n   in the rest of the decoding tables with invalid code markers.\n   */\n\n  /* set up for code type */\n  // poor man optimization - use if-else instead of switch,\n  // to avoid deopts in old v8\n  if (type === CODES) {\n    base = extra = work;    /* dummy value--not used */\n    end = 19;\n\n  } else if (type === LENS) {\n    base = lbase;\n    base_index -= 257;\n    extra = lext;\n    extra_index -= 257;\n    end = 256;\n\n  } else {                    /* DISTS */\n    base = dbase;\n    extra = dext;\n    end = -1;\n  }\n\n  /* initialize opts for loop */\n  huff = 0;                   /* starting code */\n  sym = 0;                    /* starting code symbol */\n  len = min;                  /* starting code length */\n  next = table_index;              /* current table to fill in */\n  curr = root;                /* current table index bits */\n  drop = 0;                   /* current bits to drop from code for index */\n  low = -1;                   /* trigger new sub-table when len > root */\n  used = 1 << root;          /* use root table entries */\n  mask = used - 1;            /* mask for comparing low */\n\n  /* check available table space */\n  if ((type === LENS && used > ENOUGH_LENS) ||\n    (type === DISTS && used > ENOUGH_DISTS)) {\n    return 1;\n  }\n\n  /* process all codes and make table entries */\n  for (;;) {\n    /* create table entry */\n    here_bits = len - drop;\n    if (work[sym] < end) {\n      here_op = 0;\n      here_val = work[sym];\n    }\n    else if (work[sym] > end) {\n      here_op = extra[extra_index + work[sym]];\n      here_val = base[base_index + work[sym]];\n    }\n    else {\n      here_op = 32 + 64;         /* end of block */\n      here_val = 0;\n    }\n\n    /* replicate for those indices with low len bits equal to huff */\n    incr = 1 << (len - drop);\n    fill = 1 << curr;\n    min = fill;                 /* save offset to next table */\n    do {\n      fill -= incr;\n      table[next + (huff >> drop) + fill] = (here_bits << 24) | (here_op << 16) | here_val |0;\n    } while (fill !== 0);\n\n    /* backwards increment the len-bit code huff */\n    incr = 1 << (len - 1);\n    while (huff & incr) {\n      incr >>= 1;\n    }\n    if (incr !== 0) {\n      huff &= incr - 1;\n      huff += incr;\n    } else {\n      huff = 0;\n    }\n\n    /* go to next symbol, update count, len */\n    sym++;\n    if (--count[len] === 0) {\n      if (len === max) { break; }\n      len = lens[lens_index + work[sym]];\n    }\n\n    /* create new sub-table if needed */\n    if (len > root && (huff & mask) !== low) {\n      /* if first time, transition to sub-tables */\n      if (drop === 0) {\n        drop = root;\n      }\n\n      /* increment past last table */\n      next += min;            /* here min is 1 << curr */\n\n      /* determine length of next table */\n      curr = len - drop;\n      left = 1 << curr;\n      while (curr + drop < max) {\n        left -= count[curr + drop];\n        if (left <= 0) { break; }\n        curr++;\n        left <<= 1;\n      }\n\n      /* check for enough space */\n      used += 1 << curr;\n      if ((type === LENS && used > ENOUGH_LENS) ||\n        (type === DISTS && used > ENOUGH_DISTS)) {\n        return 1;\n      }\n\n      /* point entry in root table to sub-table */\n      low = huff & mask;\n      /*table.op[low] = curr;\n      table.bits[low] = root;\n      table.val[low] = next - opts.table_index;*/\n      table[low] = (root << 24) | (curr << 16) | (next - table_index) |0;\n    }\n  }\n\n  /* fill in remaining table entry if code is incomplete (guaranteed to have\n   at most one remaining entry, since if the code is incomplete, the\n   maximum code length that was allowed to get this far is one bit) */\n  if (huff !== 0) {\n    //table.op[next + huff] = 64;            /* invalid code marker */\n    //table.bits[next + huff] = len - drop;\n    //table.val[next + huff] = 0;\n    table[next + huff] = ((len - drop) << 24) | (64 << 16) |0;\n  }\n\n  /* set return parameters */\n  //opts.table_index += used;\n  opts.bits = root;\n  return 0;\n};\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,IAAI;AAEJ,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,0CAA0C;AAE1C,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AAEZ,IAAI,QAAQ;IAAE,8BAA8B,GAC1C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IACrD;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAG;CAC9D;AAED,IAAI,OAAO;IAAE,+BAA+B,GAC1C;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAC5D;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;CACzD;AAED,IAAI,QAAQ;IAAE,6BAA6B,GACzC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAK;IACtD;IAAK;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAClD;IAAM;IAAO;IAAO;IAAO;IAAG;CAC/B;AAED,IAAI,OAAO;IAAE,8BAA8B,GACzC;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAC5D;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IACpC;IAAI;IAAI;IAAI;IAAI;IAAI;CACrB;AAED,OAAO,OAAO,GAAG,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI;IAEnG,IAAI,OAAO,KAAK,IAAI;IAChB,qDAAqD;IAEzD,IAAI,MAAM,GAAiB,2BAA2B;IACtD,IAAI,MAAM,GAAiB,yBAAyB;IACpD,IAAI,MAAM,GAAG,MAAM,GAAY,oCAAoC;IACnE,IAAI,OAAO,GAAgB,uCAAuC;IAClE,IAAI,OAAO,GAAgB,0CAA0C;IACrE,IAAI,OAAO,GAAgB,mCAAmC;IAC9D,IAAI,OAAO,GAAqB,oCAAoC;IACpE,IAAI,OAAO,GAAgB,8BAA8B;IACzD,IAAI,OAAO,GAAgB,gBAAgB;IAC3C,IAAI,MAAmB,gCAAgC;IACvD,IAAI,MAAmB,iCAAiC;IACxD,IAAI,KAAmB,mCAAmC;IAC1D,IAAI,MAAmB,0BAA0B;IACjD,IAAI,MAAkB,iCAAiC;IACvD,IAAI,OAAO,MAAU,2BAA2B;IAChD,IAAI,aAAa;IACnB,kDAAkD;IAChD,IAAI,KAAwB,uCAAuC;IACnE,IAAI,QAAQ,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,sDAAsD;IAChG,IAAI,OAAO,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,yDAAyD;IAClG,IAAI,QAAQ;IACZ,IAAI,cAAc;IAElB,IAAI,WAAW,SAAS;IAExB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BC,GAED,mEAAmE,GACnE,IAAK,MAAM,GAAG,OAAO,SAAS,MAAO;QACnC,KAAK,CAAC,IAAI,GAAG;IACf;IACA,IAAK,MAAM,GAAG,MAAM,OAAO,MAAO;QAChC,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC;IAC/B;IAEA,4DAA4D,GAC5D,OAAO;IACP,IAAK,MAAM,SAAS,OAAO,GAAG,MAAO;QACnC,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG;YAAE;QAAO;IACjC;IACA,IAAI,OAAO,KAAK;QACd,OAAO;IACT;IACA,IAAI,QAAQ,GAAG;QACb,0FAA0F;QAC1F,gEAAgE;QAChE,iEAAiE;QACjE,KAAK,CAAC,cAAc,GAAG,AAAC,KAAK,KAAO,MAAM,KAAM;QAGhD,kCAAkC;QAClC,mCAAmC;QACnC,oCAAoC;QACpC,KAAK,CAAC,cAAc,GAAG,AAAC,KAAK,KAAO,MAAM,KAAM;QAEhD,KAAK,IAAI,GAAG;QACZ,OAAO,GAAO,qDAAqD;IACrE;IACA,IAAK,MAAM,GAAG,MAAM,KAAK,MAAO;QAC9B,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG;YAAE;QAAO;IACjC;IACA,IAAI,OAAO,KAAK;QACd,OAAO;IACT;IAEA,6DAA6D,GAC7D,OAAO;IACP,IAAK,MAAM,GAAG,OAAO,SAAS,MAAO;QACnC,SAAS;QACT,QAAQ,KAAK,CAAC,IAAI;QAClB,IAAI,OAAO,GAAG;YACZ,OAAO,CAAC;QACV,EAAS,mBAAmB;IAC9B;IACA,IAAI,OAAO,KAAK,CAAC,SAAS,SAAS,QAAQ,CAAC,GAAG;QAC7C,OAAO,CAAC,GAAwB,kBAAkB;IACpD;IAEA,kEAAkE,GAClE,IAAI,CAAC,EAAE,GAAG;IACV,IAAK,MAAM,GAAG,MAAM,SAAS,MAAO;QAClC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IACxC;IAEA,8DAA8D,GAC9D,IAAK,MAAM,GAAG,MAAM,OAAO,MAAO;QAChC,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,GAAG;YAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,GAAG,GAAG;QACzC;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BC,GAED,wBAAwB,GACxB,yDAAyD;IACzD,4BAA4B;IAC5B,IAAI,SAAS,OAAO;QAClB,OAAO,QAAQ,MAAS,yBAAyB;QACjD,MAAM;IAER,OAAO,IAAI,SAAS,MAAM;QACxB,OAAO;QACP,cAAc;QACd,QAAQ;QACR,eAAe;QACf,MAAM;IAER,OAAO;QACL,OAAO;QACP,QAAQ;QACR,MAAM,CAAC;IACT;IAEA,4BAA4B,GAC5B,OAAO,GAAqB,iBAAiB;IAC7C,MAAM,GAAsB,wBAAwB;IACpD,MAAM,KAAsB,wBAAwB;IACpD,OAAO,aAA0B,4BAA4B;IAC7D,OAAO,MAAqB,4BAA4B;IACxD,OAAO,GAAqB,4CAA4C;IACxE,MAAM,CAAC,GAAqB,yCAAyC;IACrE,OAAO,KAAK,MAAe,0BAA0B;IACrD,OAAO,OAAO,GAAc,0BAA0B;IAEtD,+BAA+B,GAC/B,IAAI,AAAC,SAAS,QAAQ,OAAO,eAC1B,SAAS,SAAS,OAAO,cAAe;QACzC,OAAO;IACT;IAEA,4CAA4C,GAC5C,OAAS;QACP,sBAAsB,GACtB,YAAY,MAAM;QAClB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK;YACnB,UAAU;YACV,WAAW,IAAI,CAAC,IAAI;QACtB,OACK,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK;YACxB,UAAU,KAAK,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC;YACxC,WAAW,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC;QACzC,OACK;YACH,UAAU,KAAK,IAAY,gBAAgB;YAC3C,WAAW;QACb;QAEA,+DAA+D,GAC/D,OAAO,KAAM,MAAM;QACnB,OAAO,KAAK;QACZ,MAAM,MAAsB,6BAA6B;QACzD,GAAG;YACD,QAAQ;YACR,KAAK,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,KAAK,GAAG,AAAC,aAAa,KAAO,WAAW,KAAM,WAAU;QACxF,QAAS,SAAS,EAAG;QAErB,6CAA6C,GAC7C,OAAO,KAAM,MAAM;QACnB,MAAO,OAAO,KAAM;YAClB,SAAS;QACX;QACA,IAAI,SAAS,GAAG;YACd,QAAQ,OAAO;YACf,QAAQ;QACV,OAAO;YACL,OAAO;QACT;QAEA,wCAAwC,GACxC;QACA,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,GAAG;YACtB,IAAI,QAAQ,KAAK;gBAAE;YAAO;YAC1B,MAAM,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC;QACpC;QAEA,kCAAkC,GAClC,IAAI,MAAM,QAAQ,CAAC,OAAO,IAAI,MAAM,KAAK;YACvC,2CAA2C,GAC3C,IAAI,SAAS,GAAG;gBACd,OAAO;YACT;YAEA,6BAA6B,GAC7B,QAAQ,KAAgB,yBAAyB;YAEjD,kCAAkC,GAClC,OAAO,MAAM;YACb,OAAO,KAAK;YACZ,MAAO,OAAO,OAAO,IAAK;gBACxB,QAAQ,KAAK,CAAC,OAAO,KAAK;gBAC1B,IAAI,QAAQ,GAAG;oBAAE;gBAAO;gBACxB;gBACA,SAAS;YACX;YAEA,0BAA0B,GAC1B,QAAQ,KAAK;YACb,IAAI,AAAC,SAAS,QAAQ,OAAO,eAC1B,SAAS,SAAS,OAAO,cAAe;gBACzC,OAAO;YACT;YAEA,0CAA0C,GAC1C,MAAM,OAAO;YACb;;+CAEyC,GACzC,KAAK,CAAC,IAAI,GAAG,AAAC,QAAQ,KAAO,QAAQ,KAAO,OAAO,cAAc;QACnE;IACF;IAEA;;oEAEkE,GAClE,IAAI,SAAS,GAAG;QACd,kEAAkE;QAClE,uCAAuC;QACvC,6BAA6B;QAC7B,KAAK,CAAC,OAAO,KAAK,GAAG,AAAE,MAAM,QAAS,KAAO,MAAM,KAAK;IAC1D;IAEA,yBAAyB,GACzB,2BAA2B;IAC3B,KAAK,IAAI,GAAG;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/inflate.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nvar utils         = require('../utils/common');\nvar adler32       = require('./adler32');\nvar crc32         = require('./crc32');\nvar inflate_fast  = require('./inffast');\nvar inflate_table = require('./inftrees');\n\nvar CODES = 0;\nvar LENS = 1;\nvar DISTS = 2;\n\n/* Public constants ==========================================================*/\n/* ===========================================================================*/\n\n\n/* Allowed flush values; see deflate() and inflate() below for details */\n//var Z_NO_FLUSH      = 0;\n//var Z_PARTIAL_FLUSH = 1;\n//var Z_SYNC_FLUSH    = 2;\n//var Z_FULL_FLUSH    = 3;\nvar Z_FINISH        = 4;\nvar Z_BLOCK         = 5;\nvar Z_TREES         = 6;\n\n\n/* Return codes for the compression/decompression functions. Negative values\n * are errors, positive values are used for special but normal events.\n */\nvar Z_OK            = 0;\nvar Z_STREAM_END    = 1;\nvar Z_NEED_DICT     = 2;\n//var Z_ERRNO         = -1;\nvar Z_STREAM_ERROR  = -2;\nvar Z_DATA_ERROR    = -3;\nvar Z_MEM_ERROR     = -4;\nvar Z_BUF_ERROR     = -5;\n//var Z_VERSION_ERROR = -6;\n\n/* The deflate compression method */\nvar Z_DEFLATED  = 8;\n\n\n/* STATES ====================================================================*/\n/* ===========================================================================*/\n\n\nvar    HEAD = 1;       /* i: waiting for magic header */\nvar    FLAGS = 2;      /* i: waiting for method and flags (gzip) */\nvar    TIME = 3;       /* i: waiting for modification time (gzip) */\nvar    OS = 4;         /* i: waiting for extra flags and operating system (gzip) */\nvar    EXLEN = 5;      /* i: waiting for extra length (gzip) */\nvar    EXTRA = 6;      /* i: waiting for extra bytes (gzip) */\nvar    NAME = 7;       /* i: waiting for end of file name (gzip) */\nvar    COMMENT = 8;    /* i: waiting for end of comment (gzip) */\nvar    HCRC = 9;       /* i: waiting for header crc (gzip) */\nvar    DICTID = 10;    /* i: waiting for dictionary check value */\nvar    DICT = 11;      /* waiting for inflateSetDictionary() call */\nvar        TYPE = 12;      /* i: waiting for type bits, including last-flag bit */\nvar        TYPEDO = 13;    /* i: same, but skip check to exit inflate on new block */\nvar        STORED = 14;    /* i: waiting for stored size (length and complement) */\nvar        COPY_ = 15;     /* i/o: same as COPY below, but only first time in */\nvar        COPY = 16;      /* i/o: waiting for input or output to copy stored block */\nvar        TABLE = 17;     /* i: waiting for dynamic block table lengths */\nvar        LENLENS = 18;   /* i: waiting for code length code lengths */\nvar        CODELENS = 19;  /* i: waiting for length/lit and distance code lengths */\nvar            LEN_ = 20;      /* i: same as LEN below, but only first time in */\nvar            LEN = 21;       /* i: waiting for length/lit/eob code */\nvar            LENEXT = 22;    /* i: waiting for length extra bits */\nvar            DIST = 23;      /* i: waiting for distance code */\nvar            DISTEXT = 24;   /* i: waiting for distance extra bits */\nvar            MATCH = 25;     /* o: waiting for output space to copy string */\nvar            LIT = 26;       /* o: waiting for output space to write literal */\nvar    CHECK = 27;     /* i: waiting for 32-bit check value */\nvar    LENGTH = 28;    /* i: waiting for 32-bit length (gzip) */\nvar    DONE = 29;      /* finished check, done -- remain here until reset */\nvar    BAD = 30;       /* got a data error -- remain here until reset */\nvar    MEM = 31;       /* got an inflate() memory error -- remain here until reset */\nvar    SYNC = 32;      /* looking for synchronization bytes to restart inflate() */\n\n/* ===========================================================================*/\n\n\n\nvar ENOUGH_LENS = 852;\nvar ENOUGH_DISTS = 592;\n//var ENOUGH =  (ENOUGH_LENS+ENOUGH_DISTS);\n\nvar MAX_WBITS = 15;\n/* 32K LZ77 window */\nvar DEF_WBITS = MAX_WBITS;\n\n\nfunction zswap32(q) {\n  return  (((q >>> 24) & 0xff) +\n          ((q >>> 8) & 0xff00) +\n          ((q & 0xff00) << 8) +\n          ((q & 0xff) << 24));\n}\n\n\nfunction InflateState() {\n  this.mode = 0;             /* current inflate mode */\n  this.last = false;          /* true if processing last block */\n  this.wrap = 0;              /* bit 0 true for zlib, bit 1 true for gzip */\n  this.havedict = false;      /* true if dictionary provided */\n  this.flags = 0;             /* gzip header method and flags (0 if zlib) */\n  this.dmax = 0;              /* zlib header max distance (INFLATE_STRICT) */\n  this.check = 0;             /* protected copy of check value */\n  this.total = 0;             /* protected copy of output count */\n  // TODO: may be {}\n  this.head = null;           /* where to save gzip header information */\n\n  /* sliding window */\n  this.wbits = 0;             /* log base 2 of requested window size */\n  this.wsize = 0;             /* window size or zero if not using window */\n  this.whave = 0;             /* valid bytes in the window */\n  this.wnext = 0;             /* window write index */\n  this.window = null;         /* allocated sliding window, if needed */\n\n  /* bit accumulator */\n  this.hold = 0;              /* input bit accumulator */\n  this.bits = 0;              /* number of bits in \"in\" */\n\n  /* for string and stored block copying */\n  this.length = 0;            /* literal or length of data to copy */\n  this.offset = 0;            /* distance back to copy string from */\n\n  /* for table and code decoding */\n  this.extra = 0;             /* extra bits needed */\n\n  /* fixed and dynamic code tables */\n  this.lencode = null;          /* starting table for length/literal codes */\n  this.distcode = null;         /* starting table for distance codes */\n  this.lenbits = 0;           /* index bits for lencode */\n  this.distbits = 0;          /* index bits for distcode */\n\n  /* dynamic table building */\n  this.ncode = 0;             /* number of code length code lengths */\n  this.nlen = 0;              /* number of length code lengths */\n  this.ndist = 0;             /* number of distance code lengths */\n  this.have = 0;              /* number of code lengths in lens[] */\n  this.next = null;              /* next available space in codes[] */\n\n  this.lens = new utils.Buf16(320); /* temporary storage for code lengths */\n  this.work = new utils.Buf16(288); /* work area for code table building */\n\n  /*\n   because we don't have pointers in js, we use lencode and distcode directly\n   as buffers so we don't need codes\n  */\n  //this.codes = new utils.Buf32(ENOUGH);       /* space for code tables */\n  this.lendyn = null;              /* dynamic table for length/literal codes (JS specific) */\n  this.distdyn = null;             /* dynamic table for distance codes (JS specific) */\n  this.sane = 0;                   /* if false, allow invalid distance too far */\n  this.back = 0;                   /* bits back of last unprocessed length/lit */\n  this.was = 0;                    /* initial length of match */\n}\n\nfunction inflateResetKeep(strm) {\n  var state;\n\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  strm.total_in = strm.total_out = state.total = 0;\n  strm.msg = ''; /*Z_NULL*/\n  if (state.wrap) {       /* to support ill-conceived Java test suite */\n    strm.adler = state.wrap & 1;\n  }\n  state.mode = HEAD;\n  state.last = 0;\n  state.havedict = 0;\n  state.dmax = 32768;\n  state.head = null/*Z_NULL*/;\n  state.hold = 0;\n  state.bits = 0;\n  //state.lencode = state.distcode = state.next = state.codes;\n  state.lencode = state.lendyn = new utils.Buf32(ENOUGH_LENS);\n  state.distcode = state.distdyn = new utils.Buf32(ENOUGH_DISTS);\n\n  state.sane = 1;\n  state.back = -1;\n  //Tracev((stderr, \"inflate: reset\\n\"));\n  return Z_OK;\n}\n\nfunction inflateReset(strm) {\n  var state;\n\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  state.wsize = 0;\n  state.whave = 0;\n  state.wnext = 0;\n  return inflateResetKeep(strm);\n\n}\n\nfunction inflateReset2(strm, windowBits) {\n  var wrap;\n  var state;\n\n  /* get the state */\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n\n  /* extract wrap request from windowBits parameter */\n  if (windowBits < 0) {\n    wrap = 0;\n    windowBits = -windowBits;\n  }\n  else {\n    wrap = (windowBits >> 4) + 1;\n    if (windowBits < 48) {\n      windowBits &= 15;\n    }\n  }\n\n  /* set number of window bits, free window if different */\n  if (windowBits && (windowBits < 8 || windowBits > 15)) {\n    return Z_STREAM_ERROR;\n  }\n  if (state.window !== null && state.wbits !== windowBits) {\n    state.window = null;\n  }\n\n  /* update state and reset the rest of it */\n  state.wrap = wrap;\n  state.wbits = windowBits;\n  return inflateReset(strm);\n}\n\nfunction inflateInit2(strm, windowBits) {\n  var ret;\n  var state;\n\n  if (!strm) { return Z_STREAM_ERROR; }\n  //strm.msg = Z_NULL;                 /* in case we return an error */\n\n  state = new InflateState();\n\n  //if (state === Z_NULL) return Z_MEM_ERROR;\n  //Tracev((stderr, \"inflate: allocated\\n\"));\n  strm.state = state;\n  state.window = null/*Z_NULL*/;\n  ret = inflateReset2(strm, windowBits);\n  if (ret !== Z_OK) {\n    strm.state = null/*Z_NULL*/;\n  }\n  return ret;\n}\n\nfunction inflateInit(strm) {\n  return inflateInit2(strm, DEF_WBITS);\n}\n\n\n/*\n Return state with length and distance decoding tables and index sizes set to\n fixed code decoding.  Normally this returns fixed tables from inffixed.h.\n If BUILDFIXED is defined, then instead this routine builds the tables the\n first time it's called, and returns those tables the first time and\n thereafter.  This reduces the size of the code by about 2K bytes, in\n exchange for a little execution time.  However, BUILDFIXED should not be\n used for threaded applications, since the rewriting of the tables and virgin\n may not be thread-safe.\n */\nvar virgin = true;\n\nvar lenfix, distfix; // We have no pointers in JS, so keep tables separate\n\nfunction fixedtables(state) {\n  /* build fixed huffman tables if first call (may not be thread safe) */\n  if (virgin) {\n    var sym;\n\n    lenfix = new utils.Buf32(512);\n    distfix = new utils.Buf32(32);\n\n    /* literal/length table */\n    sym = 0;\n    while (sym < 144) { state.lens[sym++] = 8; }\n    while (sym < 256) { state.lens[sym++] = 9; }\n    while (sym < 280) { state.lens[sym++] = 7; }\n    while (sym < 288) { state.lens[sym++] = 8; }\n\n    inflate_table(LENS,  state.lens, 0, 288, lenfix,   0, state.work, { bits: 9 });\n\n    /* distance table */\n    sym = 0;\n    while (sym < 32) { state.lens[sym++] = 5; }\n\n    inflate_table(DISTS, state.lens, 0, 32,   distfix, 0, state.work, { bits: 5 });\n\n    /* do this just once */\n    virgin = false;\n  }\n\n  state.lencode = lenfix;\n  state.lenbits = 9;\n  state.distcode = distfix;\n  state.distbits = 5;\n}\n\n\n/*\n Update the window with the last wsize (normally 32K) bytes written before\n returning.  If window does not exist yet, create it.  This is only called\n when a window is already in use, or when output has been written during this\n inflate call, but the end of the deflate stream has not been reached yet.\n It is also called to create a window for dictionary data when a dictionary\n is loaded.\n\n Providing output buffers larger than 32K to inflate() should provide a speed\n advantage, since only the last 32K of output is copied to the sliding window\n upon return from inflate(), and since all distances after the first 32K of\n output will fall in the output data, making match copies simpler and faster.\n The advantage may be dependent on the size of the processor's data caches.\n */\nfunction updatewindow(strm, src, end, copy) {\n  var dist;\n  var state = strm.state;\n\n  /* if it hasn't been done already, allocate space for the window */\n  if (state.window === null) {\n    state.wsize = 1 << state.wbits;\n    state.wnext = 0;\n    state.whave = 0;\n\n    state.window = new utils.Buf8(state.wsize);\n  }\n\n  /* copy state->wsize or less output bytes into the circular window */\n  if (copy >= state.wsize) {\n    utils.arraySet(state.window, src, end - state.wsize, state.wsize, 0);\n    state.wnext = 0;\n    state.whave = state.wsize;\n  }\n  else {\n    dist = state.wsize - state.wnext;\n    if (dist > copy) {\n      dist = copy;\n    }\n    //zmemcpy(state->window + state->wnext, end - copy, dist);\n    utils.arraySet(state.window, src, end - copy, dist, state.wnext);\n    copy -= dist;\n    if (copy) {\n      //zmemcpy(state->window, end - copy, copy);\n      utils.arraySet(state.window, src, end - copy, copy, 0);\n      state.wnext = copy;\n      state.whave = state.wsize;\n    }\n    else {\n      state.wnext += dist;\n      if (state.wnext === state.wsize) { state.wnext = 0; }\n      if (state.whave < state.wsize) { state.whave += dist; }\n    }\n  }\n  return 0;\n}\n\nfunction inflate(strm, flush) {\n  var state;\n  var input, output;          // input/output buffers\n  var next;                   /* next input INDEX */\n  var put;                    /* next output INDEX */\n  var have, left;             /* available input and output */\n  var hold;                   /* bit buffer */\n  var bits;                   /* bits in bit buffer */\n  var _in, _out;              /* save starting available input and output */\n  var copy;                   /* number of stored or match bytes to copy */\n  var from;                   /* where to copy match bytes from */\n  var from_source;\n  var here = 0;               /* current decoding table entry */\n  var here_bits, here_op, here_val; // paked \"here\" denormalized (JS specific)\n  //var last;                   /* parent table entry */\n  var last_bits, last_op, last_val; // paked \"last\" denormalized (JS specific)\n  var len;                    /* length to copy for repeats, bits to drop */\n  var ret;                    /* return code */\n  var hbuf = new utils.Buf8(4);    /* buffer for gzip header crc calculation */\n  var opts;\n\n  var n; // temporary var for NEED_BITS\n\n  var order = /* permutation of code lengths */\n    [ 16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15 ];\n\n\n  if (!strm || !strm.state || !strm.output ||\n      (!strm.input && strm.avail_in !== 0)) {\n    return Z_STREAM_ERROR;\n  }\n\n  state = strm.state;\n  if (state.mode === TYPE) { state.mode = TYPEDO; }    /* skip check */\n\n\n  //--- LOAD() ---\n  put = strm.next_out;\n  output = strm.output;\n  left = strm.avail_out;\n  next = strm.next_in;\n  input = strm.input;\n  have = strm.avail_in;\n  hold = state.hold;\n  bits = state.bits;\n  //---\n\n  _in = have;\n  _out = left;\n  ret = Z_OK;\n\n  inf_leave: // goto emulation\n  for (;;) {\n    switch (state.mode) {\n      case HEAD:\n        if (state.wrap === 0) {\n          state.mode = TYPEDO;\n          break;\n        }\n        //=== NEEDBITS(16);\n        while (bits < 16) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if ((state.wrap & 2) && hold === 0x8b1f) {  /* gzip header */\n          state.check = 0/*crc32(0L, Z_NULL, 0)*/;\n          //=== CRC2(state.check, hold);\n          hbuf[0] = hold & 0xff;\n          hbuf[1] = (hold >>> 8) & 0xff;\n          state.check = crc32(state.check, hbuf, 2, 0);\n          //===//\n\n          //=== INITBITS();\n          hold = 0;\n          bits = 0;\n          //===//\n          state.mode = FLAGS;\n          break;\n        }\n        state.flags = 0;           /* expect zlib header */\n        if (state.head) {\n          state.head.done = false;\n        }\n        if (!(state.wrap & 1) ||   /* check if zlib header allowed */\n          (((hold & 0xff)/*BITS(8)*/ << 8) + (hold >> 8)) % 31) {\n          strm.msg = 'incorrect header check';\n          state.mode = BAD;\n          break;\n        }\n        if ((hold & 0x0f)/*BITS(4)*/ !== Z_DEFLATED) {\n          strm.msg = 'unknown compression method';\n          state.mode = BAD;\n          break;\n        }\n        //--- DROPBITS(4) ---//\n        hold >>>= 4;\n        bits -= 4;\n        //---//\n        len = (hold & 0x0f)/*BITS(4)*/ + 8;\n        if (state.wbits === 0) {\n          state.wbits = len;\n        }\n        else if (len > state.wbits) {\n          strm.msg = 'invalid window size';\n          state.mode = BAD;\n          break;\n        }\n        state.dmax = 1 << len;\n        //Tracev((stderr, \"inflate:   zlib header ok\\n\"));\n        strm.adler = state.check = 1/*adler32(0L, Z_NULL, 0)*/;\n        state.mode = hold & 0x200 ? DICTID : TYPE;\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        break;\n      case FLAGS:\n        //=== NEEDBITS(16); */\n        while (bits < 16) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.flags = hold;\n        if ((state.flags & 0xff) !== Z_DEFLATED) {\n          strm.msg = 'unknown compression method';\n          state.mode = BAD;\n          break;\n        }\n        if (state.flags & 0xe000) {\n          strm.msg = 'unknown header flags set';\n          state.mode = BAD;\n          break;\n        }\n        if (state.head) {\n          state.head.text = ((hold >> 8) & 1);\n        }\n        if (state.flags & 0x0200) {\n          //=== CRC2(state.check, hold);\n          hbuf[0] = hold & 0xff;\n          hbuf[1] = (hold >>> 8) & 0xff;\n          state.check = crc32(state.check, hbuf, 2, 0);\n          //===//\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = TIME;\n        /* falls through */\n      case TIME:\n        //=== NEEDBITS(32); */\n        while (bits < 32) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if (state.head) {\n          state.head.time = hold;\n        }\n        if (state.flags & 0x0200) {\n          //=== CRC4(state.check, hold)\n          hbuf[0] = hold & 0xff;\n          hbuf[1] = (hold >>> 8) & 0xff;\n          hbuf[2] = (hold >>> 16) & 0xff;\n          hbuf[3] = (hold >>> 24) & 0xff;\n          state.check = crc32(state.check, hbuf, 4, 0);\n          //===\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = OS;\n        /* falls through */\n      case OS:\n        //=== NEEDBITS(16); */\n        while (bits < 16) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if (state.head) {\n          state.head.xflags = (hold & 0xff);\n          state.head.os = (hold >> 8);\n        }\n        if (state.flags & 0x0200) {\n          //=== CRC2(state.check, hold);\n          hbuf[0] = hold & 0xff;\n          hbuf[1] = (hold >>> 8) & 0xff;\n          state.check = crc32(state.check, hbuf, 2, 0);\n          //===//\n        }\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = EXLEN;\n        /* falls through */\n      case EXLEN:\n        if (state.flags & 0x0400) {\n          //=== NEEDBITS(16); */\n          while (bits < 16) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          state.length = hold;\n          if (state.head) {\n            state.head.extra_len = hold;\n          }\n          if (state.flags & 0x0200) {\n            //=== CRC2(state.check, hold);\n            hbuf[0] = hold & 0xff;\n            hbuf[1] = (hold >>> 8) & 0xff;\n            state.check = crc32(state.check, hbuf, 2, 0);\n            //===//\n          }\n          //=== INITBITS();\n          hold = 0;\n          bits = 0;\n          //===//\n        }\n        else if (state.head) {\n          state.head.extra = null/*Z_NULL*/;\n        }\n        state.mode = EXTRA;\n        /* falls through */\n      case EXTRA:\n        if (state.flags & 0x0400) {\n          copy = state.length;\n          if (copy > have) { copy = have; }\n          if (copy) {\n            if (state.head) {\n              len = state.head.extra_len - state.length;\n              if (!state.head.extra) {\n                // Use untyped array for more convenient processing later\n                state.head.extra = new Array(state.head.extra_len);\n              }\n              utils.arraySet(\n                state.head.extra,\n                input,\n                next,\n                // extra field is limited to 65536 bytes\n                // - no need for additional size check\n                copy,\n                /*len + copy > state.head.extra_max - len ? state.head.extra_max : copy,*/\n                len\n              );\n              //zmemcpy(state.head.extra + len, next,\n              //        len + copy > state.head.extra_max ?\n              //        state.head.extra_max - len : copy);\n            }\n            if (state.flags & 0x0200) {\n              state.check = crc32(state.check, input, copy, next);\n            }\n            have -= copy;\n            next += copy;\n            state.length -= copy;\n          }\n          if (state.length) { break inf_leave; }\n        }\n        state.length = 0;\n        state.mode = NAME;\n        /* falls through */\n      case NAME:\n        if (state.flags & 0x0800) {\n          if (have === 0) { break inf_leave; }\n          copy = 0;\n          do {\n            // TODO: 2 or 1 bytes?\n            len = input[next + copy++];\n            /* use constant limit because in js we should not preallocate memory */\n            if (state.head && len &&\n                (state.length < 65536 /*state.head.name_max*/)) {\n              state.head.name += String.fromCharCode(len);\n            }\n          } while (len && copy < have);\n\n          if (state.flags & 0x0200) {\n            state.check = crc32(state.check, input, copy, next);\n          }\n          have -= copy;\n          next += copy;\n          if (len) { break inf_leave; }\n        }\n        else if (state.head) {\n          state.head.name = null;\n        }\n        state.length = 0;\n        state.mode = COMMENT;\n        /* falls through */\n      case COMMENT:\n        if (state.flags & 0x1000) {\n          if (have === 0) { break inf_leave; }\n          copy = 0;\n          do {\n            len = input[next + copy++];\n            /* use constant limit because in js we should not preallocate memory */\n            if (state.head && len &&\n                (state.length < 65536 /*state.head.comm_max*/)) {\n              state.head.comment += String.fromCharCode(len);\n            }\n          } while (len && copy < have);\n          if (state.flags & 0x0200) {\n            state.check = crc32(state.check, input, copy, next);\n          }\n          have -= copy;\n          next += copy;\n          if (len) { break inf_leave; }\n        }\n        else if (state.head) {\n          state.head.comment = null;\n        }\n        state.mode = HCRC;\n        /* falls through */\n      case HCRC:\n        if (state.flags & 0x0200) {\n          //=== NEEDBITS(16); */\n          while (bits < 16) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          if (hold !== (state.check & 0xffff)) {\n            strm.msg = 'header crc mismatch';\n            state.mode = BAD;\n            break;\n          }\n          //=== INITBITS();\n          hold = 0;\n          bits = 0;\n          //===//\n        }\n        if (state.head) {\n          state.head.hcrc = ((state.flags >> 9) & 1);\n          state.head.done = true;\n        }\n        strm.adler = state.check = 0;\n        state.mode = TYPE;\n        break;\n      case DICTID:\n        //=== NEEDBITS(32); */\n        while (bits < 32) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        strm.adler = state.check = zswap32(hold);\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = DICT;\n        /* falls through */\n      case DICT:\n        if (state.havedict === 0) {\n          //--- RESTORE() ---\n          strm.next_out = put;\n          strm.avail_out = left;\n          strm.next_in = next;\n          strm.avail_in = have;\n          state.hold = hold;\n          state.bits = bits;\n          //---\n          return Z_NEED_DICT;\n        }\n        strm.adler = state.check = 1/*adler32(0L, Z_NULL, 0)*/;\n        state.mode = TYPE;\n        /* falls through */\n      case TYPE:\n        if (flush === Z_BLOCK || flush === Z_TREES) { break inf_leave; }\n        /* falls through */\n      case TYPEDO:\n        if (state.last) {\n          //--- BYTEBITS() ---//\n          hold >>>= bits & 7;\n          bits -= bits & 7;\n          //---//\n          state.mode = CHECK;\n          break;\n        }\n        //=== NEEDBITS(3); */\n        while (bits < 3) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.last = (hold & 0x01)/*BITS(1)*/;\n        //--- DROPBITS(1) ---//\n        hold >>>= 1;\n        bits -= 1;\n        //---//\n\n        switch ((hold & 0x03)/*BITS(2)*/) {\n          case 0:                             /* stored block */\n            //Tracev((stderr, \"inflate:     stored block%s\\n\",\n            //        state.last ? \" (last)\" : \"\"));\n            state.mode = STORED;\n            break;\n          case 1:                             /* fixed block */\n            fixedtables(state);\n            //Tracev((stderr, \"inflate:     fixed codes block%s\\n\",\n            //        state.last ? \" (last)\" : \"\"));\n            state.mode = LEN_;             /* decode codes */\n            if (flush === Z_TREES) {\n              //--- DROPBITS(2) ---//\n              hold >>>= 2;\n              bits -= 2;\n              //---//\n              break inf_leave;\n            }\n            break;\n          case 2:                             /* dynamic block */\n            //Tracev((stderr, \"inflate:     dynamic codes block%s\\n\",\n            //        state.last ? \" (last)\" : \"\"));\n            state.mode = TABLE;\n            break;\n          case 3:\n            strm.msg = 'invalid block type';\n            state.mode = BAD;\n        }\n        //--- DROPBITS(2) ---//\n        hold >>>= 2;\n        bits -= 2;\n        //---//\n        break;\n      case STORED:\n        //--- BYTEBITS() ---// /* go to byte boundary */\n        hold >>>= bits & 7;\n        bits -= bits & 7;\n        //---//\n        //=== NEEDBITS(32); */\n        while (bits < 32) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        if ((hold & 0xffff) !== ((hold >>> 16) ^ 0xffff)) {\n          strm.msg = 'invalid stored block lengths';\n          state.mode = BAD;\n          break;\n        }\n        state.length = hold & 0xffff;\n        //Tracev((stderr, \"inflate:       stored length %u\\n\",\n        //        state.length));\n        //=== INITBITS();\n        hold = 0;\n        bits = 0;\n        //===//\n        state.mode = COPY_;\n        if (flush === Z_TREES) { break inf_leave; }\n        /* falls through */\n      case COPY_:\n        state.mode = COPY;\n        /* falls through */\n      case COPY:\n        copy = state.length;\n        if (copy) {\n          if (copy > have) { copy = have; }\n          if (copy > left) { copy = left; }\n          if (copy === 0) { break inf_leave; }\n          //--- zmemcpy(put, next, copy); ---\n          utils.arraySet(output, input, next, copy, put);\n          //---//\n          have -= copy;\n          next += copy;\n          left -= copy;\n          put += copy;\n          state.length -= copy;\n          break;\n        }\n        //Tracev((stderr, \"inflate:       stored end\\n\"));\n        state.mode = TYPE;\n        break;\n      case TABLE:\n        //=== NEEDBITS(14); */\n        while (bits < 14) {\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n        }\n        //===//\n        state.nlen = (hold & 0x1f)/*BITS(5)*/ + 257;\n        //--- DROPBITS(5) ---//\n        hold >>>= 5;\n        bits -= 5;\n        //---//\n        state.ndist = (hold & 0x1f)/*BITS(5)*/ + 1;\n        //--- DROPBITS(5) ---//\n        hold >>>= 5;\n        bits -= 5;\n        //---//\n        state.ncode = (hold & 0x0f)/*BITS(4)*/ + 4;\n        //--- DROPBITS(4) ---//\n        hold >>>= 4;\n        bits -= 4;\n        //---//\n//#ifndef PKZIP_BUG_WORKAROUND\n        if (state.nlen > 286 || state.ndist > 30) {\n          strm.msg = 'too many length or distance symbols';\n          state.mode = BAD;\n          break;\n        }\n//#endif\n        //Tracev((stderr, \"inflate:       table sizes ok\\n\"));\n        state.have = 0;\n        state.mode = LENLENS;\n        /* falls through */\n      case LENLENS:\n        while (state.have < state.ncode) {\n          //=== NEEDBITS(3);\n          while (bits < 3) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          state.lens[order[state.have++]] = (hold & 0x07);//BITS(3);\n          //--- DROPBITS(3) ---//\n          hold >>>= 3;\n          bits -= 3;\n          //---//\n        }\n        while (state.have < 19) {\n          state.lens[order[state.have++]] = 0;\n        }\n        // We have separate tables & no pointers. 2 commented lines below not needed.\n        //state.next = state.codes;\n        //state.lencode = state.next;\n        // Switch to use dynamic table\n        state.lencode = state.lendyn;\n        state.lenbits = 7;\n\n        opts = { bits: state.lenbits };\n        ret = inflate_table(CODES, state.lens, 0, 19, state.lencode, 0, state.work, opts);\n        state.lenbits = opts.bits;\n\n        if (ret) {\n          strm.msg = 'invalid code lengths set';\n          state.mode = BAD;\n          break;\n        }\n        //Tracev((stderr, \"inflate:       code lengths ok\\n\"));\n        state.have = 0;\n        state.mode = CODELENS;\n        /* falls through */\n      case CODELENS:\n        while (state.have < state.nlen + state.ndist) {\n          for (;;) {\n            here = state.lencode[hold & ((1 << state.lenbits) - 1)];/*BITS(state.lenbits)*/\n            here_bits = here >>> 24;\n            here_op = (here >>> 16) & 0xff;\n            here_val = here & 0xffff;\n\n            if ((here_bits) <= bits) { break; }\n            //--- PULLBYTE() ---//\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n            //---//\n          }\n          if (here_val < 16) {\n            //--- DROPBITS(here.bits) ---//\n            hold >>>= here_bits;\n            bits -= here_bits;\n            //---//\n            state.lens[state.have++] = here_val;\n          }\n          else {\n            if (here_val === 16) {\n              //=== NEEDBITS(here.bits + 2);\n              n = here_bits + 2;\n              while (bits < n) {\n                if (have === 0) { break inf_leave; }\n                have--;\n                hold += input[next++] << bits;\n                bits += 8;\n              }\n              //===//\n              //--- DROPBITS(here.bits) ---//\n              hold >>>= here_bits;\n              bits -= here_bits;\n              //---//\n              if (state.have === 0) {\n                strm.msg = 'invalid bit length repeat';\n                state.mode = BAD;\n                break;\n              }\n              len = state.lens[state.have - 1];\n              copy = 3 + (hold & 0x03);//BITS(2);\n              //--- DROPBITS(2) ---//\n              hold >>>= 2;\n              bits -= 2;\n              //---//\n            }\n            else if (here_val === 17) {\n              //=== NEEDBITS(here.bits + 3);\n              n = here_bits + 3;\n              while (bits < n) {\n                if (have === 0) { break inf_leave; }\n                have--;\n                hold += input[next++] << bits;\n                bits += 8;\n              }\n              //===//\n              //--- DROPBITS(here.bits) ---//\n              hold >>>= here_bits;\n              bits -= here_bits;\n              //---//\n              len = 0;\n              copy = 3 + (hold & 0x07);//BITS(3);\n              //--- DROPBITS(3) ---//\n              hold >>>= 3;\n              bits -= 3;\n              //---//\n            }\n            else {\n              //=== NEEDBITS(here.bits + 7);\n              n = here_bits + 7;\n              while (bits < n) {\n                if (have === 0) { break inf_leave; }\n                have--;\n                hold += input[next++] << bits;\n                bits += 8;\n              }\n              //===//\n              //--- DROPBITS(here.bits) ---//\n              hold >>>= here_bits;\n              bits -= here_bits;\n              //---//\n              len = 0;\n              copy = 11 + (hold & 0x7f);//BITS(7);\n              //--- DROPBITS(7) ---//\n              hold >>>= 7;\n              bits -= 7;\n              //---//\n            }\n            if (state.have + copy > state.nlen + state.ndist) {\n              strm.msg = 'invalid bit length repeat';\n              state.mode = BAD;\n              break;\n            }\n            while (copy--) {\n              state.lens[state.have++] = len;\n            }\n          }\n        }\n\n        /* handle error breaks in while */\n        if (state.mode === BAD) { break; }\n\n        /* check for end-of-block code (better have one) */\n        if (state.lens[256] === 0) {\n          strm.msg = 'invalid code -- missing end-of-block';\n          state.mode = BAD;\n          break;\n        }\n\n        /* build code tables -- note: do not change the lenbits or distbits\n           values here (9 and 6) without reading the comments in inftrees.h\n           concerning the ENOUGH constants, which depend on those values */\n        state.lenbits = 9;\n\n        opts = { bits: state.lenbits };\n        ret = inflate_table(LENS, state.lens, 0, state.nlen, state.lencode, 0, state.work, opts);\n        // We have separate tables & no pointers. 2 commented lines below not needed.\n        // state.next_index = opts.table_index;\n        state.lenbits = opts.bits;\n        // state.lencode = state.next;\n\n        if (ret) {\n          strm.msg = 'invalid literal/lengths set';\n          state.mode = BAD;\n          break;\n        }\n\n        state.distbits = 6;\n        //state.distcode.copy(state.codes);\n        // Switch to use dynamic table\n        state.distcode = state.distdyn;\n        opts = { bits: state.distbits };\n        ret = inflate_table(DISTS, state.lens, state.nlen, state.ndist, state.distcode, 0, state.work, opts);\n        // We have separate tables & no pointers. 2 commented lines below not needed.\n        // state.next_index = opts.table_index;\n        state.distbits = opts.bits;\n        // state.distcode = state.next;\n\n        if (ret) {\n          strm.msg = 'invalid distances set';\n          state.mode = BAD;\n          break;\n        }\n        //Tracev((stderr, 'inflate:       codes ok\\n'));\n        state.mode = LEN_;\n        if (flush === Z_TREES) { break inf_leave; }\n        /* falls through */\n      case LEN_:\n        state.mode = LEN;\n        /* falls through */\n      case LEN:\n        if (have >= 6 && left >= 258) {\n          //--- RESTORE() ---\n          strm.next_out = put;\n          strm.avail_out = left;\n          strm.next_in = next;\n          strm.avail_in = have;\n          state.hold = hold;\n          state.bits = bits;\n          //---\n          inflate_fast(strm, _out);\n          //--- LOAD() ---\n          put = strm.next_out;\n          output = strm.output;\n          left = strm.avail_out;\n          next = strm.next_in;\n          input = strm.input;\n          have = strm.avail_in;\n          hold = state.hold;\n          bits = state.bits;\n          //---\n\n          if (state.mode === TYPE) {\n            state.back = -1;\n          }\n          break;\n        }\n        state.back = 0;\n        for (;;) {\n          here = state.lencode[hold & ((1 << state.lenbits) - 1)];  /*BITS(state.lenbits)*/\n          here_bits = here >>> 24;\n          here_op = (here >>> 16) & 0xff;\n          here_val = here & 0xffff;\n\n          if (here_bits <= bits) { break; }\n          //--- PULLBYTE() ---//\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n          //---//\n        }\n        if (here_op && (here_op & 0xf0) === 0) {\n          last_bits = here_bits;\n          last_op = here_op;\n          last_val = here_val;\n          for (;;) {\n            here = state.lencode[last_val +\n                    ((hold & ((1 << (last_bits + last_op)) - 1))/*BITS(last.bits + last.op)*/ >> last_bits)];\n            here_bits = here >>> 24;\n            here_op = (here >>> 16) & 0xff;\n            here_val = here & 0xffff;\n\n            if ((last_bits + here_bits) <= bits) { break; }\n            //--- PULLBYTE() ---//\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n            //---//\n          }\n          //--- DROPBITS(last.bits) ---//\n          hold >>>= last_bits;\n          bits -= last_bits;\n          //---//\n          state.back += last_bits;\n        }\n        //--- DROPBITS(here.bits) ---//\n        hold >>>= here_bits;\n        bits -= here_bits;\n        //---//\n        state.back += here_bits;\n        state.length = here_val;\n        if (here_op === 0) {\n          //Tracevv((stderr, here.val >= 0x20 && here.val < 0x7f ?\n          //        \"inflate:         literal '%c'\\n\" :\n          //        \"inflate:         literal 0x%02x\\n\", here.val));\n          state.mode = LIT;\n          break;\n        }\n        if (here_op & 32) {\n          //Tracevv((stderr, \"inflate:         end of block\\n\"));\n          state.back = -1;\n          state.mode = TYPE;\n          break;\n        }\n        if (here_op & 64) {\n          strm.msg = 'invalid literal/length code';\n          state.mode = BAD;\n          break;\n        }\n        state.extra = here_op & 15;\n        state.mode = LENEXT;\n        /* falls through */\n      case LENEXT:\n        if (state.extra) {\n          //=== NEEDBITS(state.extra);\n          n = state.extra;\n          while (bits < n) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          state.length += hold & ((1 << state.extra) - 1)/*BITS(state.extra)*/;\n          //--- DROPBITS(state.extra) ---//\n          hold >>>= state.extra;\n          bits -= state.extra;\n          //---//\n          state.back += state.extra;\n        }\n        //Tracevv((stderr, \"inflate:         length %u\\n\", state.length));\n        state.was = state.length;\n        state.mode = DIST;\n        /* falls through */\n      case DIST:\n        for (;;) {\n          here = state.distcode[hold & ((1 << state.distbits) - 1)];/*BITS(state.distbits)*/\n          here_bits = here >>> 24;\n          here_op = (here >>> 16) & 0xff;\n          here_val = here & 0xffff;\n\n          if ((here_bits) <= bits) { break; }\n          //--- PULLBYTE() ---//\n          if (have === 0) { break inf_leave; }\n          have--;\n          hold += input[next++] << bits;\n          bits += 8;\n          //---//\n        }\n        if ((here_op & 0xf0) === 0) {\n          last_bits = here_bits;\n          last_op = here_op;\n          last_val = here_val;\n          for (;;) {\n            here = state.distcode[last_val +\n                    ((hold & ((1 << (last_bits + last_op)) - 1))/*BITS(last.bits + last.op)*/ >> last_bits)];\n            here_bits = here >>> 24;\n            here_op = (here >>> 16) & 0xff;\n            here_val = here & 0xffff;\n\n            if ((last_bits + here_bits) <= bits) { break; }\n            //--- PULLBYTE() ---//\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n            //---//\n          }\n          //--- DROPBITS(last.bits) ---//\n          hold >>>= last_bits;\n          bits -= last_bits;\n          //---//\n          state.back += last_bits;\n        }\n        //--- DROPBITS(here.bits) ---//\n        hold >>>= here_bits;\n        bits -= here_bits;\n        //---//\n        state.back += here_bits;\n        if (here_op & 64) {\n          strm.msg = 'invalid distance code';\n          state.mode = BAD;\n          break;\n        }\n        state.offset = here_val;\n        state.extra = (here_op) & 15;\n        state.mode = DISTEXT;\n        /* falls through */\n      case DISTEXT:\n        if (state.extra) {\n          //=== NEEDBITS(state.extra);\n          n = state.extra;\n          while (bits < n) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          state.offset += hold & ((1 << state.extra) - 1)/*BITS(state.extra)*/;\n          //--- DROPBITS(state.extra) ---//\n          hold >>>= state.extra;\n          bits -= state.extra;\n          //---//\n          state.back += state.extra;\n        }\n//#ifdef INFLATE_STRICT\n        if (state.offset > state.dmax) {\n          strm.msg = 'invalid distance too far back';\n          state.mode = BAD;\n          break;\n        }\n//#endif\n        //Tracevv((stderr, \"inflate:         distance %u\\n\", state.offset));\n        state.mode = MATCH;\n        /* falls through */\n      case MATCH:\n        if (left === 0) { break inf_leave; }\n        copy = _out - left;\n        if (state.offset > copy) {         /* copy from window */\n          copy = state.offset - copy;\n          if (copy > state.whave) {\n            if (state.sane) {\n              strm.msg = 'invalid distance too far back';\n              state.mode = BAD;\n              break;\n            }\n// (!) This block is disabled in zlib defaults,\n// don't enable it for binary compatibility\n//#ifdef INFLATE_ALLOW_INVALID_DISTANCE_TOOFAR_ARRR\n//          Trace((stderr, \"inflate.c too far\\n\"));\n//          copy -= state.whave;\n//          if (copy > state.length) { copy = state.length; }\n//          if (copy > left) { copy = left; }\n//          left -= copy;\n//          state.length -= copy;\n//          do {\n//            output[put++] = 0;\n//          } while (--copy);\n//          if (state.length === 0) { state.mode = LEN; }\n//          break;\n//#endif\n          }\n          if (copy > state.wnext) {\n            copy -= state.wnext;\n            from = state.wsize - copy;\n          }\n          else {\n            from = state.wnext - copy;\n          }\n          if (copy > state.length) { copy = state.length; }\n          from_source = state.window;\n        }\n        else {                              /* copy from output */\n          from_source = output;\n          from = put - state.offset;\n          copy = state.length;\n        }\n        if (copy > left) { copy = left; }\n        left -= copy;\n        state.length -= copy;\n        do {\n          output[put++] = from_source[from++];\n        } while (--copy);\n        if (state.length === 0) { state.mode = LEN; }\n        break;\n      case LIT:\n        if (left === 0) { break inf_leave; }\n        output[put++] = state.length;\n        left--;\n        state.mode = LEN;\n        break;\n      case CHECK:\n        if (state.wrap) {\n          //=== NEEDBITS(32);\n          while (bits < 32) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            // Use '|' instead of '+' to make sure that result is signed\n            hold |= input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          _out -= left;\n          strm.total_out += _out;\n          state.total += _out;\n          if (_out) {\n            strm.adler = state.check =\n                /*UPDATE(state.check, put - _out, _out);*/\n                (state.flags ? crc32(state.check, output, _out, put - _out) : adler32(state.check, output, _out, put - _out));\n\n          }\n          _out = left;\n          // NB: crc32 stored as signed 32-bit int, zswap32 returns signed too\n          if ((state.flags ? hold : zswap32(hold)) !== state.check) {\n            strm.msg = 'incorrect data check';\n            state.mode = BAD;\n            break;\n          }\n          //=== INITBITS();\n          hold = 0;\n          bits = 0;\n          //===//\n          //Tracev((stderr, \"inflate:   check matches trailer\\n\"));\n        }\n        state.mode = LENGTH;\n        /* falls through */\n      case LENGTH:\n        if (state.wrap && state.flags) {\n          //=== NEEDBITS(32);\n          while (bits < 32) {\n            if (have === 0) { break inf_leave; }\n            have--;\n            hold += input[next++] << bits;\n            bits += 8;\n          }\n          //===//\n          if (hold !== (state.total & 0xffffffff)) {\n            strm.msg = 'incorrect length check';\n            state.mode = BAD;\n            break;\n          }\n          //=== INITBITS();\n          hold = 0;\n          bits = 0;\n          //===//\n          //Tracev((stderr, \"inflate:   length matches trailer\\n\"));\n        }\n        state.mode = DONE;\n        /* falls through */\n      case DONE:\n        ret = Z_STREAM_END;\n        break inf_leave;\n      case BAD:\n        ret = Z_DATA_ERROR;\n        break inf_leave;\n      case MEM:\n        return Z_MEM_ERROR;\n      case SYNC:\n        /* falls through */\n      default:\n        return Z_STREAM_ERROR;\n    }\n  }\n\n  // inf_leave <- here is real place for \"goto inf_leave\", emulated via \"break inf_leave\"\n\n  /*\n     Return from inflate(), updating the total counts and the check value.\n     If there was no progress during the inflate() call, return a buffer\n     error.  Call updatewindow() to create and/or update the window state.\n     Note: a memory error from inflate() is non-recoverable.\n   */\n\n  //--- RESTORE() ---\n  strm.next_out = put;\n  strm.avail_out = left;\n  strm.next_in = next;\n  strm.avail_in = have;\n  state.hold = hold;\n  state.bits = bits;\n  //---\n\n  if (state.wsize || (_out !== strm.avail_out && state.mode < BAD &&\n                      (state.mode < CHECK || flush !== Z_FINISH))) {\n    if (updatewindow(strm, strm.output, strm.next_out, _out - strm.avail_out)) {\n      state.mode = MEM;\n      return Z_MEM_ERROR;\n    }\n  }\n  _in -= strm.avail_in;\n  _out -= strm.avail_out;\n  strm.total_in += _in;\n  strm.total_out += _out;\n  state.total += _out;\n  if (state.wrap && _out) {\n    strm.adler = state.check = /*UPDATE(state.check, strm.next_out - _out, _out);*/\n      (state.flags ? crc32(state.check, output, _out, strm.next_out - _out) : adler32(state.check, output, _out, strm.next_out - _out));\n  }\n  strm.data_type = state.bits + (state.last ? 64 : 0) +\n                    (state.mode === TYPE ? 128 : 0) +\n                    (state.mode === LEN_ || state.mode === COPY_ ? 256 : 0);\n  if (((_in === 0 && _out === 0) || flush === Z_FINISH) && ret === Z_OK) {\n    ret = Z_BUF_ERROR;\n  }\n  return ret;\n}\n\nfunction inflateEnd(strm) {\n\n  if (!strm || !strm.state /*|| strm->zfree == (free_func)0*/) {\n    return Z_STREAM_ERROR;\n  }\n\n  var state = strm.state;\n  if (state.window) {\n    state.window = null;\n  }\n  strm.state = null;\n  return Z_OK;\n}\n\nfunction inflateGetHeader(strm, head) {\n  var state;\n\n  /* check state */\n  if (!strm || !strm.state) { return Z_STREAM_ERROR; }\n  state = strm.state;\n  if ((state.wrap & 2) === 0) { return Z_STREAM_ERROR; }\n\n  /* save header structure */\n  state.head = head;\n  head.done = false;\n  return Z_OK;\n}\n\nfunction inflateSetDictionary(strm, dictionary) {\n  var dictLength = dictionary.length;\n\n  var state;\n  var dictid;\n  var ret;\n\n  /* check state */\n  if (!strm /* == Z_NULL */ || !strm.state /* == Z_NULL */) { return Z_STREAM_ERROR; }\n  state = strm.state;\n\n  if (state.wrap !== 0 && state.mode !== DICT) {\n    return Z_STREAM_ERROR;\n  }\n\n  /* check for correct dictionary identifier */\n  if (state.mode === DICT) {\n    dictid = 1; /* adler32(0, null, 0)*/\n    /* dictid = adler32(dictid, dictionary, dictLength); */\n    dictid = adler32(dictid, dictionary, dictLength, 0);\n    if (dictid !== state.check) {\n      return Z_DATA_ERROR;\n    }\n  }\n  /* copy dictionary to window using updatewindow(), which will amend the\n   existing dictionary if appropriate */\n  ret = updatewindow(strm, dictionary, dictLength, dictLength);\n  if (ret) {\n    state.mode = MEM;\n    return Z_MEM_ERROR;\n  }\n  state.havedict = 1;\n  // Tracev((stderr, \"inflate:   dictionary set\\n\"));\n  return Z_OK;\n}\n\nexports.inflateReset = inflateReset;\nexports.inflateReset2 = inflateReset2;\nexports.inflateResetKeep = inflateResetKeep;\nexports.inflateInit = inflateInit;\nexports.inflateInit2 = inflateInit2;\nexports.inflate = inflate;\nexports.inflateEnd = inflateEnd;\nexports.inflateGetHeader = inflateGetHeader;\nexports.inflateSetDictionary = inflateSetDictionary;\nexports.inflateInfo = 'pako inflate (from Nodeca project)';\n\n/* Not implemented\nexports.inflateCopy = inflateCopy;\nexports.inflateGetDictionary = inflateGetDictionary;\nexports.inflateMark = inflateMark;\nexports.inflatePrime = inflatePrime;\nexports.inflateSync = inflateSync;\nexports.inflateSyncPoint = inflateSyncPoint;\nexports.inflateUndermine = inflateUndermine;\n*/\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AAEZ,8EAA8E,GAC9E,8EAA8E,GAG9E,uEAAuE,GACvE,0BAA0B;AAC1B,0BAA0B;AAC1B,0BAA0B;AAC1B,0BAA0B;AAC1B,IAAI,WAAkB;AACtB,IAAI,UAAkB;AACtB,IAAI,UAAkB;AAGtB;;CAEC,GACD,IAAI,OAAkB;AACtB,IAAI,eAAkB;AACtB,IAAI,cAAkB;AACtB,2BAA2B;AAC3B,IAAI,iBAAkB,CAAC;AACvB,IAAI,eAAkB,CAAC;AACvB,IAAI,cAAkB,CAAC;AACvB,IAAI,cAAkB,CAAC;AACvB,2BAA2B;AAE3B,kCAAkC,GAClC,IAAI,aAAc;AAGlB,8EAA8E,GAC9E,8EAA8E,GAG9E,IAAO,OAAO,GAAS,+BAA+B;AACtD,IAAO,QAAQ,GAAQ,0CAA0C;AACjE,IAAO,OAAO,GAAS,2CAA2C;AAClE,IAAO,KAAK,GAAW,0DAA0D;AACjF,IAAO,QAAQ,GAAQ,sCAAsC;AAC7D,IAAO,QAAQ,GAAQ,qCAAqC;AAC5D,IAAO,OAAO,GAAS,0CAA0C;AACjE,IAAO,UAAU,GAAM,wCAAwC;AAC/D,IAAO,OAAO,GAAS,oCAAoC;AAC3D,IAAO,SAAS,IAAO,yCAAyC;AAChE,IAAO,OAAO,IAAS,2CAA2C;AAClE,IAAW,OAAO,IAAS,qDAAqD;AAChF,IAAW,SAAS,IAAO,wDAAwD;AACnF,IAAW,SAAS,IAAO,sDAAsD;AACjF,IAAW,QAAQ,IAAQ,mDAAmD;AAC9E,IAAW,OAAO,IAAS,yDAAyD;AACpF,IAAW,QAAQ,IAAQ,8CAA8C;AACzE,IAAW,UAAU,IAAM,2CAA2C;AACtE,IAAW,WAAW,IAAK,uDAAuD;AAClF,IAAe,OAAO,IAAS,gDAAgD;AAC/E,IAAe,MAAM,IAAU,sCAAsC;AACrE,IAAe,SAAS,IAAO,oCAAoC;AACnE,IAAe,OAAO,IAAS,gCAAgC;AAC/D,IAAe,UAAU,IAAM,sCAAsC;AACrE,IAAe,QAAQ,IAAQ,8CAA8C;AAC7E,IAAe,MAAM,IAAU,gDAAgD;AAC/E,IAAO,QAAQ,IAAQ,qCAAqC;AAC5D,IAAO,SAAS,IAAO,uCAAuC;AAC9D,IAAO,OAAO,IAAS,mDAAmD;AAC1E,IAAO,MAAM,IAAU,+CAA+C;AACtE,IAAO,MAAM,IAAU,4DAA4D;AACnF,IAAO,OAAO,IAAS,0DAA0D;AAEjF,8EAA8E,GAI9E,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,2CAA2C;AAE3C,IAAI,YAAY;AAChB,mBAAmB,GACnB,IAAI,YAAY;AAGhB,SAAS,QAAQ,CAAC;IAChB,OAAS,CAAC,AAAC,MAAM,KAAM,IAAI,IACnB,CAAC,AAAC,MAAM,IAAK,MAAM,IACnB,CAAC,CAAC,IAAI,MAAM,KAAK,CAAC,IAClB,CAAC,CAAC,IAAI,IAAI,KAAK,EAAE;AAC3B;AAGA,SAAS;IACP,IAAI,CAAC,IAAI,GAAG,GAAe,wBAAwB;IACnD,IAAI,CAAC,IAAI,GAAG,OAAgB,iCAAiC;IAC7D,IAAI,CAAC,IAAI,GAAG,GAAgB,4CAA4C;IACxE,IAAI,CAAC,QAAQ,GAAG,OAAY,+BAA+B;IAC3D,IAAI,CAAC,KAAK,GAAG,GAAe,4CAA4C;IACxE,IAAI,CAAC,IAAI,GAAG,GAAgB,6CAA6C;IACzE,IAAI,CAAC,KAAK,GAAG,GAAe,iCAAiC;IAC7D,IAAI,CAAC,KAAK,GAAG,GAAe,kCAAkC;IAC9D,kBAAkB;IAClB,IAAI,CAAC,IAAI,GAAG,MAAgB,yCAAyC;IAErE,kBAAkB,GAClB,IAAI,CAAC,KAAK,GAAG,GAAe,uCAAuC;IACnE,IAAI,CAAC,KAAK,GAAG,GAAe,2CAA2C;IACvE,IAAI,CAAC,KAAK,GAAG,GAAe,6BAA6B;IACzD,IAAI,CAAC,KAAK,GAAG,GAAe,sBAAsB;IAClD,IAAI,CAAC,MAAM,GAAG,MAAc,uCAAuC;IAEnE,mBAAmB,GACnB,IAAI,CAAC,IAAI,GAAG,GAAgB,yBAAyB;IACrD,IAAI,CAAC,IAAI,GAAG,GAAgB,0BAA0B;IAEtD,uCAAuC,GACvC,IAAI,CAAC,MAAM,GAAG,GAAc,qCAAqC;IACjE,IAAI,CAAC,MAAM,GAAG,GAAc,qCAAqC;IAEjE,+BAA+B,GAC/B,IAAI,CAAC,KAAK,GAAG,GAAe,qBAAqB;IAEjD,iCAAiC,GACjC,IAAI,CAAC,OAAO,GAAG,MAAe,2CAA2C;IACzE,IAAI,CAAC,QAAQ,GAAG,MAAc,qCAAqC;IACnE,IAAI,CAAC,OAAO,GAAG,GAAa,0BAA0B;IACtD,IAAI,CAAC,QAAQ,GAAG,GAAY,2BAA2B;IAEvD,0BAA0B,GAC1B,IAAI,CAAC,KAAK,GAAG,GAAe,sCAAsC;IAClE,IAAI,CAAC,IAAI,GAAG,GAAgB,iCAAiC;IAC7D,IAAI,CAAC,KAAK,GAAG,GAAe,mCAAmC;IAC/D,IAAI,CAAC,IAAI,GAAG,GAAgB,oCAAoC;IAChE,IAAI,CAAC,IAAI,GAAG,MAAmB,mCAAmC;IAElE,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,sCAAsC;IACxE,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,qCAAqC;IAEvE;;;EAGA,GACA,yEAAyE;IACzE,IAAI,CAAC,MAAM,GAAG,MAAmB,wDAAwD;IACzF,IAAI,CAAC,OAAO,GAAG,MAAkB,kDAAkD;IACnF,IAAI,CAAC,IAAI,GAAG,GAAqB,4CAA4C;IAC7E,IAAI,CAAC,IAAI,GAAG,GAAqB,4CAA4C;IAC7E,IAAI,CAAC,GAAG,GAAG,GAAsB,2BAA2B;AAC9D;AAEA,SAAS,iBAAiB,IAAI;IAC5B,IAAI;IAEJ,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QAAE,OAAO;IAAgB;IACnD,QAAQ,KAAK,KAAK;IAClB,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,MAAM,KAAK,GAAG;IAC/C,KAAK,GAAG,GAAG,IAAI,QAAQ;IACvB,IAAI,MAAM,IAAI,EAAE;QACd,KAAK,KAAK,GAAG,MAAM,IAAI,GAAG;IAC5B;IACA,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG;IACb,MAAM,QAAQ,GAAG;IACjB,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG,KAAI,QAAQ;IACzB,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG;IACb,4DAA4D;IAC5D,MAAM,OAAO,GAAG,MAAM,MAAM,GAAG,IAAI,MAAM,KAAK,CAAC;IAC/C,MAAM,QAAQ,GAAG,MAAM,OAAO,GAAG,IAAI,MAAM,KAAK,CAAC;IAEjD,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG,CAAC;IACd,uCAAuC;IACvC,OAAO;AACT;AAEA,SAAS,aAAa,IAAI;IACxB,IAAI;IAEJ,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QAAE,OAAO;IAAgB;IACnD,QAAQ,KAAK,KAAK;IAClB,MAAM,KAAK,GAAG;IACd,MAAM,KAAK,GAAG;IACd,MAAM,KAAK,GAAG;IACd,OAAO,iBAAiB;AAE1B;AAEA,SAAS,cAAc,IAAI,EAAE,UAAU;IACrC,IAAI;IACJ,IAAI;IAEJ,iBAAiB,GACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QAAE,OAAO;IAAgB;IACnD,QAAQ,KAAK,KAAK;IAElB,kDAAkD,GAClD,IAAI,aAAa,GAAG;QAClB,OAAO;QACP,aAAa,CAAC;IAChB,OACK;QACH,OAAO,CAAC,cAAc,CAAC,IAAI;QAC3B,IAAI,aAAa,IAAI;YACnB,cAAc;QAChB;IACF;IAEA,uDAAuD,GACvD,IAAI,cAAc,CAAC,aAAa,KAAK,aAAa,EAAE,GAAG;QACrD,OAAO;IACT;IACA,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,YAAY;QACvD,MAAM,MAAM,GAAG;IACjB;IAEA,yCAAyC,GACzC,MAAM,IAAI,GAAG;IACb,MAAM,KAAK,GAAG;IACd,OAAO,aAAa;AACtB;AAEA,SAAS,aAAa,IAAI,EAAE,UAAU;IACpC,IAAI;IACJ,IAAI;IAEJ,IAAI,CAAC,MAAM;QAAE,OAAO;IAAgB;IACpC,qEAAqE;IAErE,QAAQ,IAAI;IAEZ,2CAA2C;IAC3C,2CAA2C;IAC3C,KAAK,KAAK,GAAG;IACb,MAAM,MAAM,GAAG,KAAI,QAAQ;IAC3B,MAAM,cAAc,MAAM;IAC1B,IAAI,QAAQ,MAAM;QAChB,KAAK,KAAK,GAAG,KAAI,QAAQ;IAC3B;IACA,OAAO;AACT;AAEA,SAAS,YAAY,IAAI;IACvB,OAAO,aAAa,MAAM;AAC5B;AAGA;;;;;;;;;CASC,GACD,IAAI,SAAS;AAEb,IAAI,QAAQ,SAAS,qDAAqD;AAE1E,SAAS,YAAY,KAAK;IACxB,qEAAqE,GACrE,IAAI,QAAQ;QACV,IAAI;QAEJ,SAAS,IAAI,MAAM,KAAK,CAAC;QACzB,UAAU,IAAI,MAAM,KAAK,CAAC;QAE1B,wBAAwB,GACxB,MAAM;QACN,MAAO,MAAM,IAAK;YAAE,MAAM,IAAI,CAAC,MAAM,GAAG;QAAG;QAC3C,MAAO,MAAM,IAAK;YAAE,MAAM,IAAI,CAAC,MAAM,GAAG;QAAG;QAC3C,MAAO,MAAM,IAAK;YAAE,MAAM,IAAI,CAAC,MAAM,GAAG;QAAG;QAC3C,MAAO,MAAM,IAAK;YAAE,MAAM,IAAI,CAAC,MAAM,GAAG;QAAG;QAE3C,cAAc,MAAO,MAAM,IAAI,EAAE,GAAG,KAAK,QAAU,GAAG,MAAM,IAAI,EAAE;YAAE,MAAM;QAAE;QAE5E,kBAAkB,GAClB,MAAM;QACN,MAAO,MAAM,GAAI;YAAE,MAAM,IAAI,CAAC,MAAM,GAAG;QAAG;QAE1C,cAAc,OAAO,MAAM,IAAI,EAAE,GAAG,IAAM,SAAS,GAAG,MAAM,IAAI,EAAE;YAAE,MAAM;QAAE;QAE5E,qBAAqB,GACrB,SAAS;IACX;IAEA,MAAM,OAAO,GAAG;IAChB,MAAM,OAAO,GAAG;IAChB,MAAM,QAAQ,GAAG;IACjB,MAAM,QAAQ,GAAG;AACnB;AAGA;;;;;;;;;;;;;CAaC,GACD,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;IACxC,IAAI;IACJ,IAAI,QAAQ,KAAK,KAAK;IAEtB,iEAAiE,GACjE,IAAI,MAAM,MAAM,KAAK,MAAM;QACzB,MAAM,KAAK,GAAG,KAAK,MAAM,KAAK;QAC9B,MAAM,KAAK,GAAG;QACd,MAAM,KAAK,GAAG;QAEd,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,MAAM,KAAK;IAC3C;IAEA,mEAAmE,GACnE,IAAI,QAAQ,MAAM,KAAK,EAAE;QACvB,MAAM,QAAQ,CAAC,MAAM,MAAM,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,MAAM,KAAK,EAAE;QAClE,MAAM,KAAK,GAAG;QACd,MAAM,KAAK,GAAG,MAAM,KAAK;IAC3B,OACK;QACH,OAAO,MAAM,KAAK,GAAG,MAAM,KAAK;QAChC,IAAI,OAAO,MAAM;YACf,OAAO;QACT;QACA,0DAA0D;QAC1D,MAAM,QAAQ,CAAC,MAAM,MAAM,EAAE,KAAK,MAAM,MAAM,MAAM,MAAM,KAAK;QAC/D,QAAQ;QACR,IAAI,MAAM;YACR,2CAA2C;YAC3C,MAAM,QAAQ,CAAC,MAAM,MAAM,EAAE,KAAK,MAAM,MAAM,MAAM;YACpD,MAAM,KAAK,GAAG;YACd,MAAM,KAAK,GAAG,MAAM,KAAK;QAC3B,OACK;YACH,MAAM,KAAK,IAAI;YACf,IAAI,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE;gBAAE,MAAM,KAAK,GAAG;YAAG;YACpD,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,EAAE;gBAAE,MAAM,KAAK,IAAI;YAAM;QACxD;IACF;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,IAAI,EAAE,KAAK;IAC1B,IAAI;IACJ,IAAI,OAAO,QAAiB,uBAAuB;IACnD,IAAI,MAAwB,oBAAoB;IAChD,IAAI,KAAwB,qBAAqB;IACjD,IAAI,MAAM,MAAkB,8BAA8B;IAC1D,IAAI,MAAwB,cAAc;IAC1C,IAAI,MAAwB,sBAAsB;IAClD,IAAI,KAAK,MAAmB,4CAA4C;IACxE,IAAI,MAAwB,2CAA2C;IACvE,IAAI,MAAwB,kCAAkC;IAC9D,IAAI;IACJ,IAAI,OAAO,GAAiB,gCAAgC;IAC5D,IAAI,WAAW,SAAS,UAAU,0CAA0C;IAC5E,sDAAsD;IACtD,IAAI,WAAW,SAAS,UAAU,0CAA0C;IAC5E,IAAI,KAAwB,4CAA4C;IACxE,IAAI,KAAwB,eAAe;IAC3C,IAAI,OAAO,IAAI,MAAM,IAAI,CAAC,IAAO,0CAA0C;IAC3E,IAAI;IAEJ,IAAI,GAAG,8BAA8B;IAErC,IAAI,QAAQ,+BAA+B,GACzC;QAAE;QAAI;QAAI;QAAI;QAAG;QAAG;QAAG;QAAG;QAAG;QAAI;QAAG;QAAI;QAAG;QAAI;QAAG;QAAI;QAAG;QAAI;QAAG;KAAI;IAGtE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,MAAM,IACnC,CAAC,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,GAAI;QACxC,OAAO;IACT;IAEA,QAAQ,KAAK,KAAK;IAClB,IAAI,MAAM,IAAI,KAAK,MAAM;QAAE,MAAM,IAAI,GAAG;IAAQ,EAAK,cAAc;IAGnE,gBAAgB;IAChB,MAAM,KAAK,QAAQ;IACnB,SAAS,KAAK,MAAM;IACpB,OAAO,KAAK,SAAS;IACrB,OAAO,KAAK,OAAO;IACnB,QAAQ,KAAK,KAAK;IAClB,OAAO,KAAK,QAAQ;IACpB,OAAO,MAAM,IAAI;IACjB,OAAO,MAAM,IAAI;IACjB,KAAK;IAEL,MAAM;IACN,OAAO;IACP,MAAM;IAEN,WACA,OAAS;QACP,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,IAAI,MAAM,IAAI,KAAK,GAAG;oBACpB,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,mBAAmB;gBACnB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,IAAI,AAAC,MAAM,IAAI,GAAG,KAAM,SAAS,QAAQ;oBACvC,MAAM,KAAK,GAAG,EAAC,sBAAsB;oBACrC,8BAA8B;oBAC9B,IAAI,CAAC,EAAE,GAAG,OAAO;oBACjB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,IAAK;oBACzB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,MAAM,GAAG;oBAC1C,OAAO;oBAEP,iBAAiB;oBACjB,OAAO;oBACP,OAAO;oBACP,OAAO;oBACP,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,MAAM,KAAK,GAAG,GAAa,sBAAsB;gBACjD,IAAI,MAAM,IAAI,EAAE;oBACd,MAAM,IAAI,CAAC,IAAI,GAAG;gBACpB;gBACA,IAAI,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,KAAO,gCAAgC,GACzD,CAAC,CAAC,CAAC,OAAO,IAAI,KAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI;oBACtD,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,CAAC,OAAO,IAAI,MAAiB,YAAY;oBAC3C,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,MAAM,CAAC,OAAO,IAAI,IAAe;gBACjC,IAAI,MAAM,KAAK,KAAK,GAAG;oBACrB,MAAM,KAAK,GAAG;gBAChB,OACK,IAAI,MAAM,MAAM,KAAK,EAAE;oBAC1B,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,MAAM,IAAI,GAAG,KAAK;gBAClB,kDAAkD;gBAClD,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG,EAAC,wBAAwB;gBACpD,MAAM,IAAI,GAAG,OAAO,QAAQ,SAAS;gBACrC,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBAEP;YACF,KAAK;gBACH,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,MAAM,KAAK,GAAG;gBACd,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,YAAY;oBACvC,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,MAAM,IAAI,EAAE;oBACd,MAAM,IAAI,CAAC,IAAI,GAAI,AAAC,QAAQ,IAAK;gBACnC;gBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,8BAA8B;oBAC9B,IAAI,CAAC,EAAE,GAAG,OAAO;oBACjB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,IAAK;oBACzB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,MAAM,GAAG;gBAC1C,OAAO;gBACT;gBACA,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,IAAI,MAAM,IAAI,EAAE;oBACd,MAAM,IAAI,CAAC,IAAI,GAAG;gBACpB;gBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,6BAA6B;oBAC7B,IAAI,CAAC,EAAE,GAAG,OAAO;oBACjB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,IAAK;oBACzB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,KAAM;oBAC1B,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,KAAM;oBAC1B,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,MAAM,GAAG;gBAC1C,KAAK;gBACP;gBACA,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,IAAI,MAAM,IAAI,EAAE;oBACd,MAAM,IAAI,CAAC,MAAM,GAAI,OAAO;oBAC5B,MAAM,IAAI,CAAC,EAAE,GAAI,QAAQ;gBAC3B;gBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,8BAA8B;oBAC9B,IAAI,CAAC,EAAE,GAAG,OAAO;oBACjB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,IAAK;oBACzB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,MAAM,GAAG;gBAC1C,OAAO;gBACT;gBACA,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,sBAAsB;oBACtB,MAAO,OAAO,GAAI;wBAChB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,MAAM,MAAM,GAAG;oBACf,IAAI,MAAM,IAAI,EAAE;wBACd,MAAM,IAAI,CAAC,SAAS,GAAG;oBACzB;oBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;wBACxB,8BAA8B;wBAC9B,IAAI,CAAC,EAAE,GAAG,OAAO;wBACjB,IAAI,CAAC,EAAE,GAAG,AAAC,SAAS,IAAK;wBACzB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,MAAM,GAAG;oBAC1C,OAAO;oBACT;oBACA,iBAAiB;oBACjB,OAAO;oBACP,OAAO;gBACP,OAAO;gBACT,OACK,IAAI,MAAM,IAAI,EAAE;oBACnB,MAAM,IAAI,CAAC,KAAK,GAAG,KAAI,QAAQ;gBACjC;gBACA,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,OAAO,MAAM,MAAM;oBACnB,IAAI,OAAO,MAAM;wBAAE,OAAO;oBAAM;oBAChC,IAAI,MAAM;wBACR,IAAI,MAAM,IAAI,EAAE;4BACd,MAAM,MAAM,IAAI,CAAC,SAAS,GAAG,MAAM,MAAM;4BACzC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE;gCACrB,yDAAyD;gCACzD,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,MAAM,IAAI,CAAC,SAAS;4BACnD;4BACA,MAAM,QAAQ,CACZ,MAAM,IAAI,CAAC,KAAK,EAChB,OACA,MACA,wCAAwC;4BACxC,sCAAsC;4BACtC,MACA,wEAAwE,GACxE;wBAEF,uCAAuC;wBACvC,6CAA6C;wBAC7C,6CAA6C;wBAC/C;wBACA,IAAI,MAAM,KAAK,GAAG,QAAQ;4BACxB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM;wBAChD;wBACA,QAAQ;wBACR,QAAQ;wBACR,MAAM,MAAM,IAAI;oBAClB;oBACA,IAAI,MAAM,MAAM,EAAE;wBAAE,MAAM;oBAAW;gBACvC;gBACA,MAAM,MAAM,GAAG;gBACf,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC,OAAO;oBACP,GAAG;wBACD,sBAAsB;wBACtB,MAAM,KAAK,CAAC,OAAO,OAAO;wBAC1B,qEAAqE,GACrE,IAAI,MAAM,IAAI,IAAI,OACb,MAAM,MAAM,GAAG,MAAM,qBAAqB,KAAK;4BAClD,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,YAAY,CAAC;wBACzC;oBACF,QAAS,OAAO,OAAO,KAAM;oBAE7B,IAAI,MAAM,KAAK,GAAG,QAAQ;wBACxB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM;oBAChD;oBACA,QAAQ;oBACR,QAAQ;oBACR,IAAI,KAAK;wBAAE,MAAM;oBAAW;gBAC9B,OACK,IAAI,MAAM,IAAI,EAAE;oBACnB,MAAM,IAAI,CAAC,IAAI,GAAG;gBACpB;gBACA,MAAM,MAAM,GAAG;gBACf,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC,OAAO;oBACP,GAAG;wBACD,MAAM,KAAK,CAAC,OAAO,OAAO;wBAC1B,qEAAqE,GACrE,IAAI,MAAM,IAAI,IAAI,OACb,MAAM,MAAM,GAAG,MAAM,qBAAqB,KAAK;4BAClD,MAAM,IAAI,CAAC,OAAO,IAAI,OAAO,YAAY,CAAC;wBAC5C;oBACF,QAAS,OAAO,OAAO,KAAM;oBAC7B,IAAI,MAAM,KAAK,GAAG,QAAQ;wBACxB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM;oBAChD;oBACA,QAAQ;oBACR,QAAQ;oBACR,IAAI,KAAK;wBAAE,MAAM;oBAAW;gBAC9B,OACK,IAAI,MAAM,IAAI,EAAE;oBACnB,MAAM,IAAI,CAAC,OAAO,GAAG;gBACvB;gBACA,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,GAAG,QAAQ;oBACxB,sBAAsB;oBACtB,MAAO,OAAO,GAAI;wBAChB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,IAAI,SAAS,CAAC,MAAM,KAAK,GAAG,MAAM,GAAG;wBACnC,KAAK,GAAG,GAAG;wBACX,MAAM,IAAI,GAAG;wBACb;oBACF;oBACA,iBAAiB;oBACjB,OAAO;oBACP,OAAO;gBACP,OAAO;gBACT;gBACA,IAAI,MAAM,IAAI,EAAE;oBACd,MAAM,IAAI,CAAC,IAAI,GAAI,AAAC,MAAM,KAAK,IAAI,IAAK;oBACxC,MAAM,IAAI,CAAC,IAAI,GAAG;gBACpB;gBACA,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG;gBAC3B,MAAM,IAAI,GAAG;gBACb;YACF,KAAK;gBACH,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG,QAAQ;gBACnC,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,QAAQ,KAAK,GAAG;oBACxB,mBAAmB;oBACnB,KAAK,QAAQ,GAAG;oBAChB,KAAK,SAAS,GAAG;oBACjB,KAAK,OAAO,GAAG;oBACf,KAAK,QAAQ,GAAG;oBAChB,MAAM,IAAI,GAAG;oBACb,MAAM,IAAI,GAAG;oBACb,KAAK;oBACL,OAAO;gBACT;gBACA,KAAK,KAAK,GAAG,MAAM,KAAK,GAAG,EAAC,wBAAwB;gBACpD,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,UAAU,WAAW,UAAU,SAAS;oBAAE,MAAM;gBAAW;YAC/D,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,IAAI,EAAE;oBACd,sBAAsB;oBACtB,UAAU,OAAO;oBACjB,QAAQ,OAAO;oBACf,OAAO;oBACP,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,qBAAqB;gBACrB,MAAO,OAAO,EAAG;oBACf,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,MAAM,IAAI,GAAI,OAAO,KAAK,SAAS;gBACnC,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBACR,OAAO;gBAEP,OAAS,OAAO;oBACd,KAAK;wBAA+B,gBAAgB,GAClD,kDAAkD;wBAClD,wCAAwC;wBACxC,MAAM,IAAI,GAAG;wBACb;oBACF,KAAK;wBAA+B,eAAe,GACjD,YAAY;wBACZ,uDAAuD;wBACvD,wCAAwC;wBACxC,MAAM,IAAI,GAAG,MAAkB,gBAAgB;wBAC/C,IAAI,UAAU,SAAS;4BACrB,uBAAuB;4BACvB,UAAU;4BACV,QAAQ;4BAER,MAAM;wBACR;wBACA;oBACF,KAAK;wBAA+B,iBAAiB,GACnD,yDAAyD;wBACzD,wCAAwC;wBACxC,MAAM,IAAI,GAAG;wBACb;oBACF,KAAK;wBACH,KAAK,GAAG,GAAG;wBACX,MAAM,IAAI,GAAG;gBACjB;gBACA,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBAER;YACF,KAAK;gBACH,gDAAgD;gBAChD,UAAU,OAAO;gBACjB,QAAQ,OAAO;gBACf,OAAO;gBACP,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,IAAI,CAAC,OAAO,MAAM,MAAM,CAAC,AAAC,SAAS,KAAM,MAAM,GAAG;oBAChD,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,MAAM,MAAM,GAAG,OAAO;gBACtB,sDAAsD;gBACtD,yBAAyB;gBACzB,iBAAiB;gBACjB,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM,IAAI,GAAG;gBACb,IAAI,UAAU,SAAS;oBAAE,MAAM;gBAAW;YAC1C,iBAAiB,GACnB,KAAK;gBACH,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,OAAO,MAAM,MAAM;gBACnB,IAAI,MAAM;oBACR,IAAI,OAAO,MAAM;wBAAE,OAAO;oBAAM;oBAChC,IAAI,OAAO,MAAM;wBAAE,OAAO;oBAAM;oBAChC,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC,mCAAmC;oBACnC,MAAM,QAAQ,CAAC,QAAQ,OAAO,MAAM,MAAM;oBAC1C,OAAO;oBACP,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR,OAAO;oBACP,MAAM,MAAM,IAAI;oBAChB;gBACF;gBACA,kDAAkD;gBAClD,MAAM,IAAI,GAAG;gBACb;YACF,KAAK;gBACH,sBAAsB;gBACtB,MAAO,OAAO,GAAI;oBAChB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACV;gBACA,OAAO;gBACP,MAAM,IAAI,GAAG,CAAC,OAAO,IAAI,IAAe;gBACxC,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,OAAO,IAAI,IAAe;gBACzC,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,MAAM,KAAK,GAAG,CAAC,OAAO,IAAI,IAAe;gBACzC,uBAAuB;gBACvB,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACf,8BAA8B;gBACtB,IAAI,MAAM,IAAI,GAAG,OAAO,MAAM,KAAK,GAAG,IAAI;oBACxC,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACR,QAAQ;gBACA,sDAAsD;gBACtD,MAAM,IAAI,GAAG;gBACb,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,MAAO,MAAM,IAAI,GAAG,MAAM,KAAK,CAAE;oBAC/B,kBAAkB;oBAClB,MAAO,OAAO,EAAG;wBACf,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC,GAAI,OAAO,MAAM,UAAU;oBAC1D,uBAAuB;oBACvB,UAAU;oBACV,QAAQ;gBACR,OAAO;gBACT;gBACA,MAAO,MAAM,IAAI,GAAG,GAAI;oBACtB,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;gBACpC;gBACA,6EAA6E;gBAC7E,2BAA2B;gBAC3B,6BAA6B;gBAC7B,8BAA8B;gBAC9B,MAAM,OAAO,GAAG,MAAM,MAAM;gBAC5B,MAAM,OAAO,GAAG;gBAEhB,OAAO;oBAAE,MAAM,MAAM,OAAO;gBAAC;gBAC7B,MAAM,cAAc,OAAO,MAAM,IAAI,EAAE,GAAG,IAAI,MAAM,OAAO,EAAE,GAAG,MAAM,IAAI,EAAE;gBAC5E,MAAM,OAAO,GAAG,KAAK,IAAI;gBAEzB,IAAI,KAAK;oBACP,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,uDAAuD;gBACvD,MAAM,IAAI,GAAG;gBACb,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,MAAO,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG,MAAM,KAAK,CAAE;oBAC5C,OAAS;wBACP,OAAO,MAAM,OAAO,CAAC,OAAQ,CAAC,KAAK,MAAM,OAAO,IAAI,EAAG,EAAC,qBAAqB;wBAC7E,YAAY,SAAS;wBACrB,UAAU,AAAC,SAAS,KAAM;wBAC1B,WAAW,OAAO;wBAElB,IAAI,AAAC,aAAc,MAAM;4BAAE;wBAAO;wBAClC,sBAAsB;wBACtB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACR,OAAO;oBACT;oBACA,IAAI,WAAW,IAAI;wBACjB,+BAA+B;wBAC/B,UAAU;wBACV,QAAQ;wBACR,OAAO;wBACP,MAAM,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG;oBAC7B,OACK;wBACH,IAAI,aAAa,IAAI;4BACnB,8BAA8B;4BAC9B,IAAI,YAAY;4BAChB,MAAO,OAAO,EAAG;gCACf,IAAI,SAAS,GAAG;oCAAE,MAAM;gCAAW;gCACnC;gCACA,QAAQ,KAAK,CAAC,OAAO,IAAI;gCACzB,QAAQ;4BACV;4BACA,OAAO;4BACP,+BAA+B;4BAC/B,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,IAAI,MAAM,IAAI,KAAK,GAAG;gCACpB,KAAK,GAAG,GAAG;gCACX,MAAM,IAAI,GAAG;gCACb;4BACF;4BACA,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE;4BAChC,OAAO,IAAI,CAAC,OAAO,IAAI,GAAE,UAAU;4BACnC,uBAAuB;4BACvB,UAAU;4BACV,QAAQ;wBACR,OAAO;wBACT,OACK,IAAI,aAAa,IAAI;4BACxB,8BAA8B;4BAC9B,IAAI,YAAY;4BAChB,MAAO,OAAO,EAAG;gCACf,IAAI,SAAS,GAAG;oCAAE,MAAM;gCAAW;gCACnC;gCACA,QAAQ,KAAK,CAAC,OAAO,IAAI;gCACzB,QAAQ;4BACV;4BACA,OAAO;4BACP,+BAA+B;4BAC/B,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,MAAM;4BACN,OAAO,IAAI,CAAC,OAAO,IAAI,GAAE,UAAU;4BACnC,uBAAuB;4BACvB,UAAU;4BACV,QAAQ;wBACR,OAAO;wBACT,OACK;4BACH,8BAA8B;4BAC9B,IAAI,YAAY;4BAChB,MAAO,OAAO,EAAG;gCACf,IAAI,SAAS,GAAG;oCAAE,MAAM;gCAAW;gCACnC;gCACA,QAAQ,KAAK,CAAC,OAAO,IAAI;gCACzB,QAAQ;4BACV;4BACA,OAAO;4BACP,+BAA+B;4BAC/B,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,MAAM;4BACN,OAAO,KAAK,CAAC,OAAO,IAAI,GAAE,UAAU;4BACpC,uBAAuB;4BACvB,UAAU;4BACV,QAAQ;wBACR,OAAO;wBACT;wBACA,IAAI,MAAM,IAAI,GAAG,OAAO,MAAM,IAAI,GAAG,MAAM,KAAK,EAAE;4BAChD,KAAK,GAAG,GAAG;4BACX,MAAM,IAAI,GAAG;4BACb;wBACF;wBACA,MAAO,OAAQ;4BACb,MAAM,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG;wBAC7B;oBACF;gBACF;gBAEA,gCAAgC,GAChC,IAAI,MAAM,IAAI,KAAK,KAAK;oBAAE;gBAAO;gBAEjC,iDAAiD,GACjD,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,GAAG;oBACzB,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBAEA;;yEAEiE,GACjE,MAAM,OAAO,GAAG;gBAEhB,OAAO;oBAAE,MAAM,MAAM,OAAO;gBAAC;gBAC7B,MAAM,cAAc,MAAM,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,IAAI,EAAE;gBACnF,6EAA6E;gBAC7E,uCAAuC;gBACvC,MAAM,OAAO,GAAG,KAAK,IAAI;gBACzB,8BAA8B;gBAE9B,IAAI,KAAK;oBACP,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBAEA,MAAM,QAAQ,GAAG;gBACjB,mCAAmC;gBACnC,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,MAAM,OAAO;gBAC9B,OAAO;oBAAE,MAAM,MAAM,QAAQ;gBAAC;gBAC9B,MAAM,cAAc,OAAO,MAAM,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,IAAI,EAAE;gBAC/F,6EAA6E;gBAC7E,uCAAuC;gBACvC,MAAM,QAAQ,GAAG,KAAK,IAAI;gBAC1B,+BAA+B;gBAE/B,IAAI,KAAK;oBACP,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,gDAAgD;gBAChD,MAAM,IAAI,GAAG;gBACb,IAAI,UAAU,SAAS;oBAAE,MAAM;gBAAW;YAC1C,iBAAiB,GACnB,KAAK;gBACH,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,QAAQ,KAAK,QAAQ,KAAK;oBAC5B,mBAAmB;oBACnB,KAAK,QAAQ,GAAG;oBAChB,KAAK,SAAS,GAAG;oBACjB,KAAK,OAAO,GAAG;oBACf,KAAK,QAAQ,GAAG;oBAChB,MAAM,IAAI,GAAG;oBACb,MAAM,IAAI,GAAG;oBACb,KAAK;oBACL,aAAa,MAAM;oBACnB,gBAAgB;oBAChB,MAAM,KAAK,QAAQ;oBACnB,SAAS,KAAK,MAAM;oBACpB,OAAO,KAAK,SAAS;oBACrB,OAAO,KAAK,OAAO;oBACnB,QAAQ,KAAK,KAAK;oBAClB,OAAO,KAAK,QAAQ;oBACpB,OAAO,MAAM,IAAI;oBACjB,OAAO,MAAM,IAAI;oBACjB,KAAK;oBAEL,IAAI,MAAM,IAAI,KAAK,MAAM;wBACvB,MAAM,IAAI,GAAG,CAAC;oBAChB;oBACA;gBACF;gBACA,MAAM,IAAI,GAAG;gBACb,OAAS;oBACP,OAAO,MAAM,OAAO,CAAC,OAAQ,CAAC,KAAK,MAAM,OAAO,IAAI,EAAG,EAAG,qBAAqB;oBAC/E,YAAY,SAAS;oBACrB,UAAU,AAAC,SAAS,KAAM;oBAC1B,WAAW,OAAO;oBAElB,IAAI,aAAa,MAAM;wBAAE;oBAAO;oBAChC,sBAAsB;oBACtB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACR,OAAO;gBACT;gBACA,IAAI,WAAW,CAAC,UAAU,IAAI,MAAM,GAAG;oBACrC,YAAY;oBACZ,UAAU;oBACV,WAAW;oBACX,OAAS;wBACP,OAAO,MAAM,OAAO,CAAC,WACb,CAAC,CAAC,OAAQ,CAAC,KAAM,YAAY,OAAQ,IAAI,CAAE,KAAkC,SAAS,EAAE;wBAChG,YAAY,SAAS;wBACrB,UAAU,AAAC,SAAS,KAAM;wBAC1B,WAAW,OAAO;wBAElB,IAAI,AAAC,YAAY,aAAc,MAAM;4BAAE;wBAAO;wBAC9C,sBAAsB;wBACtB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACR,OAAO;oBACT;oBACA,+BAA+B;oBAC/B,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,MAAM,IAAI,IAAI;gBAChB;gBACA,+BAA+B;gBAC/B,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,MAAM,IAAI,IAAI;gBACd,MAAM,MAAM,GAAG;gBACf,IAAI,YAAY,GAAG;oBACjB,wDAAwD;oBACxD,6CAA6C;oBAC7C,0DAA0D;oBAC1D,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,UAAU,IAAI;oBAChB,uDAAuD;oBACvD,MAAM,IAAI,GAAG,CAAC;oBACd,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,UAAU,IAAI;oBAChB,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,MAAM,KAAK,GAAG,UAAU;gBACxB,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,EAAE;oBACf,4BAA4B;oBAC5B,IAAI,MAAM,KAAK;oBACf,MAAO,OAAO,EAAG;wBACf,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,MAAM,MAAM,IAAI,OAAQ,CAAC,KAAK,MAAM,KAAK,IAAI,EAAE,mBAAmB;oBAClE,iCAAiC;oBACjC,UAAU,MAAM,KAAK;oBACrB,QAAQ,MAAM,KAAK;oBACnB,OAAO;oBACP,MAAM,IAAI,IAAI,MAAM,KAAK;gBAC3B;gBACA,kEAAkE;gBAClE,MAAM,GAAG,GAAG,MAAM,MAAM;gBACxB,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,OAAS;oBACP,OAAO,MAAM,QAAQ,CAAC,OAAQ,CAAC,KAAK,MAAM,QAAQ,IAAI,EAAG,EAAC,sBAAsB;oBAChF,YAAY,SAAS;oBACrB,UAAU,AAAC,SAAS,KAAM;oBAC1B,WAAW,OAAO;oBAElB,IAAI,AAAC,aAAc,MAAM;wBAAE;oBAAO;oBAClC,sBAAsB;oBACtB,IAAI,SAAS,GAAG;wBAAE,MAAM;oBAAW;oBACnC;oBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;oBACzB,QAAQ;gBACR,OAAO;gBACT;gBACA,IAAI,CAAC,UAAU,IAAI,MAAM,GAAG;oBAC1B,YAAY;oBACZ,UAAU;oBACV,WAAW;oBACX,OAAS;wBACP,OAAO,MAAM,QAAQ,CAAC,WACd,CAAC,CAAC,OAAQ,CAAC,KAAM,YAAY,OAAQ,IAAI,CAAE,KAAkC,SAAS,EAAE;wBAChG,YAAY,SAAS;wBACrB,UAAU,AAAC,SAAS,KAAM;wBAC1B,WAAW,OAAO;wBAElB,IAAI,AAAC,YAAY,aAAc,MAAM;4BAAE;wBAAO;wBAC9C,sBAAsB;wBACtB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACR,OAAO;oBACT;oBACA,+BAA+B;oBAC/B,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,MAAM,IAAI,IAAI;gBAChB;gBACA,+BAA+B;gBAC/B,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,MAAM,IAAI,IAAI;gBACd,IAAI,UAAU,IAAI;oBAChB,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,MAAM,MAAM,GAAG;gBACf,MAAM,KAAK,GAAG,AAAC,UAAW;gBAC1B,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,KAAK,EAAE;oBACf,4BAA4B;oBAC5B,IAAI,MAAM,KAAK;oBACf,MAAO,OAAO,EAAG;wBACf,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,MAAM,MAAM,IAAI,OAAQ,CAAC,KAAK,MAAM,KAAK,IAAI,EAAE,mBAAmB;oBAClE,iCAAiC;oBACjC,UAAU,MAAM,KAAK;oBACrB,QAAQ,MAAM,KAAK;oBACnB,OAAO;oBACP,MAAM,IAAI,IAAI,MAAM,KAAK;gBAC3B;gBACR,uBAAuB;gBACf,IAAI,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE;oBAC7B,KAAK,GAAG,GAAG;oBACX,MAAM,IAAI,GAAG;oBACb;gBACF;gBACR,QAAQ;gBACA,oEAAoE;gBACpE,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,SAAS,GAAG;oBAAE,MAAM;gBAAW;gBACnC,OAAO,OAAO;gBACd,IAAI,MAAM,MAAM,GAAG,MAAM;oBACvB,OAAO,MAAM,MAAM,GAAG;oBACtB,IAAI,OAAO,MAAM,KAAK,EAAE;wBACtB,IAAI,MAAM,IAAI,EAAE;4BACd,KAAK,GAAG,GAAG;4BACX,MAAM,IAAI,GAAG;4BACb;wBACF;oBACZ,+CAA+C;oBAC/C,2CAA2C;oBAC3C,mDAAmD;oBACnD,mDAAmD;oBACnD,gCAAgC;oBAChC,6DAA6D;oBAC7D,6CAA6C;oBAC7C,yBAAyB;oBACzB,iCAAiC;oBACjC,gBAAgB;oBAChB,gCAAgC;oBAChC,6BAA6B;oBAC7B,yDAAyD;oBACzD,kBAAkB;oBAClB,QAAQ;oBACE;oBACA,IAAI,OAAO,MAAM,KAAK,EAAE;wBACtB,QAAQ,MAAM,KAAK;wBACnB,OAAO,MAAM,KAAK,GAAG;oBACvB,OACK;wBACH,OAAO,MAAM,KAAK,GAAG;oBACvB;oBACA,IAAI,OAAO,MAAM,MAAM,EAAE;wBAAE,OAAO,MAAM,MAAM;oBAAE;oBAChD,cAAc,MAAM,MAAM;gBAC5B,OACK;oBACH,cAAc;oBACd,OAAO,MAAM,MAAM,MAAM;oBACzB,OAAO,MAAM,MAAM;gBACrB;gBACA,IAAI,OAAO,MAAM;oBAAE,OAAO;gBAAM;gBAChC,QAAQ;gBACR,MAAM,MAAM,IAAI;gBAChB,GAAG;oBACD,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,OAAO;gBACrC,QAAS,EAAE,KAAM;gBACjB,IAAI,MAAM,MAAM,KAAK,GAAG;oBAAE,MAAM,IAAI,GAAG;gBAAK;gBAC5C;YACF,KAAK;gBACH,IAAI,SAAS,GAAG;oBAAE,MAAM;gBAAW;gBACnC,MAAM,CAAC,MAAM,GAAG,MAAM,MAAM;gBAC5B;gBACA,MAAM,IAAI,GAAG;gBACb;YACF,KAAK;gBACH,IAAI,MAAM,IAAI,EAAE;oBACd,mBAAmB;oBACnB,MAAO,OAAO,GAAI;wBAChB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,4DAA4D;wBAC5D,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,QAAQ;oBACR,KAAK,SAAS,IAAI;oBAClB,MAAM,KAAK,IAAI;oBACf,IAAI,MAAM;wBACR,KAAK,KAAK,GAAG,MAAM,KAAK,GAEnB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,QAAQ,MAAM,MAAM,QAAQ,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,MAAM;oBAE7G;oBACA,OAAO;oBACP,oEAAoE;oBACpE,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,QAAQ,KAAK,MAAM,MAAM,KAAK,EAAE;wBACxD,KAAK,GAAG,GAAG;wBACX,MAAM,IAAI,GAAG;wBACb;oBACF;oBACA,iBAAiB;oBACjB,OAAO;oBACP,OAAO;gBACP,OAAO;gBACP,yDAAyD;gBAC3D;gBACA,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK,EAAE;oBAC7B,mBAAmB;oBACnB,MAAO,OAAO,GAAI;wBAChB,IAAI,SAAS,GAAG;4BAAE,MAAM;wBAAW;wBACnC;wBACA,QAAQ,KAAK,CAAC,OAAO,IAAI;wBACzB,QAAQ;oBACV;oBACA,OAAO;oBACP,IAAI,SAAS,CAAC,MAAM,KAAK,GAAG,UAAU,GAAG;wBACvC,KAAK,GAAG,GAAG;wBACX,MAAM,IAAI,GAAG;wBACb;oBACF;oBACA,iBAAiB;oBACjB,OAAO;oBACP,OAAO;gBACP,OAAO;gBACP,0DAA0D;gBAC5D;gBACA,MAAM,IAAI,GAAG;YACb,iBAAiB,GACnB,KAAK;gBACH,MAAM;gBACN,MAAM;YACR,KAAK;gBACH,MAAM;gBACN,MAAM;YACR,KAAK;gBACH,OAAO;YACT,KAAK;YACH,iBAAiB,GACnB;gBACE,OAAO;QACX;IACF;IAEA,uFAAuF;IAEvF;;;;;GAKC,GAED,mBAAmB;IACnB,KAAK,QAAQ,GAAG;IAChB,KAAK,SAAS,GAAG;IACjB,KAAK,OAAO,GAAG;IACf,KAAK,QAAQ,GAAG;IAChB,MAAM,IAAI,GAAG;IACb,MAAM,IAAI,GAAG;IACb,KAAK;IAEL,IAAI,MAAM,KAAK,IAAK,SAAS,KAAK,SAAS,IAAI,MAAM,IAAI,GAAG,OACxC,CAAC,MAAM,IAAI,GAAG,SAAS,UAAU,QAAQ,GAAI;QAC/D,IAAI,aAAa,MAAM,KAAK,MAAM,EAAE,KAAK,QAAQ,EAAE,OAAO,KAAK,SAAS,GAAG;;QAG3E;IACF;IACA,OAAO,KAAK,QAAQ;IACpB,QAAQ,KAAK,SAAS;IACtB,KAAK,QAAQ,IAAI;IACjB,KAAK,SAAS,IAAI;IAClB,MAAM,KAAK,IAAI;IACf,IAAI,MAAM,IAAI,IAAI,MAAM;QACtB,KAAK,KAAK,GAAG,MAAM,KAAK,GACrB,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,QAAQ,GAAG,QAAQ,QAAQ,MAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,QAAQ,GAAG;IAC/H;IACA,KAAK,SAAS,GAAG,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,KAAK,CAAC,IAChC,CAAC,MAAM,IAAI,KAAK,OAAO,MAAM,CAAC,IAC9B,CAAC,MAAM,IAAI,KAAK,QAAQ,MAAM,IAAI,KAAK,QAAQ,MAAM,CAAC;IACxE,IAAI,CAAC,AAAC,QAAQ,KAAK,SAAS,KAAM,UAAU,QAAQ,KAAK,QAAQ,MAAM;QACrE,MAAM;IACR;IACA,OAAO;AACT;AAEA,SAAS,WAAW,IAAI;IAEtB,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,gCAAgC,KAAI;QAC3D,OAAO;IACT;IAEA,IAAI,QAAQ,KAAK,KAAK;IACtB,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,MAAM,GAAG;IACjB;IACA,KAAK,KAAK,GAAG;IACb,OAAO;AACT;AAEA,SAAS,iBAAiB,IAAI,EAAE,IAAI;IAClC,IAAI;IAEJ,eAAe,GACf,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE;QAAE,OAAO;IAAgB;IACnD,QAAQ,KAAK,KAAK;IAClB,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG;QAAE,OAAO;IAAgB;IAErD,yBAAyB,GACzB,MAAM,IAAI,GAAG;IACb,KAAK,IAAI,GAAG;IACZ,OAAO;AACT;AAEA,SAAS,qBAAqB,IAAI,EAAE,UAAU;IAC5C,IAAI,aAAa,WAAW,MAAM;IAElC,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,eAAe,GACf,IAAI,CAAC,KAAK,aAAa,OAAM,CAAC,KAAK,KAAK,CAAC,aAAa,KAAI;QAAE,OAAO;IAAgB;IACnF,QAAQ,KAAK,KAAK;IAElB,IAAI,MAAM,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAM;QAC3C,OAAO;IACT;IAEA,2CAA2C,GAC3C,IAAI,MAAM,IAAI,KAAK,MAAM;QACvB,SAAS,GAAG,sBAAsB;QAClC,qDAAqD,GACrD,SAAS,QAAQ,QAAQ,YAAY,YAAY;QACjD,IAAI,WAAW,MAAM,KAAK,EAAE;YAC1B,OAAO;QACT;IACF;IACA;sCACoC,GACpC,MAAM,aAAa,MAAM,YAAY,YAAY;IACjD,IAAI,KAAK;QACP,MAAM,IAAI,GAAG;QACb,OAAO;IACT;IACA,MAAM,QAAQ,GAAG;IACjB,mDAAmD;IACnD,OAAO;AACT;AAEA,QAAQ,YAAY,GAAG;AACvB,QAAQ,aAAa,GAAG;AACxB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,WAAW,GAAG;AACtB,QAAQ,YAAY,GAAG;AACvB,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,WAAW,GAAG,sCAEtB;;;;;;;;AAQA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/constants.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nmodule.exports = {\n\n  /* Allowed flush values; see deflate() and inflate() below for details */\n  Z_NO_FLUSH:         0,\n  Z_PARTIAL_FLUSH:    1,\n  Z_SYNC_FLUSH:       2,\n  Z_FULL_FLUSH:       3,\n  Z_FINISH:           4,\n  Z_BLOCK:            5,\n  Z_TREES:            6,\n\n  /* Return codes for the compression/decompression functions. Negative values\n  * are errors, positive values are used for special but normal events.\n  */\n  Z_OK:               0,\n  Z_STREAM_END:       1,\n  Z_NEED_DICT:        2,\n  Z_ERRNO:           -1,\n  Z_STREAM_ERROR:    -2,\n  Z_DATA_ERROR:      -3,\n  //Z_MEM_ERROR:     -4,\n  Z_BUF_ERROR:       -5,\n  //Z_VERSION_ERROR: -6,\n\n  /* compression levels */\n  Z_NO_COMPRESSION:         0,\n  Z_BEST_SPEED:             1,\n  Z_BEST_COMPRESSION:       9,\n  Z_DEFAULT_COMPRESSION:   -1,\n\n\n  Z_FILTERED:               1,\n  Z_HUFFMAN_ONLY:           2,\n  Z_RLE:                    3,\n  Z_FIXED:                  4,\n  Z_DEFAULT_STRATEGY:       0,\n\n  /* Possible values of the data_type field (though see inflate()) */\n  Z_BINARY:                 0,\n  Z_TEXT:                   1,\n  //Z_ASCII:                1, // = Z_TEXT (deprecated)\n  Z_UNKNOWN:                2,\n\n  /* The deflate compression method */\n  Z_DEFLATED:               8\n  //Z_NULL:                 null // Use -1 or null inline, depending on var type\n};\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,OAAO,OAAO,GAAG;IAEf,uEAAuE,GACvE,YAAoB;IACpB,iBAAoB;IACpB,cAAoB;IACpB,cAAoB;IACpB,UAAoB;IACpB,SAAoB;IACpB,SAAoB;IAEpB;;EAEA,GACA,MAAoB;IACpB,cAAoB;IACpB,aAAoB;IACpB,SAAmB,CAAC;IACpB,gBAAmB,CAAC;IACpB,cAAmB,CAAC;IACpB,sBAAsB;IACtB,aAAmB,CAAC;IACpB,sBAAsB;IAEtB,sBAAsB,GACtB,kBAA0B;IAC1B,cAA0B;IAC1B,oBAA0B;IAC1B,uBAAyB,CAAC;IAG1B,YAA0B;IAC1B,gBAA0B;IAC1B,OAA0B;IAC1B,SAA0B;IAC1B,oBAA0B;IAE1B,iEAAiE,GACjE,UAA0B;IAC1B,QAA0B;IAC1B,qDAAqD;IACrD,WAA0B;IAE1B,kCAAkC,GAClC,YAA0B;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/zlib/gzheader.js"], "sourcesContent": ["'use strict';\n\n// (C) 1995-2013 <PERSON><PERSON><PERSON><PERSON> and <PERSON>\n// (C) 2014-2017 <PERSON><PERSON> and <PERSON><PERSON>\n//\n// This software is provided 'as-is', without any express or implied\n// warranty. In no event will the authors be held liable for any damages\n// arising from the use of this software.\n//\n// Permission is granted to anyone to use this software for any purpose,\n// including commercial applications, and to alter it and redistribute it\n// freely, subject to the following restrictions:\n//\n// 1. The origin of this software must not be misrepresented; you must not\n//   claim that you wrote the original software. If you use this software\n//   in a product, an acknowledgment in the product documentation would be\n//   appreciated but is not required.\n// 2. Altered source versions must be plainly marked as such, and must not be\n//   misrepresented as being the original software.\n// 3. This notice may not be removed or altered from any source distribution.\n\nfunction GZheader() {\n  /* true if compressed data believed to be text */\n  this.text       = 0;\n  /* modification time */\n  this.time       = 0;\n  /* extra flags (not used when writing a gzip file) */\n  this.xflags     = 0;\n  /* operating system */\n  this.os         = 0;\n  /* pointer to extra field or Z_NULL if none */\n  this.extra      = null;\n  /* extra field length (valid if extra != Z_NULL) */\n  this.extra_len  = 0; // Actually, we don't need it in JS,\n                       // but leave for few code modifications\n\n  //\n  // Setup limits is not necessary because in js we should not preallocate memory\n  // for inflate use constant limit in 65536 bytes\n  //\n\n  /* space at extra (only when reading header) */\n  // this.extra_max  = 0;\n  /* pointer to zero-terminated file name or Z_NULL */\n  this.name       = '';\n  /* space at name (only when reading header) */\n  // this.name_max   = 0;\n  /* pointer to zero-terminated comment or Z_NULL */\n  this.comment    = '';\n  /* space at comment (only when reading header) */\n  // this.comm_max   = 0;\n  /* true if there was or will be a header crc */\n  this.hcrc       = 0;\n  /* true when done reading gzip header (not used when writing a gzip file) */\n  this.done       = false;\n}\n\nmodule.exports = GZheader;\n"], "names": [], "mappings": "AAAA;AAEA,gDAAgD;AAChD,kDAAkD;AAClD,EAAE;AACF,oEAAoE;AACpE,wEAAwE;AACxE,yCAAyC;AACzC,EAAE;AACF,wEAAwE;AACxE,yEAAyE;AACzE,iDAAiD;AACjD,EAAE;AACF,0EAA0E;AAC1E,yEAAyE;AACzE,0EAA0E;AAC1E,qCAAqC;AACrC,6EAA6E;AAC7E,mDAAmD;AACnD,6EAA6E;AAE7E,SAAS;IACP,+CAA+C,GAC/C,IAAI,CAAC,IAAI,GAAS;IAClB,qBAAqB,GACrB,IAAI,CAAC,IAAI,GAAS;IAClB,mDAAmD,GACnD,IAAI,CAAC,MAAM,GAAO;IAClB,oBAAoB,GACpB,IAAI,CAAC,EAAE,GAAW;IAClB,4CAA4C,GAC5C,IAAI,CAAC,KAAK,GAAQ;IAClB,iDAAiD,GACjD,IAAI,CAAC,SAAS,GAAI,GAAG,oCAAoC;IACpC,uCAAuC;IAE5D,EAAE;IACF,+EAA+E;IAC/E,gDAAgD;IAChD,EAAE;IAEF,6CAA6C,GAC7C,uBAAuB;IACvB,kDAAkD,GAClD,IAAI,CAAC,IAAI,GAAS;IAClB,4CAA4C,GAC5C,uBAAuB;IACvB,gDAAgD,GAChD,IAAI,CAAC,OAAO,GAAM;IAClB,+CAA+C,GAC/C,uBAAuB;IACvB,6CAA6C,GAC7C,IAAI,CAAC,IAAI,GAAS;IAClB,0EAA0E,GAC1E,IAAI,CAAC,IAAI,GAAS;AACpB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/lib/inflate.js"], "sourcesContent": ["'use strict';\n\n\nvar zlib_inflate = require('./zlib/inflate');\nvar utils        = require('./utils/common');\nvar strings      = require('./utils/strings');\nvar c            = require('./zlib/constants');\nvar msg          = require('./zlib/messages');\nvar ZStream      = require('./zlib/zstream');\nvar GZheader     = require('./zlib/gzheader');\n\nvar toString = Object.prototype.toString;\n\n/**\n * class Inflate\n *\n * Generic JS-style wrapper for zlib calls. If you don't need\n * streaming behaviour - use more simple functions: [[inflate]]\n * and [[inflateRaw]].\n **/\n\n/* internal\n * inflate.chunks -> Array\n *\n * Chunks of output data, if [[Inflate#onData]] not overridden.\n **/\n\n/**\n * Inflate.result -> Uint8Array|Array|String\n *\n * Uncompressed result, generated by default [[Inflate#onData]]\n * and [[Inflate#onEnd]] handlers. Filled after you push last chunk\n * (call [[Inflate#push]] with `Z_FINISH` / `true` param) or if you\n * push a chunk with explicit flush (call [[Inflate#push]] with\n * `Z_SYNC_FLUSH` param).\n **/\n\n/**\n * Inflate.err -> Number\n *\n * Error code after inflate finished. 0 (Z_OK) on success.\n * Should be checked if broken data possible.\n **/\n\n/**\n * Inflate.msg -> String\n *\n * Error message, if [[Inflate.err]] != 0\n **/\n\n\n/**\n * new Inflate(options)\n * - options (Object): zlib inflate options.\n *\n * Creates new inflator instance with specified params. Throws exception\n * on bad params. Supported options:\n *\n * - `windowBits`\n * - `dictionary`\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information on these.\n *\n * Additional options, for internal needs:\n *\n * - `chunkSize` - size of generated data chunks (16K by default)\n * - `raw` (Boolean) - do raw inflate\n * - `to` (String) - if equal to 'string', then result will be converted\n *   from utf8 to utf16 (javascript) string. When string output requested,\n *   chunk length can differ from `chunkSize`, depending on content.\n *\n * By default, when no options set, autodetect deflate/gzip data format via\n * wrapper header.\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , chunk1 = Uint8Array([1,2,3,4,5,6,7,8,9])\n *   , chunk2 = Uint8Array([10,11,12,13,14,15,16,17,18,19]);\n *\n * var inflate = new pako.Inflate({ level: 3});\n *\n * inflate.push(chunk1, false);\n * inflate.push(chunk2, true);  // true -> last chunk\n *\n * if (inflate.err) { throw new Error(inflate.err); }\n *\n * console.log(inflate.result);\n * ```\n **/\nfunction Inflate(options) {\n  if (!(this instanceof Inflate)) return new Inflate(options);\n\n  this.options = utils.assign({\n    chunkSize: 16384,\n    windowBits: 0,\n    to: ''\n  }, options || {});\n\n  var opt = this.options;\n\n  // Force window size for `raw` data, if not set directly,\n  // because we have no header for autodetect.\n  if (opt.raw && (opt.windowBits >= 0) && (opt.windowBits < 16)) {\n    opt.windowBits = -opt.windowBits;\n    if (opt.windowBits === 0) { opt.windowBits = -15; }\n  }\n\n  // If `windowBits` not defined (and mode not raw) - set autodetect flag for gzip/deflate\n  if ((opt.windowBits >= 0) && (opt.windowBits < 16) &&\n      !(options && options.windowBits)) {\n    opt.windowBits += 32;\n  }\n\n  // Gzip header has no info about windows size, we can do autodetect only\n  // for deflate. So, if window size not set, force it to max when gzip possible\n  if ((opt.windowBits > 15) && (opt.windowBits < 48)) {\n    // bit 3 (16) -> gzipped data\n    // bit 4 (32) -> autodetect gzip/deflate\n    if ((opt.windowBits & 15) === 0) {\n      opt.windowBits |= 15;\n    }\n  }\n\n  this.err    = 0;      // error code, if happens (0 = Z_OK)\n  this.msg    = '';     // error message\n  this.ended  = false;  // used to avoid multiple onEnd() calls\n  this.chunks = [];     // chunks of compressed data\n\n  this.strm   = new ZStream();\n  this.strm.avail_out = 0;\n\n  var status  = zlib_inflate.inflateInit2(\n    this.strm,\n    opt.windowBits\n  );\n\n  if (status !== c.Z_OK) {\n    throw new Error(msg[status]);\n  }\n\n  this.header = new GZheader();\n\n  zlib_inflate.inflateGetHeader(this.strm, this.header);\n\n  // Setup dictionary\n  if (opt.dictionary) {\n    // Convert data if needed\n    if (typeof opt.dictionary === 'string') {\n      opt.dictionary = strings.string2buf(opt.dictionary);\n    } else if (toString.call(opt.dictionary) === '[object ArrayBuffer]') {\n      opt.dictionary = new Uint8Array(opt.dictionary);\n    }\n    if (opt.raw) { //In raw mode we need to set the dictionary early\n      status = zlib_inflate.inflateSetDictionary(this.strm, opt.dictionary);\n      if (status !== c.Z_OK) {\n        throw new Error(msg[status]);\n      }\n    }\n  }\n}\n\n/**\n * Inflate#push(data[, mode]) -> Boolean\n * - data (Uint8Array|Array|ArrayBuffer|String): input data\n * - mode (Number|Boolean): 0..6 for corresponding Z_NO_FLUSH..Z_TREE modes.\n *   See constants. Skipped or `false` means Z_NO_FLUSH, `true` means Z_FINISH.\n *\n * Sends input data to inflate pipe, generating [[Inflate#onData]] calls with\n * new output chunks. Returns `true` on success. The last data block must have\n * mode Z_FINISH (or `true`). That will flush internal pending buffers and call\n * [[Inflate#onEnd]]. For interim explicit flushes (without ending the stream) you\n * can use mode Z_SYNC_FLUSH, keeping the decompression context.\n *\n * On fail call [[Inflate#onEnd]] with error code and return false.\n *\n * We strongly recommend to use `Uint8Array` on input for best speed (output\n * format is detected automatically). Also, don't skip last param and always\n * use the same type in your code (boolean or number). That will improve JS speed.\n *\n * For regular `Array`-s make sure all elements are [0..255].\n *\n * ##### Example\n *\n * ```javascript\n * push(chunk, false); // push one of data chunks\n * ...\n * push(chunk, true);  // push last chunk\n * ```\n **/\nInflate.prototype.push = function (data, mode) {\n  var strm = this.strm;\n  var chunkSize = this.options.chunkSize;\n  var dictionary = this.options.dictionary;\n  var status, _mode;\n  var next_out_utf8, tail, utf8str;\n\n  // Flag to properly process Z_BUF_ERROR on testing inflate call\n  // when we check that all output data was flushed.\n  var allowBufError = false;\n\n  if (this.ended) { return false; }\n  _mode = (mode === ~~mode) ? mode : ((mode === true) ? c.Z_FINISH : c.Z_NO_FLUSH);\n\n  // Convert data if needed\n  if (typeof data === 'string') {\n    // Only binary strings can be decompressed on practice\n    strm.input = strings.binstring2buf(data);\n  } else if (toString.call(data) === '[object ArrayBuffer]') {\n    strm.input = new Uint8Array(data);\n  } else {\n    strm.input = data;\n  }\n\n  strm.next_in = 0;\n  strm.avail_in = strm.input.length;\n\n  do {\n    if (strm.avail_out === 0) {\n      strm.output = new utils.Buf8(chunkSize);\n      strm.next_out = 0;\n      strm.avail_out = chunkSize;\n    }\n\n    status = zlib_inflate.inflate(strm, c.Z_NO_FLUSH);    /* no bad return value */\n\n    if (status === c.Z_NEED_DICT && dictionary) {\n      status = zlib_inflate.inflateSetDictionary(this.strm, dictionary);\n    }\n\n    if (status === c.Z_BUF_ERROR && allowBufError === true) {\n      status = c.Z_OK;\n      allowBufError = false;\n    }\n\n    if (status !== c.Z_STREAM_END && status !== c.Z_OK) {\n      this.onEnd(status);\n      this.ended = true;\n      return false;\n    }\n\n    if (strm.next_out) {\n      if (strm.avail_out === 0 || status === c.Z_STREAM_END || (strm.avail_in === 0 && (_mode === c.Z_FINISH || _mode === c.Z_SYNC_FLUSH))) {\n\n        if (this.options.to === 'string') {\n\n          next_out_utf8 = strings.utf8border(strm.output, strm.next_out);\n\n          tail = strm.next_out - next_out_utf8;\n          utf8str = strings.buf2string(strm.output, next_out_utf8);\n\n          // move tail\n          strm.next_out = tail;\n          strm.avail_out = chunkSize - tail;\n          if (tail) { utils.arraySet(strm.output, strm.output, next_out_utf8, tail, 0); }\n\n          this.onData(utf8str);\n\n        } else {\n          this.onData(utils.shrinkBuf(strm.output, strm.next_out));\n        }\n      }\n    }\n\n    // When no more input data, we should check that internal inflate buffers\n    // are flushed. The only way to do it when avail_out = 0 - run one more\n    // inflate pass. But if output data not exists, inflate return Z_BUF_ERROR.\n    // Here we set flag to process this error properly.\n    //\n    // NOTE. Deflate does not return error in this case and does not needs such\n    // logic.\n    if (strm.avail_in === 0 && strm.avail_out === 0) {\n      allowBufError = true;\n    }\n\n  } while ((strm.avail_in > 0 || strm.avail_out === 0) && status !== c.Z_STREAM_END);\n\n  if (status === c.Z_STREAM_END) {\n    _mode = c.Z_FINISH;\n  }\n\n  // Finalize on the last chunk.\n  if (_mode === c.Z_FINISH) {\n    status = zlib_inflate.inflateEnd(this.strm);\n    this.onEnd(status);\n    this.ended = true;\n    return status === c.Z_OK;\n  }\n\n  // callback interim results if Z_SYNC_FLUSH.\n  if (_mode === c.Z_SYNC_FLUSH) {\n    this.onEnd(c.Z_OK);\n    strm.avail_out = 0;\n    return true;\n  }\n\n  return true;\n};\n\n\n/**\n * Inflate#onData(chunk) -> Void\n * - chunk (Uint8Array|Array|String): output data. Type of array depends\n *   on js engine support. When string output requested, each chunk\n *   will be string.\n *\n * By default, stores data blocks in `chunks[]` property and glue\n * those in `onEnd`. Override this handler, if you need another behaviour.\n **/\nInflate.prototype.onData = function (chunk) {\n  this.chunks.push(chunk);\n};\n\n\n/**\n * Inflate#onEnd(status) -> Void\n * - status (Number): inflate status. 0 (Z_OK) on success,\n *   other if not.\n *\n * Called either after you tell inflate that the input stream is\n * complete (Z_FINISH) or should be flushed (Z_SYNC_FLUSH)\n * or if an error happened. By default - join collected chunks,\n * free memory and fill `results` / `err` properties.\n **/\nInflate.prototype.onEnd = function (status) {\n  // On success - join\n  if (status === c.Z_OK) {\n    if (this.options.to === 'string') {\n      // Glue & convert here, until we teach pako to send\n      // utf8 aligned strings to onData\n      this.result = this.chunks.join('');\n    } else {\n      this.result = utils.flattenChunks(this.chunks);\n    }\n  }\n  this.chunks = [];\n  this.err = status;\n  this.msg = this.strm.msg;\n};\n\n\n/**\n * inflate(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * Decompress `data` with inflate/ungzip and `options`. Autodetect\n * format via wrapper header by default. That's why we don't provide\n * separate `ungzip` method.\n *\n * Supported options are:\n *\n * - windowBits\n *\n * [http://zlib.net/manual.html#Advanced](http://zlib.net/manual.html#Advanced)\n * for more information.\n *\n * Sugar (options):\n *\n * - `raw` (Boolean) - say that we work with raw stream, if you don't wish to specify\n *   negative windowBits implicitly.\n * - `to` (String) - if equal to 'string', then result will be converted\n *   from utf8 to utf16 (javascript) string. When string output requested,\n *   chunk length can differ from `chunkSize`, depending on content.\n *\n *\n * ##### Example:\n *\n * ```javascript\n * var pako = require('pako')\n *   , input = pako.deflate([1,2,3,4,5,6,7,8,9])\n *   , output;\n *\n * try {\n *   output = pako.inflate(input);\n * } catch (err)\n *   console.log(err);\n * }\n * ```\n **/\nfunction inflate(input, options) {\n  var inflator = new Inflate(options);\n\n  inflator.push(input, true);\n\n  // That will never happens, if you don't cheat with options :)\n  if (inflator.err) { throw inflator.msg || msg[inflator.err]; }\n\n  return inflator.result;\n}\n\n\n/**\n * inflateRaw(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * The same as [[inflate]], but creates raw data, without wrapper\n * (header and adler32 crc).\n **/\nfunction inflateRaw(input, options) {\n  options = options || {};\n  options.raw = true;\n  return inflate(input, options);\n}\n\n\n/**\n * ungzip(data[, options]) -> Uint8Array|Array|String\n * - data (Uint8Array|Array|String): input data to decompress.\n * - options (Object): zlib inflate options.\n *\n * Just shortcut to [[inflate]], because it autodetects format\n * by header.content. Done for convenience.\n **/\n\n\nexports.Inflate = Inflate;\nexports.inflate = inflate;\nexports.inflateRaw = inflateRaw;\nexports.ungzip  = inflate;\n"], "names": [], "mappings": "AAAA;AAGA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,WAAW,OAAO,SAAS,CAAC,QAAQ;AAExC;;;;;;EAME,GAEF;;;;EAIE,GAEF;;;;;;;;EAQE,GAEF;;;;;EAKE,GAEF;;;;EAIE,GAGF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwCE,GACF,SAAS,QAAQ,OAAO;IACtB,IAAI,CAAC,CAAC,IAAI,YAAY,OAAO,GAAG,OAAO,IAAI,QAAQ;IAEnD,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC;QAC1B,WAAW;QACX,YAAY;QACZ,IAAI;IACN,GAAG,WAAW,CAAC;IAEf,IAAI,MAAM,IAAI,CAAC,OAAO;IAEtB,yDAAyD;IACzD,4CAA4C;IAC5C,IAAI,IAAI,GAAG,IAAK,IAAI,UAAU,IAAI,KAAO,IAAI,UAAU,GAAG,IAAK;QAC7D,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU;QAChC,IAAI,IAAI,UAAU,KAAK,GAAG;YAAE,IAAI,UAAU,GAAG,CAAC;QAAI;IACpD;IAEA,wFAAwF;IACxF,IAAI,AAAC,IAAI,UAAU,IAAI,KAAO,IAAI,UAAU,GAAG,MAC3C,CAAC,CAAC,WAAW,QAAQ,UAAU,GAAG;QACpC,IAAI,UAAU,IAAI;IACpB;IAEA,wEAAwE;IACxE,8EAA8E;IAC9E,IAAI,AAAC,IAAI,UAAU,GAAG,MAAQ,IAAI,UAAU,GAAG,IAAK;QAClD,6BAA6B;QAC7B,wCAAwC;QACxC,IAAI,CAAC,IAAI,UAAU,GAAG,EAAE,MAAM,GAAG;YAC/B,IAAI,UAAU,IAAI;QACpB;IACF;IAEA,IAAI,CAAC,GAAG,GAAM,GAAQ,oCAAoC;IAC1D,IAAI,CAAC,GAAG,GAAM,IAAQ,gBAAgB;IACtC,IAAI,CAAC,KAAK,GAAI,OAAQ,uCAAuC;IAC7D,IAAI,CAAC,MAAM,GAAG,EAAE,EAAM,4BAA4B;IAElD,IAAI,CAAC,IAAI,GAAK,IAAI;IAClB,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG;IAEtB,IAAI,SAAU,aAAa,YAAY,CACrC,IAAI,CAAC,IAAI,EACT,IAAI,UAAU;IAGhB,IAAI,WAAW,EAAE,IAAI,EAAE;QACrB,MAAM,IAAI,MAAM,GAAG,CAAC,OAAO;IAC7B;IAEA,IAAI,CAAC,MAAM,GAAG,IAAI;IAElB,aAAa,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM;IAEpD,mBAAmB;IACnB,IAAI,IAAI,UAAU,EAAE;QAClB,yBAAyB;QACzB,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU;YACtC,IAAI,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI,UAAU;QACpD,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,UAAU,MAAM,wBAAwB;YACnE,IAAI,UAAU,GAAG,IAAI,WAAW,IAAI,UAAU;QAChD;QACA,IAAI,IAAI,GAAG,EAAE;YACX,SAAS,aAAa,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,UAAU;YACpE,IAAI,WAAW,EAAE,IAAI,EAAE;gBACrB,MAAM,IAAI,MAAM,GAAG,CAAC,OAAO;YAC7B;QACF;IACF;AACF;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BE,GACF,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,IAAI;IAC3C,IAAI,OAAO,IAAI,CAAC,IAAI;IACpB,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,SAAS;IACtC,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,UAAU;IACxC,IAAI,QAAQ;IACZ,IAAI,eAAe,MAAM;IAEzB,+DAA+D;IAC/D,kDAAkD;IAClD,IAAI,gBAAgB;IAEpB,IAAI,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO;IAAO;IAChC,QAAQ,AAAC,SAAS,CAAC,CAAC,OAAQ,OAAQ,AAAC,SAAS,OAAQ,EAAE,QAAQ,GAAG,EAAE,UAAU;IAE/E,yBAAyB;IACzB,IAAI,OAAO,SAAS,UAAU;QAC5B,sDAAsD;QACtD,KAAK,KAAK,GAAG,QAAQ,aAAa,CAAC;IACrC,OAAO,IAAI,SAAS,IAAI,CAAC,UAAU,wBAAwB;QACzD,KAAK,KAAK,GAAG,IAAI,WAAW;IAC9B,OAAO;QACL,KAAK,KAAK,GAAG;IACf;IAEA,KAAK,OAAO,GAAG;IACf,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,MAAM;IAEjC,GAAG;QACD,IAAI,KAAK,SAAS,KAAK,GAAG;YACxB,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC;YAC7B,KAAK,QAAQ,GAAG;YAChB,KAAK,SAAS,GAAG;QACnB;QAEA,SAAS,aAAa,OAAO,CAAC,MAAM,EAAE,UAAU,GAAM,uBAAuB;QAE7E,IAAI,WAAW,EAAE,WAAW,IAAI,YAAY;YAC1C,SAAS,aAAa,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE;QACxD;QAEA,IAAI,WAAW,EAAE,WAAW,IAAI,kBAAkB,MAAM;YACtD,SAAS,EAAE,IAAI;YACf,gBAAgB;QAClB;QAEA,IAAI,WAAW,EAAE,YAAY,IAAI,WAAW,EAAE,IAAI,EAAE;YAClD,IAAI,CAAC,KAAK,CAAC;YACX,IAAI,CAAC,KAAK,GAAG;YACb,OAAO;QACT;QAEA,IAAI,KAAK,QAAQ,EAAE;YACjB,IAAI,KAAK,SAAS,KAAK,KAAK,WAAW,EAAE,YAAY,IAAK,KAAK,QAAQ,KAAK,KAAK,CAAC,UAAU,EAAE,QAAQ,IAAI,UAAU,EAAE,YAAY,GAAI;gBAEpI,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,UAAU;oBAEhC,gBAAgB,QAAQ,UAAU,CAAC,KAAK,MAAM,EAAE,KAAK,QAAQ;oBAE7D,OAAO,KAAK,QAAQ,GAAG;oBACvB,UAAU,QAAQ,UAAU,CAAC,KAAK,MAAM,EAAE;oBAE1C,YAAY;oBACZ,KAAK,QAAQ,GAAG;oBAChB,KAAK,SAAS,GAAG,YAAY;oBAC7B,IAAI,MAAM;wBAAE,MAAM,QAAQ,CAAC,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,eAAe,MAAM;oBAAI;oBAE9E,IAAI,CAAC,MAAM,CAAC;gBAEd,OAAO;oBACL,IAAI,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,MAAM,EAAE,KAAK,QAAQ;gBACxD;YACF;QACF;QAEA,yEAAyE;QACzE,uEAAuE;QACvE,2EAA2E;QAC3E,mDAAmD;QACnD,EAAE;QACF,2EAA2E;QAC3E,SAAS;QACT,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG;YAC/C,gBAAgB;QAClB;IAEF,QAAS,CAAC,KAAK,QAAQ,GAAG,KAAK,KAAK,SAAS,KAAK,CAAC,KAAK,WAAW,EAAE,YAAY,CAAE;IAEnF,IAAI,WAAW,EAAE,YAAY,EAAE;QAC7B,QAAQ,EAAE,QAAQ;IACpB;IAEA,8BAA8B;IAC9B,IAAI,UAAU,EAAE,QAAQ,EAAE;QACxB,SAAS,aAAa,UAAU,CAAC,IAAI,CAAC,IAAI;QAC1C,IAAI,CAAC,KAAK,CAAC;QACX,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,WAAW,EAAE,IAAI;IAC1B;IAEA,4CAA4C;IAC5C,IAAI,UAAU,EAAE,YAAY,EAAE;QAC5B,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI;QACjB,KAAK,SAAS,GAAG;QACjB,OAAO;IACT;IAEA,OAAO;AACT;AAGA;;;;;;;;EAQE,GACF,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;IACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACnB;AAGA;;;;;;;;;EASE,GACF,QAAQ,SAAS,CAAC,KAAK,GAAG,SAAU,MAAM;IACxC,oBAAoB;IACpB,IAAI,WAAW,EAAE,IAAI,EAAE;QACrB,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,KAAK,UAAU;YAChC,mDAAmD;YACnD,iCAAiC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC,OAAO;YACL,IAAI,CAAC,MAAM,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,MAAM;QAC/C;IACF;IACA,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG;AAC1B;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCE,GACF,SAAS,QAAQ,KAAK,EAAE,OAAO;IAC7B,IAAI,WAAW,IAAI,QAAQ;IAE3B,SAAS,IAAI,CAAC,OAAO;IAErB,8DAA8D;IAC9D,IAAI,SAAS,GAAG,EAAE;QAAE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,SAAS,GAAG,CAAC;IAAE;IAE7D,OAAO,SAAS,MAAM;AACxB;AAGA;;;;;;;EAOE,GACF,SAAS,WAAW,KAAK,EAAE,OAAO;IAChC,UAAU,WAAW,CAAC;IACtB,QAAQ,GAAG,GAAG;IACd,OAAO,QAAQ,OAAO;AACxB;AAGA;;;;;;;EAOE,GAGF,QAAQ,OAAO,GAAG;AAClB,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,QAAQ,MAAM,GAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/pako/index.js"], "sourcesContent": ["// Top level file is just a mixin of submodules & constants\n'use strict';\n\nvar assign    = require('./lib/utils/common').assign;\n\nvar deflate   = require('./lib/deflate');\nvar inflate   = require('./lib/inflate');\nvar constants = require('./lib/zlib/constants');\n\nvar pako = {};\n\nassign(pako, deflate, inflate, constants);\n\nmodule.exports = pako;\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D;AAEA,IAAI,SAAY,qGAA8B,MAAM;AAEpD,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,OAAO,CAAC;AAEZ,OAAO,MAAM,SAAS,SAAS;AAE/B,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}