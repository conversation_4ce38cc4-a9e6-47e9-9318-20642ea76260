'use client';

import React, { useState } from 'react';
import { useTheme, ThemeToggle } from './ThemeProvider';
import { UserPreferences } from '@/lib/types';
import { Settings as SettingsIcon, Type, Palette, Save, RotateCcw, Zap } from 'lucide-react';

interface SettingsProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const FONT_FAMILIES = [
  { value: 'Inter', label: 'Inter (Default)' },
  { value: 'Georgia', label: 'Georgia (Serif)' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Arial', label: 'Arial (Sans-serif)' },
  { value: 'Helvetica', label: 'Helvetica' },
  { value: 'Courier New', label: 'Courier New (Monospace)' },
  { value: 'Verdana', label: 'Verdana' },
  { value: 'Trebuchet MS', label: 'Trebuchet MS' }
];

const FONT_SIZES = [
  { value: 12, label: 'Extra Small' },
  { value: 14, label: 'Small' },
  { value: 16, label: 'Medium (Default)' },
  { value: 18, label: 'Large' },
  { value: 20, label: 'Extra Large' },
  { value: 24, label: 'Huge' }
];

const AUTO_SAVE_INTERVALS = [
  { value: 1000, label: '1 second' },
  { value: 2000, label: '2 seconds (Default)' },
  { value: 5000, label: '5 seconds' },
  { value: 10000, label: '10 seconds' },
  { value: 30000, label: '30 seconds' },
  { value: 60000, label: '1 minute' }
];

export default function Settings({ isOpen, onClose, className = '' }: SettingsProps) {
  const { preferences, updatePreferences } = useTheme();
  const [tempPreferences, setTempPreferences] = useState<UserPreferences | null>(preferences);
  const [hasChanges, setHasChanges] = useState(false);

  // Update temp preferences when actual preferences change
  React.useEffect(() => {
    if (preferences && !hasChanges) {
      setTempPreferences(preferences);
    }
  }, [preferences, hasChanges]);

  if (!isOpen || !tempPreferences) return null;

  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    setTempPreferences(prev => prev ? { ...prev, [key]: value } : null);
    setHasChanges(true);
  };

  const handleSave = () => {
    if (tempPreferences) {
      updatePreferences(tempPreferences);
      setHasChanges(false);
    }
  };

  const handleReset = () => {
    setTempPreferences(preferences);
    setHasChanges(false);
  };

  const handleResetToDefaults = () => {
    const defaultPreferences: UserPreferences = {
      id: tempPreferences.id,
      theme: 'system',
      fontSize: 16,
      fontFamily: 'Inter',
      spellCheckEnabled: true,
      autoSaveInterval: 2000
    };
    setTempPreferences(defaultPreferences);
    setHasChanges(true);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-lg shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <SettingsIcon className="w-6 h-6 text-gray-600 dark:text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Settings
              </h2>
            </div>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-8 max-h-96 overflow-y-auto">
            {/* Theme Settings */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Palette className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Appearance
                </h3>
              </div>
              
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Theme
                </label>
                <ThemeToggle />
              </div>
            </div>

            {/* Typography Settings */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Type className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Typography
                </h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Font Family */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Font Family
                  </label>
                  <select
                    value={tempPreferences.fontFamily}
                    onChange={(e) => handlePreferenceChange('fontFamily', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {FONT_FAMILIES.map(font => (
                      <option key={font.value} value={font.value}>
                        {font.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Font Size */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Font Size
                  </label>
                  <select
                    value={tempPreferences.fontSize}
                    onChange={(e) => handlePreferenceChange('fontSize', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {FONT_SIZES.map(size => (
                      <option key={size.value} value={size.value}>
                        {size.label} ({size.value}px)
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Font Preview */}
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Preview:</p>
                <p 
                  style={{ 
                    fontFamily: tempPreferences.fontFamily,
                    fontSize: `${tempPreferences.fontSize}px`
                  }}
                  className="text-gray-900 dark:text-white"
                >
                  The quick brown fox jumps over the lazy dog. This is how your journal entries will look with the selected font settings.
                </p>
              </div>
            </div>

            {/* Editor Settings */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Editor
                </h3>
              </div>
              
              <div className="space-y-4">
                {/* Auto-save Interval */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Auto-save Interval
                  </label>
                  <select
                    value={tempPreferences.autoSaveInterval}
                    onChange={(e) => handlePreferenceChange('autoSaveInterval', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {AUTO_SAVE_INTERVALS.map(interval => (
                      <option key={interval.value} value={interval.value}>
                        {interval.label}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    How often to automatically save your work while typing
                  </p>
                </div>

                {/* Spell Check */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Spell Check
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Enable spell checking while typing
                    </p>
                  </div>
                  <button
                    onClick={() => handlePreferenceChange('spellCheckEnabled', !tempPreferences.spellCheckEnabled)}
                    className={`
                      relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                      ${tempPreferences.spellCheckEnabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}
                    `}
                  >
                    <span
                      className={`
                        inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                        ${tempPreferences.spellCheckEnabled ? 'translate-x-6' : 'translate-x-1'}
                      `}
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={handleResetToDefaults}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset to Defaults</span>
            </button>
            
            <div className="flex space-x-3">
              {hasChanges && (
                <button
                  onClick={handleReset}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Cancel
                </button>
              )}
              
              <button
                onClick={handleSave}
                disabled={!hasChanges}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
