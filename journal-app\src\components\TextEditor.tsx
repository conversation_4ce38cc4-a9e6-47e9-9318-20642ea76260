'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { JournalEntry, UserPreferences } from '@/lib/types';
import { localStorageManager } from '@/lib/localStorage';
import { Save, Clock, Type } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

interface TextEditorProps {
  entry?: JournalEntry;
  onSave: (entry: JournalEntry) => void;
  onAutoSave?: (content: string) => void;
  preferences: UserPreferences;
  className?: string;
}

export default function TextEditor({ 
  entry, 
  onSave, 
  onAutoSave, 
  preferences, 
  className = '' 
}: TextEditorProps) {
  const [title, setTitle] = useState(entry?.title || '');
  const [content, setContent] = useState(entry?.content || '');
  const [tags, setTags] = useState<string[]>(entry?.tags || []);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(entry?.updatedAt || null);
  const [wordCount, setWordCount] = useState(0);
  
  const titleRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const entryIdRef = useRef(entry?.id || uuidv4());

  // Calculate word count
  const calculateWordCount = useCallback((text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }, []);

  // Auto-save functionality
  const triggerAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(() => {
      if (content.trim() || title.trim()) {
        // Save draft to localStorage
        localStorageManager.saveDraft(entryIdRef.current, content);
        
        // Trigger auto-save callback if provided
        if (onAutoSave) {
          onAutoSave(content);
        }

        setLastSaved(new Date());
      }
    }, preferences.autoSaveInterval);
  }, [content, title, preferences.autoSaveInterval, onAutoSave]);

  // Handle content changes
  const handleContentChange = useCallback((value: string) => {
    setContent(value);
    setWordCount(calculateWordCount(value));
    triggerAutoSave();
  }, [calculateWordCount, triggerAutoSave]);

  // Handle title changes
  const handleTitleChange = useCallback((value: string) => {
    setTitle(value);
    triggerAutoSave();
  }, [triggerAutoSave]);

  // Manual save
  const handleSave = useCallback(async () => {
    if (!title.trim() && !content.trim()) return;

    setIsSaving(true);
    
    try {
      const now = new Date();
      const savedEntry: JournalEntry = {
        id: entryIdRef.current,
        title: title.trim() || 'Untitled',
        content: content,
        tags: tags,
        createdAt: entry?.createdAt || now,
        updatedAt: now,
        wordCount: calculateWordCount(content)
      };

      // Save to localStorage
      localStorageManager.saveEntry(savedEntry);
      
      // Clear draft
      localStorageManager.clearDraft(entryIdRef.current);
      
      // Call parent save handler
      onSave(savedEntry);
      
      setLastSaved(now);
    } catch (error) {
      console.error('Failed to save entry:', error);
    } finally {
      setIsSaving(false);
    }
  }, [title, content, tags, entry, calculateWordCount, onSave]);

  // Load draft on mount
  useEffect(() => {
    if (!entry) {
      const draft = localStorageManager.getDraft(entryIdRef.current);
      if (draft) {
        setContent(draft.content);
        setWordCount(calculateWordCount(draft.content));
      }
    }
  }, [entry, calculateWordCount]);

  // Update word count when content changes
  useEffect(() => {
    setWordCount(calculateWordCount(content));
  }, [content, calculateWordCount]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave]);

  // Cleanup auto-save timeout
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  const formatLastSaved = (date: Date | null) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header with save status */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <Clock className="w-4 h-4" />
          <span>Last saved: {formatLastSaved(lastSaved)}</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <Type className="w-4 h-4" />
            <span>{wordCount} words</span>
          </div>
          
          <button
            onClick={handleSave}
            disabled={isSaving || (!title.trim() && !content.trim())}
            className="flex items-center space-x-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save'}</span>
          </button>
        </div>
      </div>

      {/* Editor content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Title input */}
        <div className="p-4 border-b border-gray-100 dark:border-gray-700">
          <input
            ref={titleRef}
            type="text"
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            placeholder="Enter a title..."
            className="w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none"
            style={{ fontSize: `${preferences.fontSize + 8}px` }}
          />
        </div>

        {/* Content textarea */}
        <div className="flex-1 p-4 overflow-auto">
          <TextareaAutosize
            ref={contentRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            placeholder="Start writing your thoughts..."
            className="w-full h-full bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none leading-relaxed"
            style={{
              fontSize: `${preferences.fontSize}px`,
              fontFamily: preferences.fontFamily
            }}
            minRows={10}
          />
        </div>
      </div>

      {/* Mobile-friendly bottom toolbar */}
      <div className="md:hidden p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {wordCount} words
          </div>
          
          <button
            onClick={handleSave}
            disabled={isSaving || (!title.trim() && !content.trim())}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>{isSaving ? 'Saving...' : 'Save'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
