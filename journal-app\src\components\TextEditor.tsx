'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getLocalStorage, Draft } from '@/lib/localStorage';
import { Save, Clock, AlertCircle } from 'lucide-react';

interface TextEditorProps {
  entryId?: string;
  initialTitle?: string;
  initialContent?: string;
  initialTags?: string[];
  onSave?: (data: { title: string; content: string; tags: string[] }) => void;
  onAutoSave?: (data: { title: string; content: string; tags: string[] }) => void;
  className?: string;
}

export function TextEditor({
  entryId,
  initialTitle = '',
  initialContent = '',
  initialTags = [],
  onSave,
  onAutoSave,
  className = '',
}: TextEditorProps) {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [tags, setTags] = useState<string[]>(initialTags);
  const [tagInput, setTagInput] = useState('');
  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'unsaved' | 'error'>('saved');
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const contentRef = useRef<HTMLTextAreaElement>(null);
  const storage = getLocalStorage();
  const settings = storage.getSettings();

  // Reset state when initial values change (new entry selected)
  useEffect(() => {
    setTitle(initialTitle);
    setContent(initialContent);
    setTags(initialTags);
    setTagInput('');
    setSaveStatus('saved');
  }, [initialTitle, initialContent, initialTags]);

  // Calculate word and character counts
  useEffect(() => {
    const words = content.trim().split(/\s+/).filter(word => word.length > 0).length;
    setWordCount(content.trim() === '' ? 0 : words);
    setCharCount(content.length);
  }, [content]);

  // Auto-save functionality
  const performAutoSave = useCallback(() => {
    if (!settings.autoSave) return;

    const draftId = entryId || `draft_${Date.now()}`;
    const draft: Draft = {
      id: draftId,
      title,
      content,
      tags,
      lastModified: new Date().toISOString(),
    };

    try {
      storage.saveDraft(draft);
      setSaveStatus('saved');

      if (onAutoSave) {
        onAutoSave({ title, content, tags });
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
      setSaveStatus('error');
    }
  }, [title, content, tags, entryId, settings.autoSave, onAutoSave]); // Remove storage from dependencies

  // Debounced auto-save
  useEffect(() => {
    if (title || content) {
      setSaveStatus('unsaved');

      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        setSaveStatus('saving');
        performAutoSave();
      }, settings.autoSaveInterval);
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [title, content, tags, settings.autoSaveInterval]); // Remove performAutoSave from dependencies

  // Manual save
  const handleManualSave = useCallback(() => {
    if (onSave) {
      onSave({ title, content, tags });
      setSaveStatus('saved');
    } else {
      performAutoSave();
    }
  }, [title, content, tags, onSave, performAutoSave]);

  // Tag management
  const addTag = useCallback((tagName: string) => {
    const trimmedTag = tagName.trim().toLowerCase();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags(prev => [...prev, trimmedTag]);
    }
  }, [tags]);

  const removeTag = useCallback((tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  }, []);

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      if (tagInput.trim()) {
        addTag(tagInput);
        setTagInput('');
      }
    } else if (e.key === 'Backspace' && !tagInput && tags.length > 0) {
      removeTag(tags[tags.length - 1]);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleManualSave();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleManualSave]);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = contentRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [content, adjustTextareaHeight]);

  // Save status indicator
  const SaveStatusIndicator = () => {
    switch (saveStatus) {
      case 'saving':
        return (
          <div className="flex items-center text-blue-600 dark:text-blue-400">
            <Clock className="w-4 h-4 mr-1 animate-spin" />
            <span className="text-sm">Saving...</span>
          </div>
        );
      case 'saved':
        return (
          <div className="flex items-center text-green-600 dark:text-green-400">
            <Save className="w-4 h-4 mr-1" />
            <span className="text-sm">Saved</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center text-red-600 dark:text-red-400">
            <AlertCircle className="w-4 h-4 mr-1" />
            <span className="text-sm">Save failed</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <span className="text-sm">Unsaved changes</span>
          </div>
        );
    }
  };

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header with title and save status */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 space-y-2 sm:space-y-0">
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter title..."
          className="flex-1 text-lg sm:text-2xl font-bold bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
          style={{ fontSize: `${Math.max(16, settings.fontSize + 2)}px` }}
        />
        <div className="flex items-center justify-between sm:justify-end space-x-2 sm:space-x-4">
          <SaveStatusIndicator />
          <button
            onClick={handleManualSave}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            Save
          </button>
        </div>
      </div>

      {/* Tags section */}
      <div className="p-3 md:p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap items-center gap-2">
          {tags.map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 text-xs sm:text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
            >
              #{tag}
              <button
                onClick={() => removeTag(tag)}
                className="ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100"
              >
                ×
              </button>
            </span>
          ))}
          <input
            type="text"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            onKeyDown={handleTagInputKeyDown}
            placeholder="Add tags..."
            className="flex-1 min-w-[120px] px-2 py-1 text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded outline-none focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
      </div>

      {/* Content editor */}
      <div className="flex-1 p-3 md:p-4">
        <textarea
          ref={contentRef}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Start writing your journal entry..."
          className="w-full h-full min-h-[400px] bg-transparent border-none outline-none resize-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 leading-relaxed"
          style={{ 
            fontSize: `${settings.fontSize}px`,
            fontFamily: settings.fontFamily,
          }}
          spellCheck={settings.spellCheck}
        />
      </div>

      {/* Footer with stats */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400 space-y-1 sm:space-y-0">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <span className="text-xs sm:text-sm">{wordCount} words</span>
          <span className="text-xs sm:text-sm">{charCount} characters</span>
        </div>
        <div className="text-xs hidden sm:block">
          Press Ctrl+S (Cmd+S) to save manually
        </div>
      </div>
    </div>
  );
}
