{"name": "use-composed-ref", "version": "1.4.0", "description": "React hook which creates a ref function from given refs. Useful when using forwardRef.", "main": "dist/use-composed-ref.cjs.js", "module": "dist/use-composed-ref.esm.js", "exports": {".": {"types": {"import": "./dist/use-composed-ref.cjs.mjs", "default": "./dist/use-composed-ref.cjs.js"}, "module": "./dist/use-composed-ref.esm.js", "import": "./dist/use-composed-ref.cjs.mjs", "default": "./dist/use-composed-ref.cjs.js"}, "./package.json": "./package.json"}, "types": "./dist/use-composed-ref.cjs.d.ts", "files": ["dist"], "scripts": {"test": "echo \"Warning: no test specified\" || jest --env=node", "build": "preconstruct build", "preversion": "npm test", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/Andarist/use-composed-ref.git"}, "license": "MIT", "bugs": {"url": "https://github.com/Andarist/use-composed-ref/issues"}, "homepage": "https://github.com/Andarist/use-composed-ref#readme", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "devDependencies": {"@babel/core": "^7.3.4", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/preset-env": "^7.3.4", "@babel/preset-typescript": "^7.3.3", "@preconstruct/cli": "^2.8.10", "@types/react": "^16.8.8", "husky": "^1.3.1", "jest": "^24.5.0", "lint-staged": "^8.1.5", "prettier": "^1.16.4", "react": "^16.8.4", "typescript": "^5.7.2"}, "preconstruct": {"exports": {"importConditionDefaultExport": "default"}, "___experimentalFlags_WILL_CHANGE_IN_PATCH": {"importsConditions": true}}}