'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { JournalEntry, Tag, SearchFilters } from '@/lib/types';
import { localStorageManager } from '@/lib/localStorage';
import { Search, X, Filter, Calendar, Hash, SortAsc, SortDesc, Clock } from 'lucide-react';

interface SearchComponentProps {
  entries: JournalEntry[];
  tags: Tag[];
  onSearchResults: (results: JournalEntry[]) => void;
  className?: string;
}

interface SearchResult extends JournalEntry {
  matchType: 'title' | 'content' | 'tag';
  matchText: string;
  score: number;
}

export default function SearchComponent({
  entries,
  tags,
  onSearchResults,
  className = ''
}: SearchComponentProps) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    tags: [],
    dateFrom: undefined,
    dateTo: undefined,
    timeFrom: '',
    timeTo: '',
    searchInTimestamps: false,
    sortBy: 'updatedAt',
    sortOrder: 'desc'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const historyRef = useRef<HTMLDivElement>(null);

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('journal_search_history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  // Save search history to localStorage
  const saveSearchHistory = useCallback((newQuery: string) => {
    if (!newQuery.trim()) return;
    
    const updatedHistory = [
      newQuery,
      ...searchHistory.filter(q => q !== newQuery)
    ].slice(0, 10); // Keep only last 10 searches
    
    setSearchHistory(updatedHistory);
    localStorage.setItem('journal_search_history', JSON.stringify(updatedHistory));
  }, [searchHistory]);

  // Highlight search terms in text
  const highlightText = useCallback((text: string, searchQuery: string): string => {
    if (!searchQuery.trim()) return text;
    
    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>');
  }, []);

  // Calculate search score
  const calculateScore = useCallback((entry: JournalEntry, searchQuery: string): number => {
    let score = 0;
    const lowerQuery = searchQuery.toLowerCase();
    const lowerTitle = entry.title.toLowerCase();
    const lowerContent = entry.content.toLowerCase();
    
    // Title matches get higher score
    if (lowerTitle.includes(lowerQuery)) {
      score += lowerTitle === lowerQuery ? 100 : 50;
    }
    
    // Content matches
    const contentMatches = (lowerContent.match(new RegExp(lowerQuery, 'g')) || []).length;
    score += contentMatches * 10;
    
    // Tag matches get high score
    const tagMatches = entry.tags.filter(tag => 
      tag.toLowerCase().includes(lowerQuery)
    ).length;
    score += tagMatches * 30;
    
    // Boost recent entries slightly
    const daysSinceUpdate = (Date.now() - entry.updatedAt.getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 10 - daysSinceUpdate);
    
    return score;
  }, []);

  // Perform search
  const performSearch = useCallback(() => {
    setIsSearching(true);

    try {
      let results: SearchResult[] = [];

      // Use enhanced localStorage search with timestamp support
      const searchResults = localStorageManager.searchEntries(
        query.trim(),
        filters.tags,
        {
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          timeFrom: filters.timeFrom,
          timeTo: filters.timeTo,
          searchInTimestamps: filters.searchInTimestamps
        }
      );

      if (query.trim()) {
        // Convert to SearchResult format with scoring
        results = searchResults
          .map(entry => {
            const score = calculateScore(entry, query);
            let matchType: 'title' | 'content' | 'tag' = 'content';
            let matchText = '';

            // Determine match type and extract match text
            if (entry.title.toLowerCase().includes(query.toLowerCase())) {
              matchType = 'title';
              matchText = entry.title;
            } else if (entry.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))) {
              matchType = 'tag';
              matchText = entry.tags.find(tag =>
                tag.toLowerCase().includes(query.toLowerCase())
              ) || '';
            } else if (filters.searchInTimestamps && entry.editHistory) {
              // Check if match is in timestamps
              const timestampMatch = entry.editHistory.find(edit => {
                const editDate = new Date(edit.timestamp);
                const dateStr = editDate.toLocaleDateString().toLowerCase();
                const timeStr = editDate.toLocaleTimeString().toLowerCase();
                const fullStr = `${dateStr} ${timeStr}`;
                return fullStr.includes(query.toLowerCase()) ||
                       dateStr.includes(query.toLowerCase()) ||
                       timeStr.includes(query.toLowerCase());
              });

              if (timestampMatch) {
                matchType = 'content';
                const editDate = new Date(timestampMatch.timestamp);
                matchText = `Edit on ${editDate.toLocaleDateString()} at ${editDate.toLocaleTimeString()}`;
              } else {
                matchType = 'content';
                // Extract snippet around the match
                const contentLower = entry.content.toLowerCase();
                const queryLower = query.toLowerCase();
                const matchIndex = contentLower.indexOf(queryLower);
                if (matchIndex !== -1) {
                  const start = Math.max(0, matchIndex - 50);
                  const end = Math.min(entry.content.length, matchIndex + query.length + 50);
                  matchText = entry.content.slice(start, end);
                  if (start > 0) matchText = '...' + matchText;
                  if (end < entry.content.length) matchText = matchText + '...';
                }
              }
            } else {
              matchType = 'content';
              // Extract snippet around the match
              const contentLower = entry.content.toLowerCase();
              const queryLower = query.toLowerCase();
              const matchIndex = contentLower.indexOf(queryLower);
              if (matchIndex !== -1) {
                const start = Math.max(0, matchIndex - 50);
                const end = Math.min(entry.content.length, matchIndex + query.length + 50);
                matchText = entry.content.slice(start, end);
                if (start > 0) matchText = '...' + matchText;
                if (end < entry.content.length) matchText = matchText + '...';
              }
            }

            return {
              ...entry,
              matchType,
              matchText,
              score
            } as SearchResult;
          })
          .filter(result => result.score > 0);
      } else {
        // No query, return filtered entries
        results = searchResults.map(entry => ({
          ...entry,
          matchType: 'content' as const,
          matchText: '',
          score: 0
        }));
      }

      // Sort results (filtering is already done by localStorage search)
      results.sort((a, b) => {
        if (query.trim() && a.score !== b.score) {
          return b.score - a.score; // Sort by relevance first when searching
        }

        const sortBy = filters.sortBy || 'updatedAt';
        const sortOrder = filters.sortOrder || 'desc';

        let aValue: any = a[sortBy];
        let bValue: any = b[sortBy];

        if (sortBy === 'createdAt' || sortBy === 'updatedAt' || sortBy === 'lastEditedAt') {
          aValue = aValue.getTime();
          bValue = bValue.getTime();
        }

        if (sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
      
      onSearchResults(results);
      
      // Save to search history
      if (query.trim()) {
        saveSearchHistory(query);
      }
      
    } finally {
      setIsSearching(false);
    }
  }, [query, filters, entries, calculateScore, onSearchResults, saveSearchHistory]);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(performSearch, 300);
    return () => clearTimeout(timer);
  }, [performSearch]);

  // Handle filter changes
  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('');
    setFilters({
      query: '',
      tags: [],
      dateFrom: undefined,
      dateTo: undefined,
      timeFrom: '',
      timeTo: '',
      searchInTimestamps: false,
      sortBy: 'updatedAt',
      sortOrder: 'desc'
    });
    onSearchResults(entries);
  }, [entries, onSearchResults]);

  // Handle search history selection
  const selectFromHistory = useCallback((historyQuery: string) => {
    setQuery(historyQuery);
    setShowHistory(false);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {
        setShowFilters(false);
      }
      if (historyRef.current && !historyRef.current.contains(event.target as Node)) {
        setShowHistory(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search input */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            ref={searchInputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => setShowHistory(searchHistory.length > 0 && !query)}
            placeholder="Search entries, tags, or content..."
            className="w-full pl-10 pr-20 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {query && (
              <button
                onClick={clearSearch}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-1 transition-colors ${
                showFilters || filters.tags?.length || filters.dateFrom || filters.dateTo
                  ? 'text-blue-600 dark:text-blue-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
            >
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Search history dropdown */}
        {showHistory && searchHistory.length > 0 && (
          <div
            ref={historyRef}
            className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto"
          >
            <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
              Recent searches
            </div>
            {searchHistory.map((historyQuery, index) => (
              <button
                key={index}
                onClick={() => selectFromHistory(historyQuery)}
                className="w-full px-3 py-2 text-left text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                {historyQuery}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Filters */}
      {showFilters && (
        <div
          ref={filtersRef}
          className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 space-y-4"
        >
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">Filters</h3>
            <button
              onClick={() => setShowFilters(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tag filter */}
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Tags
              </label>
              <select
                multiple
                value={filters.tags || []}
                onChange={(e) => updateFilter('tags', Array.from(e.target.selectedOptions, option => option.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                size={3}
              >
                {tags.map(tag => (
                  <option key={tag.id} value={tag.name}>
                    {tag.name} ({tag.usageCount})
                  </option>
                ))}
              </select>
            </div>

            {/* Sort options */}
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                Sort by
              </label>
              <div className="flex space-x-2">
                <select
                  value={filters.sortBy || 'updatedAt'}
                  onChange={(e) => updateFilter('sortBy', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                >
                  <option value="updatedAt">Last Modified</option>
                  <option value="lastEditedAt">Last Edited</option>
                  <option value="createdAt">Created Date</option>
                  <option value="title">Title</option>
                  <option value="wordCount">Word Count</option>
                </select>
                
                <button
                  onClick={() => updateFilter('sortOrder', filters.sortOrder === 'desc' ? 'asc' : 'desc')}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  {filters.sortOrder === 'desc' ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />}
                </button>
              </div>
            </div>
          </div>

          {/* Date range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                From Date
              </label>
              <input
                type="date"
                value={filters.dateFrom ? filters.dateFrom.toISOString().split('T')[0] : ''}
                onChange={(e) => updateFilter('dateFrom', e.target.value ? new Date(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                To Date
              </label>
              <input
                type="date"
                value={filters.dateTo ? filters.dateTo.toISOString().split('T')[0] : ''}
                onChange={(e) => updateFilter('dateTo', e.target.value ? new Date(e.target.value) : undefined)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>
          </div>

          {/* Time range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                From Time
              </label>
              <input
                type="time"
                value={filters.timeFrom || ''}
                onChange={(e) => updateFilter('timeFrom', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                To Time
              </label>
              <input
                type="time"
                value={filters.timeTo || ''}
                onChange={(e) => updateFilter('timeTo', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>
          </div>

          {/* Timestamp search option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="searchInTimestamps"
              checked={filters.searchInTimestamps || false}
              onChange={(e) => updateFilter('searchInTimestamps', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="searchInTimestamps" className="flex items-center text-sm text-gray-700 dark:text-gray-300">
              <Clock className="w-4 h-4 mr-1" />
              Search in edit timestamps
            </label>
          </div>
        </div>
      )}

      {/* Search status */}
      {isSearching && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">Searching...</span>
        </div>
      )}
    </div>
  );
}
