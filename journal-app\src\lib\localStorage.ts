import { JournalEntry, Tag, UserPreferences } from './types';

// Local storage keys
const STORAGE_KEYS = {
  ENTRIES: 'journal_entries',
  TAGS: 'journal_tags',
  PREFERENCES: 'journal_preferences',
  DRAFT: 'journal_draft',
  PENDING_SYNC: 'journal_pending_sync',
  LAST_SYNC: 'journal_last_sync'
} as const;

// Local storage utilities for offline functionality
export class LocalStorageManager {
  
  // Check if localStorage is available
  private isLocalStorageAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  // Generic storage methods
  private setItem<T>(key: string, value: T): void {
    if (!this.isLocalStorageAvailable()) return;
    
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  }

  private getItem<T>(key: string): T | null {
    if (!this.isLocalStorageAvailable()) return null;
    
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Failed to read from localStorage:', error);
      return null;
    }
  }

  private removeItem(key: string): void {
    if (!this.isLocalStorageAvailable()) return;
    
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
    }
  }

  // Journal entries management
  saveEntries(entries: JournalEntry[]): void {
    this.setItem(STORAGE_KEYS.ENTRIES, entries);
  }

  getEntries(): JournalEntry[] {
    const entries = this.getItem<JournalEntry[]>(STORAGE_KEYS.ENTRIES);

    // Ensure we always return an array
    if (Array.isArray(entries)) {
      return entries;
    }

    // If entries is not an array, return empty array and clear the corrupted data
    if (entries !== null) {
      console.warn('Corrupted entries data found, clearing localStorage');
      this.removeItem(STORAGE_KEYS.ENTRIES);
    }

    return [];
  }

  saveEntry(entry: JournalEntry): void {
    const entries = this.getEntries();

    // Ensure entries is always an array
    const entriesArray = Array.isArray(entries) ? entries : [];
    const existingIndex = entriesArray.findIndex(e => e.id === entry.id);

    if (existingIndex >= 0) {
      entriesArray[existingIndex] = entry;
    } else {
      entriesArray.unshift(entry); // Add new entries at the beginning
    }

    this.saveEntries(entriesArray);
    this.markForSync(entry.id);
  }

  deleteEntry(entryId: string): void {
    const entries = this.getEntries();

    // Ensure entries is always an array
    const entriesArray = Array.isArray(entries) ? entries : [];
    const filteredEntries = entriesArray.filter(e => e.id !== entryId);
    this.saveEntries(filteredEntries);
    this.markForSync(entryId, 'delete');
  }

  getEntry(entryId: string): JournalEntry | null {
    const entries = this.getEntries();

    // Ensure entries is always an array
    const entriesArray = Array.isArray(entries) ? entries : [];
    return entriesArray.find(e => e.id === entryId) || null;
  }

  // Tags management
  saveTags(tags: Tag[]): void {
    this.setItem(STORAGE_KEYS.TAGS, tags);
  }

  getTags(): Tag[] {
    const tags = this.getItem<Tag[]>(STORAGE_KEYS.TAGS);

    // Ensure we always return an array
    if (Array.isArray(tags)) {
      return tags;
    }

    // If tags is not an array, return empty array and clear the corrupted data
    if (tags !== null) {
      console.warn('Corrupted tags data found, clearing localStorage');
      this.removeItem(STORAGE_KEYS.TAGS);
    }

    return [];
  }

  saveTag(tag: Tag): void {
    const tags = this.getTags();
    const existingIndex = tags.findIndex(t => t.id === tag.id);
    
    if (existingIndex >= 0) {
      tags[existingIndex] = tag;
    } else {
      tags.push(tag);
    }
    
    this.saveTags(tags);
  }

  // User preferences management
  savePreferences(preferences: UserPreferences): void {
    this.setItem(STORAGE_KEYS.PREFERENCES, preferences);
  }

  getPreferences(): UserPreferences | null {
    return this.getItem<UserPreferences>(STORAGE_KEYS.PREFERENCES);
  }

  // Draft management for auto-save
  saveDraft(entryId: string, content: string): void {
    const drafts = this.getDrafts();
    drafts[entryId] = {
      content,
      timestamp: Date.now()
    };
    this.setItem(STORAGE_KEYS.DRAFT, drafts);
  }

  getDraft(entryId: string): { content: string; timestamp: number } | null {
    const drafts = this.getDrafts();
    return drafts[entryId] || null;
  }

  clearDraft(entryId: string): void {
    const drafts = this.getDrafts();
    delete drafts[entryId];
    this.setItem(STORAGE_KEYS.DRAFT, drafts);
  }

  private getDrafts(): Record<string, { content: string; timestamp: number }> {
    return this.getItem<Record<string, { content: string; timestamp: number }>>(STORAGE_KEYS.DRAFT) || {};
  }

  // Sync management
  markForSync(entryId: string, action: 'create' | 'update' | 'delete' = 'update'): void {
    const pendingSync = this.getPendingSync();
    pendingSync[entryId] = {
      action,
      timestamp: Date.now()
    };
    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
  }

  getPendingSync(): Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }> {
    return this.getItem<Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }>>(STORAGE_KEYS.PENDING_SYNC) || {};
  }

  clearPendingSync(entryId: string): void {
    const pendingSync = this.getPendingSync();
    delete pendingSync[entryId];
    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);
  }

  setLastSyncTime(timestamp: number): void {
    this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);
  }

  getLastSyncTime(): number | null {
    return this.getItem<number>(STORAGE_KEYS.LAST_SYNC);
  }

  // Search functionality
  searchEntries(query: string, tags?: string[]): JournalEntry[] {
    const entries = this.getEntries();

    // Ensure entries is always an array
    const entriesArray = Array.isArray(entries) ? entries : [];
    const lowercaseQuery = query.toLowerCase();

    return entriesArray.filter(entry => {
      // Text search
      const titleMatch = entry.title.toLowerCase().includes(lowercaseQuery);
      const contentMatch = entry.content.toLowerCase().includes(lowercaseQuery);
      const textMatch = titleMatch || contentMatch;

      // Tag filter
      const tagMatch = !tags || tags.length === 0 || 
        tags.every(tag => entry.tags.includes(tag));

      return textMatch && tagMatch;
    }).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  // Utility methods
  getStorageUsage(): { used: number; available: number } {
    if (!this.isLocalStorageAvailable()) {
      return { used: 0, available: 0 };
    }

    let used = 0;
    for (const key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length;
      }
    }

    // Estimate available space (most browsers have ~5-10MB limit)
    const estimated = 5 * 1024 * 1024; // 5MB
    return {
      used,
      available: Math.max(0, estimated - used)
    };
  }

  clearAllData(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      this.removeItem(key);
    });
  }

  // Export/Import functionality
  exportData(): {
    entries: JournalEntry[];
    tags: Tag[];
    preferences: UserPreferences | null;
    exportDate: string;
  } {
    return {
      entries: this.getEntries(),
      tags: this.getTags(),
      preferences: this.getPreferences(),
      exportDate: new Date().toISOString()
    };
  }

  importData(data: {
    entries?: JournalEntry[];
    tags?: Tag[];
    preferences?: UserPreferences;
  }): void {
    if (data.entries) {
      this.saveEntries(data.entries);
    }
    if (data.tags) {
      this.saveTags(data.tags);
    }
    if (data.preferences) {
      this.savePreferences(data.preferences);
    }
  }

  // Auto-cleanup old drafts (older than 7 days)
  cleanupOldDrafts(): void {
    const drafts = this.getDrafts();
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    let hasChanges = false;
    Object.keys(drafts).forEach(entryId => {
      if (drafts[entryId].timestamp < sevenDaysAgo) {
        delete drafts[entryId];
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.setItem(STORAGE_KEYS.DRAFT, drafts);
    }
  }
}

// Create a singleton instance
export const localStorageManager = new LocalStorageManager();

// Auto-cleanup on initialization
localStorageManager.cleanupOldDrafts();
