import { JournalEntry } from './database';

const STORAGE_KEYS = {
  ENTRIES: 'journal_entries',
  DRAFTS: 'journal_drafts',
  SETTINGS: 'journal_settings',
  TAGS: 'journal_tags',
} as const;

export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  fontSize: number;
  fontFamily: string;
  autoSave: boolean;
  autoSaveInterval: number; // in milliseconds
  spellCheck: boolean;
}

export interface Draft {
  id: string;
  title: string;
  content: string;
  tags: string[];
  lastModified: string;
}

class LocalStorageManager {
  // Settings management
  getSettings(): UserSettings {
    console.log('getSettings called');
    if (typeof window === 'undefined') {
      console.log('Window undefined, returning defaults');
      return this.getDefaultSettings();
    }

    try {
      console.log('Getting settings from localStorage...');
      const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);
      console.log('Stored settings:', stored);
      if (stored) {
        const parsed = JSON.parse(stored);
        const result = { ...this.getDefaultSettings(), ...parsed };
        console.log('Merged settings:', result);
        return result;
      }
    } catch (error) {
      console.error('Error loading settings from localStorage:', error);
    }
    const defaults = this.getDefaultSettings();
    console.log('Returning default settings:', defaults);
    return defaults;
  }

  saveSettings(settings: Partial<UserSettings>): void {
    if (typeof window === 'undefined') return;

    try {
      const current = this.getSettings();
      const updated = { ...current, ...settings };
      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updated));
    } catch (error) {
      console.error('Error saving settings to localStorage:', error);
    }
  }

  private getDefaultSettings(): UserSettings {
    return {
      theme: 'system',
      fontSize: 16,
      fontFamily: 'Inter, system-ui, sans-serif',
      autoSave: true,
      autoSaveInterval: 2000, // 2 seconds
      spellCheck: true,
    };
  }

  // Draft management (for unsaved entries)
  saveDraft(draft: Draft): void {
    if (typeof window === 'undefined') return;

    try {
      const drafts = this.getDrafts();
      const existingIndex = drafts.findIndex(d => d.id === draft.id);

      if (existingIndex >= 0) {
        drafts[existingIndex] = draft;
      } else {
        drafts.push(draft);
      }

      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));
    } catch (error) {
      console.error('Error saving draft to localStorage:', error);
    }
  }

  getDrafts(): Draft[] {
    if (typeof window === 'undefined') return [];

    try {
      const stored = localStorage.getItem(STORAGE_KEYS.DRAFTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading drafts from localStorage:', error);
      return [];
    }
  }

  getDraft(id: string): Draft | null {
    const drafts = this.getDrafts();
    return drafts.find(d => d.id === id) || null;
  }

  deleteDraft(id: string): void {
    try {
      const drafts = this.getDrafts().filter(d => d.id !== id);
      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));
    } catch (error) {
      console.error('Error deleting draft from localStorage:', error);
    }
  }

  // Entry caching (for offline access)
  cacheEntries(entries: JournalEntry[]): void {
    try {
      const cached = {
        entries,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
    } catch (error) {
      console.error('Error caching entries to localStorage:', error);
    }
  }

  getCachedEntries(): { entries: JournalEntry[]; lastUpdated: string } | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error loading cached entries from localStorage:', error);
      return null;
    }
  }

  cacheEntry(entry: JournalEntry): void {
    try {
      const cached = this.getCachedEntries();
      if (cached) {
        const existingIndex = cached.entries.findIndex(e => e.id === entry.id);
        if (existingIndex >= 0) {
          cached.entries[existingIndex] = entry;
        } else {
          cached.entries.unshift(entry);
        }
        cached.lastUpdated = new Date().toISOString();
        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
      } else {
        this.cacheEntries([entry]);
      }
    } catch (error) {
      console.error('Error caching entry to localStorage:', error);
    }
  }

  removeCachedEntry(id: string): void {
    try {
      const cached = this.getCachedEntries();
      if (cached) {
        cached.entries = cached.entries.filter(e => e.id !== id);
        cached.lastUpdated = new Date().toISOString();
        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));
      }
    } catch (error) {
      console.error('Error removing cached entry from localStorage:', error);
    }
  }

  // Tag caching
  cacheTags(tags: string[]): void {
    try {
      const cached = {
        tags,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));
    } catch (error) {
      console.error('Error caching tags to localStorage:', error);
    }
  }

  getCachedTags(): string[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.TAGS);
      const cached = stored ? JSON.parse(stored) : null;
      return cached ? cached.tags : [];
    } catch (error) {
      console.error('Error loading cached tags from localStorage:', error);
      return [];
    }
  }

  // Utility methods
  clearAllData(): void {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  getStorageUsage(): { used: number; available: number } {
    try {
      let used = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          used += localStorage.getItem(key)?.length || 0;
        }
      }

      // Estimate available space (most browsers have ~5-10MB limit)
      const available = 5 * 1024 * 1024 - used; // Assume 5MB limit
      
      return { used, available };
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return { used: 0, available: 0 };
    }
  }

  // Auto-save functionality
  createAutoSaveTimer(callback: () => void, interval?: number): NodeJS.Timeout {
    const settings = this.getSettings();
    const saveInterval = interval || settings.autoSaveInterval;
    
    return setInterval(callback, saveInterval);
  }

  // Export/Import functionality
  exportData(): string {
    try {
      const data = {
        entries: this.getCachedEntries(),
        drafts: this.getDrafts(),
        settings: this.getSettings(),
        tags: this.getCachedTags(),
        exportDate: new Date().toISOString(),
      };
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Error exporting data:', error);
      throw new Error('Failed to export data');
    }
  }

  importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.entries) {
        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));
      }
      
      if (data.drafts) {
        localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));
      }
      
      if (data.settings) {
        localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));
      }
      
      if (data.tags) {
        localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));
      }
    } catch (error) {
      console.error('Error importing data:', error);
      throw new Error('Failed to import data');
    }
  }
}

// Singleton instance
let storageInstance: LocalStorageManager | null = null;

export function getLocalStorage(): LocalStorageManager {
  if (!storageInstance) {
    storageInstance = new LocalStorageManager();
  }
  return storageInstance;
}

export default LocalStorageManager;
