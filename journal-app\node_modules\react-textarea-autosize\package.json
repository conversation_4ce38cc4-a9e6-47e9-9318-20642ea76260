{"name": "react-textarea-autosize", "description": "textarea component for React which grows with content", "version": "8.5.9", "keywords": ["autosize", "grow", "react", "react-component", "textarea"], "repository": {"type": "git", "url": "git+https://github.com/Andarist/react-textarea-autosize.git"}, "license": "MIT", "main": "dist/react-textarea-autosize.cjs.js", "module": "dist/react-textarea-autosize.esm.js", "browser": {"./dist/react-textarea-autosize.esm.js": "./dist/react-textarea-autosize.browser.esm.js"}, "exports": {".": {"types": {"import": "./dist/react-textarea-autosize.cjs.mjs", "default": "./dist/react-textarea-autosize.cjs.js"}, "development": {"edge-light": {"module": "./dist/react-textarea-autosize.development.edge-light.esm.js", "import": "./dist/react-textarea-autosize.development.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.development.edge-light.cjs.js"}, "worker": {"module": "./dist/react-textarea-autosize.development.edge-light.esm.js", "import": "./dist/react-textarea-autosize.development.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.development.edge-light.cjs.js"}, "workerd": {"module": "./dist/react-textarea-autosize.development.edge-light.esm.js", "import": "./dist/react-textarea-autosize.development.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.development.edge-light.cjs.js"}, "browser": {"module": "./dist/react-textarea-autosize.browser.development.esm.js", "import": "./dist/react-textarea-autosize.browser.development.cjs.mjs", "default": "./dist/react-textarea-autosize.browser.development.cjs.js"}, "module": "./dist/react-textarea-autosize.development.esm.js", "import": "./dist/react-textarea-autosize.development.cjs.mjs", "default": "./dist/react-textarea-autosize.development.cjs.js"}, "edge-light": {"module": "./dist/react-textarea-autosize.edge-light.esm.js", "import": "./dist/react-textarea-autosize.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.edge-light.cjs.js"}, "worker": {"module": "./dist/react-textarea-autosize.edge-light.esm.js", "import": "./dist/react-textarea-autosize.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.edge-light.cjs.js"}, "workerd": {"module": "./dist/react-textarea-autosize.edge-light.esm.js", "import": "./dist/react-textarea-autosize.edge-light.cjs.mjs", "default": "./dist/react-textarea-autosize.edge-light.cjs.js"}, "browser": {"module": "./dist/react-textarea-autosize.browser.esm.js", "import": "./dist/react-textarea-autosize.browser.cjs.mjs", "default": "./dist/react-textarea-autosize.browser.cjs.js"}, "module": "./dist/react-textarea-autosize.esm.js", "import": "./dist/react-textarea-autosize.cjs.mjs", "default": "./dist/react-textarea-autosize.cjs.js"}, "./package.json": "./package.json"}, "imports": {"#is-development": {"development": "./src/conditions/true.ts", "default": "./src/conditions/false.ts"}, "#is-browser": {"edge-light": "./src/conditions/false.ts", "workerd": "./src/conditions/false.ts", "worker": "./src/conditions/false.ts", "browser": "./src/conditions/true.ts", "default": "./src/conditions/is-browser.ts"}}, "sideEffects": false, "files": ["dist"], "author": "<PERSON><PERSON> <<EMAIL>> (httsps://andreypopp.com/)", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)"], "scripts": {"build": "preconstruct build", "docs:dev": "npm run dev --prefix example", "docs:build": "npm run build --prefix example", "docs:publish": "npm run docs:build && cd ./example/dist && git init && git commit --allow-empty -m 'update docs' && git checkout -b gh-pages && touch .nojekyll && git add . && git commit -am 'update docs' && <NAME_EMAIL>:Andarist/react-textarea-autosize gh-pages --force", "lint": "eslint --ext .js,.ts,.tsx src", "prepare": "npm run build", "changeset": "changeset", "prerelease": "npm run lint && npm test", "release": "changeset publish", "test": "jest", "test:watch": "npm test -- --watch"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"@babel/runtime": "^7.20.13", "use-composed-ref": "^1.3.0", "use-latest": "^1.2.1"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@changesets/changelog-github": "^0.4.4", "@changesets/cli": "^2.22.0", "@preconstruct/cli": "^2.8.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^10.4.9", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "@vitejs/plugin-react": "^4.3.4", "babel-eslint": "11.0.0-beta.2", "bytes": "^3.1.0", "cross-env": "^7.0.2", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "husky": "^4.2.5", "jest": "^29.4.2", "jest-environment-jsdom": "^29.4.2", "lint-staged": "^10.2.8", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "typescript": "^5.1.3", "vite": "^6.0.7"}, "engines": {"node": ">=10"}, "packageManager": "yarn@3.1.1", "preconstruct": {"exports": {"importConditionDefaultExport": "default"}, "___experimentalFlags_WILL_CHANGE_IN_PATCH": {"importsConditions": true}}}