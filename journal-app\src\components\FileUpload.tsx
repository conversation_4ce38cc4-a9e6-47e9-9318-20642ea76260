'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, File, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';
import mammoth from 'mammoth';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  content: string;
  title: string;
  status: 'processing' | 'success' | 'error';
  error?: string;
}

interface FileUploadProps {
  onFilesProcessed: (files: UploadedFile[]) => void;
  onImport: (file: UploadedFile) => void;
  className?: string;
}

export function FileUpload({ onFilesProcessed, onImport, className = '' }: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const processTextFile = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        resolve(content);
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const processDocxFile = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          const result = await mammoth.extractRawText({ arrayBuffer });
          resolve(result.value);
        } catch (error) {
          reject(new Error('Failed to process DOCX file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  };

  const extractTitle = (content: string, filename: string): string => {
    // Try to extract title from first line
    const lines = content.trim().split('\n');
    const firstLine = lines[0]?.trim();
    
    if (firstLine && firstLine.length > 0 && firstLine.length < 100) {
      // Remove common title markers
      const cleanTitle = firstLine
        .replace(/^#+\s*/, '') // Remove markdown headers
        .replace(/^Title:\s*/i, '') // Remove "Title:" prefix
        .replace(/^Subject:\s*/i, '') // Remove "Subject:" prefix
        .trim();
      
      if (cleanTitle.length > 0) {
        return cleanTitle;
      }
    }
    
    // Fallback to filename without extension
    return filename.replace(/\.[^/.]+$/, '');
  };

  const processFile = async (file: File): Promise<UploadedFile> => {
    const id = crypto.randomUUID();
    const uploadedFile: UploadedFile = {
      id,
      name: file.name,
      size: file.size,
      type: file.type,
      content: '',
      title: '',
      status: 'processing',
    };

    try {
      let content: string;

      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        content = await processTextFile(file);
      } else if (
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.name.endsWith('.docx')
      ) {
        content = await processDocxFile(file);
      } else {
        throw new Error('Unsupported file type. Please upload .txt or .docx files.');
      }

      const title = extractTitle(content, file.name);

      return {
        ...uploadedFile,
        content: content.trim(),
        title,
        status: 'success',
      };
    } catch (error) {
      return {
        ...uploadedFile,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setIsProcessing(true);
    
    const processedFiles: UploadedFile[] = [];
    
    for (const file of acceptedFiles) {
      const processed = await processFile(file);
      processedFiles.push(processed);
      
      // Update state incrementally
      setUploadedFiles(prev => [...prev, processed]);
    }
    
    setIsProcessing(false);
    onFilesProcessed(processedFiles);
  }, [onFilesProcessed]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    multiple: true,
  });

  const removeFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== id));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string, name: string) => {
    if (type === 'text/plain' || name.endsWith('.txt')) {
      return <FileText className="w-5 h-5" />;
    }
    return <File className="w-5 h-5" />;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
        `}
      >
        <input {...getInputProps()} />
        <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          {isDragActive ? 'Drop files here' : 'Upload your writings'}
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-sm text-gray-400 dark:text-gray-500">
          Supports: .txt, .docx files
        </p>
      </div>

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3" />
          <span className="text-blue-700 dark:text-blue-300">Processing files...</span>
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Uploaded Files ({uploadedFiles.length})
          </h3>
          
          <div className="space-y-3">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-3 flex-1">
                  <div className="text-gray-500 dark:text-gray-400">
                    {getFileIcon(file.type, file.name)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {file.title || file.name}
                      </h4>
                      {getStatusIcon(file.status)}
                    </div>
                    
                    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>{file.name}</span>
                      <span>•</span>
                      <span>{formatFileSize(file.size)}</span>
                      {file.content && (
                        <>
                          <span>•</span>
                          <span>{file.content.split(/\s+/).length} words</span>
                        </>
                      )}
                    </div>
                    
                    {file.error && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {file.error}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {file.status === 'success' && (
                    <button
                      onClick={() => onImport(file)}
                      className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      Import
                    </button>
                  )}
                  
                  <button
                    onClick={() => removeFile(file.id)}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
