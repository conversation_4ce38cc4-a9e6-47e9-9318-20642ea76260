'use client';

import React, { useState, useRef, useCallback } from 'react';
import { JournalEntry } from '@/lib/types';
import { localStorageManager } from '@/lib/localStorage';
import { Upload, File, X, Check, AlertCircle, FileText } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

interface FileUploadProps {
  onEntriesImported: (entries: JournalEntry[]) => void;
  className?: string;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
}

interface UploadedFile {
  id: string;
  file: File;
  content: string;
  status: 'processing' | 'success' | 'error';
  error?: string;
  entry?: JournalEntry;
}

export default function FileUpload({
  onEntriesImported,
  className = '',
  maxFileSize = 10,
  acceptedTypes = ['.txt', '.md', '.rtf', '.doc', '.docx']
}: FileUploadProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Extract text content from different file types
  const extractTextContent = useCallback(async (file: File): Promise<string> => {
    const fileExtension = file.name.toLowerCase().split('.').pop();
    
    switch (fileExtension) {
      case 'txt':
      case 'md':
      case 'rtf':
        return await file.text();
      
      case 'doc':
      case 'docx':
        // For now, we'll just read as text. In a real app, you'd use a library like mammoth.js
        try {
          return await file.text();
        } catch {
          throw new Error('Unable to read Word document. Please save as .txt or .md format.');
        }
      
      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }
  }, []);

  // Process uploaded files
  const processFiles = useCallback(async (files: FileList) => {
    setIsProcessing(true);
    const newUploadedFiles: UploadedFile[] = [];

    for (const file of Array.from(files)) {
      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        newUploadedFiles.push({
          id: uuidv4(),
          file,
          content: '',
          status: 'error',
          error: `File size exceeds ${maxFileSize}MB limit`
        });
        continue;
      }

      // Check file type
      const fileExtension = '.' + file.name.toLowerCase().split('.').pop();
      if (!acceptedTypes.includes(fileExtension)) {
        newUploadedFiles.push({
          id: uuidv4(),
          file,
          content: '',
          status: 'error',
          error: `Unsupported file type. Accepted types: ${acceptedTypes.join(', ')}`
        });
        continue;
      }

      const uploadedFile: UploadedFile = {
        id: uuidv4(),
        file,
        content: '',
        status: 'processing'
      };

      newUploadedFiles.push(uploadedFile);

      try {
        // Extract text content
        const content = await extractTextContent(file);
        
        // Create journal entry
        const entry: JournalEntry = {
          id: uuidv4(),
          title: file.name.replace(/\.[^/.]+$/, ''), // Remove file extension
          content: content.trim(),
          tags: ['imported'],
          createdAt: new Date(file.lastModified || Date.now()),
          updatedAt: new Date(),
          wordCount: content.trim().split(/\s+/).filter(word => word.length > 0).length
        };

        // Update the uploaded file with success status
        uploadedFile.content = content;
        uploadedFile.status = 'success';
        uploadedFile.entry = entry;

      } catch (error) {
        uploadedFile.status = 'error';
        uploadedFile.error = error instanceof Error ? error.message : 'Failed to process file';
      }
    }

    setUploadedFiles(prev => [...prev, ...newUploadedFiles]);
    setIsProcessing(false);
  }, [maxFileSize, acceptedTypes, extractTextContent]);

  // Handle file input change
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [processFiles]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  }, [processFiles]);

  // Import successful entries
  const handleImportEntries = useCallback(() => {
    const successfulEntries = uploadedFiles
      .filter(file => file.status === 'success' && file.entry)
      .map(file => file.entry!);

    if (successfulEntries.length > 0) {
      // Save to localStorage
      successfulEntries.forEach(entry => {
        localStorageManager.saveEntry(entry);
      });

      // Call parent callback
      onEntriesImported(successfulEntries);

      // Clear uploaded files
      setUploadedFiles([]);
    }
  }, [uploadedFiles, onEntriesImported]);

  // Remove uploaded file
  const removeUploadedFile = useCallback((fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  }, []);

  // Clear all uploaded files
  const clearAllFiles = useCallback(() => {
    setUploadedFiles([]);
  }, []);

  const successfulFiles = uploadedFiles.filter(file => file.status === 'success');
  const hasErrors = uploadedFiles.some(file => file.status === 'error');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
          ${isDragOver 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
        />
        
        <div className="space-y-4">
          <div className="flex justify-center">
            <Upload className="w-12 h-12 text-gray-400" />
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-white">
              Upload your writings
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Drag and drop files here, or click to browse
            </p>
          </div>
          
          <div className="text-xs text-gray-400 dark:text-gray-500">
            <p>Supported formats: {acceptedTypes.join(', ')}</p>
            <p>Maximum file size: {maxFileSize}MB</p>
          </div>
        </div>
      </div>

      {/* Processing indicator */}
      {isProcessing && (
        <div className="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
          <span className="text-blue-600 dark:text-blue-400">Processing files...</span>
        </div>
      )}

      {/* Uploaded files list */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Uploaded Files ({uploadedFiles.length})
            </h3>
            
            <div className="flex gap-2">
              {successfulFiles.length > 0 && (
                <button
                  onClick={handleImportEntries}
                  className="px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm"
                >
                  Import {successfulFiles.length} entries
                </button>
              )}
              
              <button
                onClick={clearAllFiles}
                className="px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm"
              >
                Clear all
              </button>
            </div>
          </div>

          <div className="space-y-2 max-h-60 overflow-y-auto">
            {uploadedFiles.map(uploadedFile => (
              <div
                key={uploadedFile.id}
                className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex-shrink-0">
                  {uploadedFile.status === 'processing' && (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                  )}
                  {uploadedFile.status === 'success' && (
                    <Check className="w-5 h-5 text-green-600" />
                  )}
                  {uploadedFile.status === 'error' && (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {uploadedFile.file.name}
                    </span>
                  </div>
                  
                  {uploadedFile.status === 'success' && uploadedFile.entry && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {uploadedFile.entry.wordCount} words • {uploadedFile.entry.tags.join(', ')}
                    </p>
                  )}
                  
                  {uploadedFile.status === 'error' && uploadedFile.error && (
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      {uploadedFile.error}
                    </p>
                  )}
                </div>

                <button
                  onClick={() => removeUploadedFile(uploadedFile.id)}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>

          {/* Summary */}
          {uploadedFiles.length > 0 && (
            <div className="text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-3">
              {successfulFiles.length} successful, {uploadedFiles.filter(f => f.status === 'error').length} failed
              {hasErrors && (
                <span className="text-red-600 dark:text-red-400 ml-2">
                  • Check error messages above
                </span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
