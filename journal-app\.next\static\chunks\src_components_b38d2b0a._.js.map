{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport TextareaAutosize from 'react-textarea-autosize';\nimport { JournalEntry, UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Save, Clock, Type } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface TextEditorProps {\n  entry?: JournalEntry;\n  onSave: (entry: JournalEntry) => void;\n  onAutoSave?: (content: string) => void;\n  preferences: UserPreferences;\n  className?: string;\n}\n\nexport default function TextEditor({ \n  entry, \n  onSave, \n  onAutoSave, \n  preferences, \n  className = '' \n}: TextEditorProps) {\n  const [title, setTitle] = useState(entry?.title || '');\n  const [content, setContent] = useState(entry?.content || '');\n  const [tags, setTags] = useState<string[]>(entry?.tags || []);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(entry?.updatedAt || null);\n  const [wordCount, setWordCount] = useState(0);\n  \n  const titleRef = useRef<HTMLInputElement>(null);\n  const contentRef = useRef<HTMLTextAreaElement>(null);\n  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();\n  const entryIdRef = useRef(entry?.id || uuidv4());\n\n  // Calculate word count\n  const calculateWordCount = useCallback((text: string) => {\n    return text.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  }, []);\n\n  // Auto-save functionality\n  const triggerAutoSave = useCallback(() => {\n    if (autoSaveTimeoutRef.current) {\n      clearTimeout(autoSaveTimeoutRef.current);\n    }\n\n    autoSaveTimeoutRef.current = setTimeout(() => {\n      if (content.trim() || title.trim()) {\n        // Save draft to localStorage\n        localStorageManager.saveDraft(entryIdRef.current, content);\n        \n        // Trigger auto-save callback if provided\n        if (onAutoSave) {\n          onAutoSave(content);\n        }\n\n        setLastSaved(new Date());\n      }\n    }, preferences.autoSaveInterval);\n  }, [content, title, preferences.autoSaveInterval, onAutoSave]);\n\n  // Handle content changes\n  const handleContentChange = useCallback((value: string) => {\n    setContent(value);\n    setWordCount(calculateWordCount(value));\n    triggerAutoSave();\n  }, [calculateWordCount, triggerAutoSave]);\n\n  // Handle title changes\n  const handleTitleChange = useCallback((value: string) => {\n    setTitle(value);\n    triggerAutoSave();\n  }, [triggerAutoSave]);\n\n  // Manual save\n  const handleSave = useCallback(async () => {\n    if (!title.trim() && !content.trim()) return;\n\n    setIsSaving(true);\n    \n    try {\n      const now = new Date();\n      const savedEntry: JournalEntry = {\n        id: entryIdRef.current,\n        title: title.trim() || 'Untitled',\n        content: content,\n        tags: tags,\n        createdAt: entry?.createdAt || now,\n        updatedAt: now,\n        wordCount: calculateWordCount(content)\n      };\n\n      // Save to localStorage\n      localStorageManager.saveEntry(savedEntry);\n      \n      // Clear draft\n      localStorageManager.clearDraft(entryIdRef.current);\n      \n      // Call parent save handler\n      onSave(savedEntry);\n      \n      setLastSaved(now);\n    } catch (error) {\n      console.error('Failed to save entry:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  }, [title, content, tags, entry, calculateWordCount, onSave]);\n\n  // Load draft on mount\n  useEffect(() => {\n    if (!entry) {\n      const draft = localStorageManager.getDraft(entryIdRef.current);\n      if (draft) {\n        setContent(draft.content);\n        setWordCount(calculateWordCount(draft.content));\n      }\n    }\n  }, [entry, calculateWordCount]);\n\n  // Update word count when content changes\n  useEffect(() => {\n    setWordCount(calculateWordCount(content));\n  }, [content, calculateWordCount]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleSave();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleSave]);\n\n  // Cleanup auto-save timeout\n  useEffect(() => {\n    return () => {\n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n    };\n  }, []);\n\n  const formatLastSaved = (date: Date | null) => {\n    if (!date) return 'Never';\n    \n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    \n    if (diff < 60000) return 'Just now';\n    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;\n    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;\n    \n    return date.toLocaleDateString();\n  };\n\n  return (\n    <div className={`flex flex-col h-full ${className}`}>\n      {/* Header with save status */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n          <Clock className=\"w-4 h-4\" />\n          <span>Last saved: {formatLastSaved(lastSaved)}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\n            <Type className=\"w-4 h-4\" />\n            <span>{wordCount} words</span>\n          </div>\n          \n          <button\n            onClick={handleSave}\n            disabled={isSaving || (!title.trim() && !content.trim())}\n            className=\"flex items-center space-x-2 px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <Save className=\"w-4 h-4\" />\n            <span>{isSaving ? 'Saving...' : 'Save'}</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Editor content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Title input */}\n        <div className=\"p-4 border-b border-gray-100 dark:border-gray-700\">\n          <input\n            ref={titleRef}\n            type=\"text\"\n            value={title}\n            onChange={(e) => handleTitleChange(e.target.value)}\n            placeholder=\"Enter a title...\"\n            className=\"w-full text-2xl font-bold bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none\"\n            style={{ fontSize: `${preferences.fontSize + 8}px` }}\n          />\n        </div>\n\n        {/* Content textarea */}\n        <div className=\"flex-1 p-4 overflow-auto\">\n          <TextareaAutosize\n            ref={contentRef}\n            value={content}\n            onChange={(e) => handleContentChange(e.target.value)}\n            placeholder=\"Start writing your thoughts...\"\n            className=\"w-full h-full bg-transparent border-none outline-none placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white resize-none leading-relaxed\"\n            style={{ \n              fontSize: `${preferences.fontSize}px`,\n              fontFamily: preferences.fontFamily,\n              minHeight: '200px'\n            }}\n            minRows={10}\n          />\n        </div>\n      </div>\n\n      {/* Mobile-friendly bottom toolbar */}\n      <div className=\"md:hidden p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n            {wordCount} words\n          </div>\n          \n          <button\n            onClick={handleSave}\n            disabled={isSaving || (!title.trim() && !content.trim())}\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <Save className=\"w-4 h-4\" />\n            <span>{isSaving ? 'Saving...' : 'Save'}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AACA;;;AAPA;;;;;;AAiBe,SAAS,WAAW,EACjC,KAAK,EACL,MAAM,EACN,UAAU,EACV,WAAW,EACX,YAAY,EAAE,EACE;;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAAS;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,WAAW;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,OAAO,QAAQ,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,OAAO,aAAa;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAC/C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAChC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;IAE5C,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;8DAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;6DAAG,MAAM;QACxE;qDAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAClC,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YAEA,mBAAmB,OAAO,GAAG;2DAAW;oBACtC,IAAI,QAAQ,IAAI,MAAM,MAAM,IAAI,IAAI;wBAClC,6BAA6B;wBAC7B,6HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC,WAAW,OAAO,EAAE;wBAElD,yCAAyC;wBACzC,IAAI,YAAY;4BACd,WAAW;wBACb;wBAEA,aAAa,IAAI;oBACnB;gBACF;0DAAG,YAAY,gBAAgB;QACjC;kDAAG;QAAC;QAAS;QAAO,YAAY,gBAAgB;QAAE;KAAW;IAE7D,yBAAyB;IACzB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACvC,WAAW;YACX,aAAa,mBAAmB;YAChC;QACF;sDAAG;QAAC;QAAoB;KAAgB;IAExC,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACrC,SAAS;YACT;QACF;oDAAG;QAAC;KAAgB;IAEpB,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC7B,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI;YAEtC,YAAY;YAEZ,IAAI;gBACF,MAAM,MAAM,IAAI;gBAChB,MAAM,aAA2B;oBAC/B,IAAI,WAAW,OAAO;oBACtB,OAAO,MAAM,IAAI,MAAM;oBACvB,SAAS;oBACT,MAAM;oBACN,WAAW,OAAO,aAAa;oBAC/B,WAAW;oBACX,WAAW,mBAAmB;gBAChC;gBAEA,uBAAuB;gBACvB,6HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;gBAE9B,cAAc;gBACd,6HAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC,WAAW,OAAO;gBAEjD,2BAA2B;gBAC3B,OAAO;gBAEP,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,YAAY;YACd;QACF;6CAAG;QAAC;QAAO;QAAS;QAAM;QAAO;QAAoB;KAAO;IAE5D,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,CAAC,OAAO;gBACV,MAAM,QAAQ,6HAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,WAAW,OAAO;gBAC7D,IAAI,OAAO;oBACT,WAAW,MAAM,OAAO;oBACxB,aAAa,mBAAmB,MAAM,OAAO;gBAC/C;YACF;QACF;+BAAG;QAAC;QAAO;KAAmB;IAE9B,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,aAAa,mBAAmB;QAClC;+BAAG;QAAC;QAAS;KAAmB;IAEhC,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;wCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;+BAAG;QAAC;KAAW;IAEf,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAO;oBACL,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,aAAa,mBAAmB,OAAO;oBACzC;gBACF;;QACF;+BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QAEzC,IAAI,OAAO,OAAO,OAAO;QACzB,IAAI,OAAO,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,OAAO,KAAK,CAAC;QAC7D,IAAI,OAAO,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,SAAS,KAAK,CAAC;QAEhE,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;;0BAEjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;;oCAAK;oCAAa,gBAAgB;;;;;;;;;;;;;kCAGrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;4CAAM;4CAAU;;;;;;;;;;;;;0CAGnB,6LAAC;gCACC,SAAS;gCACT,UAAU,YAAa,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;gCACrD,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4BACjD,aAAY;4BACZ,WAAU;4BACV,OAAO;gCAAE,UAAU,GAAG,YAAY,QAAQ,GAAG,EAAE,EAAE,CAAC;4BAAC;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0NAAA,CAAA,UAAgB;4BACf,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,aAAY;4BACZ,WAAU;4BACV,OAAO;gCACL,UAAU,GAAG,YAAY,QAAQ,CAAC,EAAE,CAAC;gCACrC,YAAY,YAAY,UAAU;gCAClC,WAAW;4BACb;4BACA,SAAS;;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ;gCAAU;;;;;;;sCAGb,6LAAC;4BACC,SAAS;4BACT,UAAU,YAAa,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI;4BACrD,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TagManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Tag } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { X, Plus, Hash, Palette } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface TagManagerProps {\n  tags: Tag[];\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  onCreateTag?: (tag: Tag) => void;\n  className?: string;\n  maxTags?: number;\n}\n\nconst TAG_COLORS = [\n  '#ef4444', // red\n  '#f97316', // orange\n  '#eab308', // yellow\n  '#22c55e', // green\n  '#06b6d4', // cyan\n  '#3b82f6', // blue\n  '#8b5cf6', // violet\n  '#ec4899', // pink\n  '#6b7280', // gray\n  '#84cc16', // lime\n];\n\nexport default function TagManager({\n  tags,\n  selectedTags,\n  onTagsChange,\n  onCreateTag,\n  className = '',\n  maxTags = 10\n}: TagManagerProps) {\n  const [isCreating, setIsCreating] = useState(false);\n  const [newTagName, setNewTagName] = useState('');\n  const [newTagColor, setNewTagColor] = useState(TAG_COLORS[0]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  \n  const inputRef = useRef<HTMLInputElement>(null);\n  const colorPickerRef = useRef<HTMLDivElement>(null);\n\n  // Filter tags based on search query\n  const filteredTags = tags.filter(tag =>\n    tag.name.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  // Get available tags (not already selected)\n  const availableTags = filteredTags.filter(tag =>\n    !selectedTags.includes(tag.name)\n  );\n\n  // Handle tag selection\n  const handleTagSelect = (tagName: string) => {\n    if (selectedTags.length >= maxTags) return;\n    \n    const newSelectedTags = [...selectedTags, tagName];\n    onTagsChange(newSelectedTags);\n    setSearchQuery('');\n  };\n\n  // Handle tag removal\n  const handleTagRemove = (tagName: string) => {\n    const newSelectedTags = selectedTags.filter(t => t !== tagName);\n    onTagsChange(newSelectedTags);\n  };\n\n  // Handle new tag creation\n  const handleCreateTag = () => {\n    const trimmedName = newTagName.trim();\n    if (!trimmedName) return;\n\n    // Check if tag already exists\n    const existingTag = tags.find(tag => \n      tag.name.toLowerCase() === trimmedName.toLowerCase()\n    );\n\n    if (existingTag) {\n      // If tag exists, just select it\n      handleTagSelect(existingTag.name);\n    } else {\n      // Create new tag\n      const newTag: Tag = {\n        id: uuidv4(),\n        name: trimmedName,\n        color: newTagColor,\n        createdAt: new Date(),\n        usageCount: 1\n      };\n\n      // Save to localStorage\n      localStorageManager.saveTag(newTag);\n\n      // Call parent callback\n      if (onCreateTag) {\n        onCreateTag(newTag);\n      }\n\n      // Select the new tag\n      handleTagSelect(newTag.name);\n    }\n\n    // Reset form\n    setNewTagName('');\n    setIsCreating(false);\n    setShowColorPicker(false);\n  };\n\n  // Handle input key events\n  const handleInputKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (isCreating) {\n        handleCreateTag();\n      } else if (availableTags.length > 0) {\n        handleTagSelect(availableTags[0].name);\n      }\n    } else if (e.key === 'Escape') {\n      setIsCreating(false);\n      setSearchQuery('');\n      setNewTagName('');\n    } else if (e.key === 'Backspace' && !searchQuery && !newTagName && selectedTags.length > 0) {\n      // Remove last selected tag when backspacing on empty input\n      handleTagRemove(selectedTags[selectedTags.length - 1]);\n    }\n  };\n\n  // Focus input when creating\n  useEffect(() => {\n    if (isCreating && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isCreating]);\n\n  // Close color picker when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (colorPickerRef.current && !colorPickerRef.current.contains(event.target as Node)) {\n        setShowColorPicker(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getTagColor = (tagName: string) => {\n    const tag = tags.find(t => t.name === tagName);\n    return tag?.color || TAG_COLORS[0];\n  };\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* Selected tags */}\n      {selectedTags.length > 0 && (\n        <div className=\"flex flex-wrap gap-2\">\n          {selectedTags.map(tagName => (\n            <span\n              key={tagName}\n              className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-sm font-medium text-white\"\n              style={{ backgroundColor: getTagColor(tagName) }}\n            >\n              <Hash className=\"w-3 h-3\" />\n              {tagName}\n              <button\n                onClick={() => handleTagRemove(tagName)}\n                className=\"ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors\"\n              >\n                <X className=\"w-3 h-3\" />\n              </button>\n            </span>\n          ))}\n        </div>\n      )}\n\n      {/* Tag input */}\n      <div className=\"relative\">\n        <div className=\"flex items-center gap-2 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800\">\n          <Hash className=\"w-4 h-4 text-gray-400\" />\n          \n          {isCreating ? (\n            <div className=\"flex-1 flex items-center gap-2\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={newTagName}\n                onChange={(e) => setNewTagName(e.target.value)}\n                onKeyDown={handleInputKeyDown}\n                placeholder=\"Enter tag name...\"\n                className=\"flex-1 bg-transparent outline-none text-gray-900 dark:text-white\"\n                maxLength={30}\n              />\n              \n              <div className=\"relative\" ref={colorPickerRef}>\n                <button\n                  onClick={() => setShowColorPicker(!showColorPicker)}\n                  className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  <div\n                    className=\"w-4 h-4 rounded-full border border-gray-300\"\n                    style={{ backgroundColor: newTagColor }}\n                  />\n                </button>\n                \n                {showColorPicker && (\n                  <div className=\"absolute top-full right-0 mt-1 p-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10\">\n                    <div className=\"grid grid-cols-5 gap-1\">\n                      {TAG_COLORS.map(color => (\n                        <button\n                          key={color}\n                          onClick={() => {\n                            setNewTagColor(color);\n                            setShowColorPicker(false);\n                          }}\n                          className=\"w-6 h-6 rounded-full border border-gray-300 hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: color }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n              \n              <button\n                onClick={handleCreateTag}\n                disabled={!newTagName.trim()}\n                className=\"px-2 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                Create\n              </button>\n              \n              <button\n                onClick={() => {\n                  setIsCreating(false);\n                  setNewTagName('');\n                  setShowColorPicker(false);\n                }}\n                className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            </div>\n          ) : (\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              onKeyDown={handleInputKeyDown}\n              placeholder={selectedTags.length >= maxTags ? `Max ${maxTags} tags reached` : \"Search or add tags...\"}\n              className=\"flex-1 bg-transparent outline-none text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500\"\n              disabled={selectedTags.length >= maxTags}\n            />\n          )}\n          \n          {!isCreating && selectedTags.length < maxTags && (\n            <button\n              onClick={() => setIsCreating(true)}\n              className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n              title=\"Create new tag\"\n            >\n              <Plus className=\"w-4 h-4\" />\n            </button>\n          )}\n        </div>\n\n        {/* Tag suggestions */}\n        {!isCreating && searchQuery && availableTags.length > 0 && (\n          <div className=\"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto\">\n            {availableTags.slice(0, 8).map(tag => (\n              <button\n                key={tag.id}\n                onClick={() => handleTagSelect(tag.name)}\n                className=\"w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: tag.color || TAG_COLORS[0] }}\n                />\n                <span className=\"text-gray-900 dark:text-white\">{tag.name}</span>\n                <span className=\"ml-auto text-xs text-gray-500 dark:text-gray-400\">\n                  {tag.usageCount} uses\n                </span>\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Tag limit indicator */}\n      {selectedTags.length > 0 && (\n        <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n          {selectedTags.length} / {maxTags} tags\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;AAiBA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,WAAW,EACjC,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,YAAY,EAAE,EACd,UAAU,EAAE,EACI;;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,oCAAoC;IACpC,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAGzD,4CAA4C;IAC5C,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,MACxC,CAAC,aAAa,QAAQ,CAAC,IAAI,IAAI;IAGjC,uBAAuB;IACvB,MAAM,kBAAkB,CAAC;QACvB,IAAI,aAAa,MAAM,IAAI,SAAS;QAEpC,MAAM,kBAAkB;eAAI;YAAc;SAAQ;QAClD,aAAa;QACb,eAAe;IACjB;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QACvD,aAAa;IACf;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB;QACtB,MAAM,cAAc,WAAW,IAAI;QACnC,IAAI,CAAC,aAAa;QAElB,8BAA8B;QAC9B,MAAM,cAAc,KAAK,IAAI,CAAC,CAAA,MAC5B,IAAI,IAAI,CAAC,WAAW,OAAO,YAAY,WAAW;QAGpD,IAAI,aAAa;YACf,gCAAgC;YAChC,gBAAgB,YAAY,IAAI;QAClC,OAAO;YACL,iBAAiB;YACjB,MAAM,SAAc;gBAClB,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,MAAM;gBACN,OAAO;gBACP,WAAW,IAAI;gBACf,YAAY;YACd;YAEA,uBAAuB;YACvB,6HAAA,CAAA,sBAAmB,CAAC,OAAO,CAAC;YAE5B,uBAAuB;YACvB,IAAI,aAAa;gBACf,YAAY;YACd;YAEA,qBAAqB;YACrB,gBAAgB,OAAO,IAAI;QAC7B;QAEA,aAAa;QACb,cAAc;QACd,cAAc;QACd,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,YAAY;gBACd;YACF,OAAO,IAAI,cAAc,MAAM,GAAG,GAAG;gBACnC,gBAAgB,aAAa,CAAC,EAAE,CAAC,IAAI;YACvC;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,cAAc;YACd,eAAe;YACf,cAAc;QAChB,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,eAAe,CAAC,cAAc,aAAa,MAAM,GAAG,GAAG;YAC1F,2DAA2D;YAC3D,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QACvD;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,cAAc,SAAS,OAAO,EAAE;gBAClC,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;+BAAG;QAAC;KAAW;IAEf,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;2DAAqB,CAAC;oBAC1B,IAAI,eAAe,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACpF,mBAAmB;oBACrB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;wCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;+BAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QACtC,OAAO,KAAK,SAAS,UAAU,CAAC,EAAE;IACpC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YAErC,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,wBAChB,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,YAAY;wBAAS;;0CAE/C,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf;0CACD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;uBAVV;;;;;;;;;;0BAkBb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAEf,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW;wCACX,aAAY;wCACZ,WAAU;wCACV,WAAW;;;;;;kDAGb,6LAAC;wCAAI,WAAU;wCAAW,KAAK;;0DAC7B,6LAAC;gDACC,SAAS,IAAM,mBAAmB,CAAC;gDACnC,WAAU;0DAEV,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAY;;;;;;;;;;;4CAIzC,iCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAA,sBACd,6LAAC;4DAEC,SAAS;gEACP,eAAe;gEACf,mBAAmB;4DACrB;4DACA,WAAU;4DACV,OAAO;gEAAE,iBAAiB;4DAAM;2DAN3B;;;;;;;;;;;;;;;;;;;;;kDAcjB,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI;wCAC1B,WAAU;kDACX;;;;;;kDAID,6LAAC;wCACC,SAAS;4CACP,cAAc;4CACd,cAAc;4CACd,mBAAmB;wCACrB;wCACA,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAIjB,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAW;gCACX,aAAa,aAAa,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,QAAQ,aAAa,CAAC,GAAG;gCAC9E,WAAU;gCACV,UAAU,aAAa,MAAM,IAAI;;;;;;4BAIpC,CAAC,cAAc,aAAa,MAAM,GAAG,yBACpC,6LAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMrB,CAAC,cAAc,eAAe,cAAc,MAAM,GAAG,mBACpD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,oBAC7B,6LAAC;gCAEC,SAAS,IAAM,gBAAgB,IAAI,IAAI;gCACvC,WAAU;;kDAEV,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,IAAI,KAAK,IAAI,UAAU,CAAC,EAAE;wCAAC;;;;;;kDAEvD,6LAAC;wCAAK,WAAU;kDAAiC,IAAI,IAAI;;;;;;kDACzD,6LAAC;wCAAK,WAAU;;4CACb,IAAI,UAAU;4CAAC;;;;;;;;+BAVb,IAAI,EAAE;;;;;;;;;;;;;;;;YAmBpB,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM;oBAAC;oBAAI;oBAAQ;;;;;;;;;;;;;AAK3C;GA/QwB;KAAA", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport { JournalEntry } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Upload, File, X, Check, AlertCircle, FileText } from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface FileUploadProps {\n  onEntriesImported: (entries: JournalEntry[]) => void;\n  className?: string;\n  maxFileSize?: number; // in MB\n  acceptedTypes?: string[];\n}\n\ninterface UploadedFile {\n  id: string;\n  file: File;\n  content: string;\n  status: 'processing' | 'success' | 'error';\n  error?: string;\n  entry?: JournalEntry;\n}\n\nexport default function FileUpload({\n  onEntriesImported,\n  className = '',\n  maxFileSize = 10,\n  acceptedTypes = ['.txt', '.md', '.rtf', '.doc', '.docx']\n}: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  \n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Extract text content from different file types\n  const extractTextContent = useCallback(async (file: File): Promise<string> => {\n    const fileExtension = file.name.toLowerCase().split('.').pop();\n    \n    switch (fileExtension) {\n      case 'txt':\n      case 'md':\n      case 'rtf':\n        return await file.text();\n      \n      case 'doc':\n      case 'docx':\n        // For now, we'll just read as text. In a real app, you'd use a library like mammoth.js\n        try {\n          return await file.text();\n        } catch {\n          throw new Error('Unable to read Word document. Please save as .txt or .md format.');\n        }\n      \n      default:\n        throw new Error(`Unsupported file type: ${fileExtension}`);\n    }\n  }, []);\n\n  // Process uploaded files\n  const processFiles = useCallback(async (files: FileList) => {\n    setIsProcessing(true);\n    const newUploadedFiles: UploadedFile[] = [];\n\n    for (const file of Array.from(files)) {\n      // Check file size\n      if (file.size > maxFileSize * 1024 * 1024) {\n        newUploadedFiles.push({\n          id: uuidv4(),\n          file,\n          content: '',\n          status: 'error',\n          error: `File size exceeds ${maxFileSize}MB limit`\n        });\n        continue;\n      }\n\n      // Check file type\n      const fileExtension = '.' + file.name.toLowerCase().split('.').pop();\n      if (!acceptedTypes.includes(fileExtension)) {\n        newUploadedFiles.push({\n          id: uuidv4(),\n          file,\n          content: '',\n          status: 'error',\n          error: `Unsupported file type. Accepted types: ${acceptedTypes.join(', ')}`\n        });\n        continue;\n      }\n\n      const uploadedFile: UploadedFile = {\n        id: uuidv4(),\n        file,\n        content: '',\n        status: 'processing'\n      };\n\n      newUploadedFiles.push(uploadedFile);\n\n      try {\n        // Extract text content\n        const content = await extractTextContent(file);\n        \n        // Create journal entry\n        const entry: JournalEntry = {\n          id: uuidv4(),\n          title: file.name.replace(/\\.[^/.]+$/, ''), // Remove file extension\n          content: content.trim(),\n          tags: ['imported'],\n          createdAt: new Date(file.lastModified || Date.now()),\n          updatedAt: new Date(),\n          wordCount: content.trim().split(/\\s+/).filter(word => word.length > 0).length\n        };\n\n        // Update the uploaded file with success status\n        uploadedFile.content = content;\n        uploadedFile.status = 'success';\n        uploadedFile.entry = entry;\n\n      } catch (error) {\n        uploadedFile.status = 'error';\n        uploadedFile.error = error instanceof Error ? error.message : 'Failed to process file';\n      }\n    }\n\n    setUploadedFiles(prev => [...prev, ...newUploadedFiles]);\n    setIsProcessing(false);\n  }, [maxFileSize, acceptedTypes, extractTextContent]);\n\n  // Handle file input change\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n    // Reset input value to allow selecting the same file again\n    e.target.value = '';\n  }, [processFiles]);\n\n  // Handle drag and drop\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    const files = e.dataTransfer.files;\n    if (files && files.length > 0) {\n      processFiles(files);\n    }\n  }, [processFiles]);\n\n  // Import successful entries\n  const handleImportEntries = useCallback(() => {\n    const successfulEntries = uploadedFiles\n      .filter(file => file.status === 'success' && file.entry)\n      .map(file => file.entry!);\n\n    if (successfulEntries.length > 0) {\n      // Save to localStorage\n      successfulEntries.forEach(entry => {\n        localStorageManager.saveEntry(entry);\n      });\n\n      // Call parent callback\n      onEntriesImported(successfulEntries);\n\n      // Clear uploaded files\n      setUploadedFiles([]);\n    }\n  }, [uploadedFiles, onEntriesImported]);\n\n  // Remove uploaded file\n  const removeUploadedFile = useCallback((fileId: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));\n  }, []);\n\n  // Clear all uploaded files\n  const clearAllFiles = useCallback(() => {\n    setUploadedFiles([]);\n  }, []);\n\n  const successfulFiles = uploadedFiles.filter(file => file.status === 'success');\n  const hasErrors = uploadedFiles.some(file => file.status === 'error');\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Upload area */}\n      <div\n        className={`\n          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer\n          ${isDragOver \n            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n          }\n        `}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={() => fileInputRef.current?.click()}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept={acceptedTypes.join(',')}\n          onChange={handleFileInputChange}\n          className=\"hidden\"\n        />\n        \n        <div className=\"space-y-4\">\n          <div className=\"flex justify-center\">\n            <Upload className=\"w-12 h-12 text-gray-400\" />\n          </div>\n          \n          <div>\n            <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Upload your writings\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n              Drag and drop files here, or click to browse\n            </p>\n          </div>\n          \n          <div className=\"text-xs text-gray-400 dark:text-gray-500\">\n            <p>Supported formats: {acceptedTypes.join(', ')}</p>\n            <p>Maximum file size: {maxFileSize}MB</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Processing indicator */}\n      {isProcessing && (\n        <div className=\"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3\"></div>\n          <span className=\"text-blue-600 dark:text-blue-400\">Processing files...</span>\n        </div>\n      )}\n\n      {/* Uploaded files list */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Uploaded Files ({uploadedFiles.length})\n            </h3>\n            \n            <div className=\"flex gap-2\">\n              {successfulFiles.length > 0 && (\n                <button\n                  onClick={handleImportEntries}\n                  className=\"px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm\"\n                >\n                  Import {successfulFiles.length} entries\n                </button>\n              )}\n              \n              <button\n                onClick={clearAllFiles}\n                className=\"px-3 py-1.5 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm\"\n              >\n                Clear all\n              </button>\n            </div>\n          </div>\n\n          <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n            {uploadedFiles.map(uploadedFile => (\n              <div\n                key={uploadedFile.id}\n                className=\"flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\"\n              >\n                <div className=\"flex-shrink-0\">\n                  {uploadedFile.status === 'processing' && (\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600\"></div>\n                  )}\n                  {uploadedFile.status === 'success' && (\n                    <Check className=\"w-5 h-5 text-green-600\" />\n                  )}\n                  {uploadedFile.status === 'error' && (\n                    <AlertCircle className=\"w-5 h-5 text-red-600\" />\n                  )}\n                </div>\n\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center gap-2\">\n                    <FileText className=\"w-4 h-4 text-gray-400\" />\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {uploadedFile.file.name}\n                    </span>\n                  </div>\n                  \n                  {uploadedFile.status === 'success' && uploadedFile.entry && (\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      {uploadedFile.entry.wordCount} words • {uploadedFile.entry.tags.join(', ')}\n                    </p>\n                  )}\n                  \n                  {uploadedFile.status === 'error' && uploadedFile.error && (\n                    <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                      {uploadedFile.error}\n                    </p>\n                  )}\n                </div>\n\n                <button\n                  onClick={() => removeUploadedFile(uploadedFile.id)}\n                  className=\"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                >\n                  <X className=\"w-4 h-4\" />\n                </button>\n              </div>\n            ))}\n          </div>\n\n          {/* Summary */}\n          {uploadedFiles.length > 0 && (\n            <div className=\"text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-3\">\n              {successfulFiles.length} successful, {uploadedFiles.filter(f => f.status === 'error').length} failed\n              {hasErrors && (\n                <span className=\"text-red-600 dark:text-red-400 ml-2\">\n                  • Check error messages above\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;AAwBe,SAAS,WAAW,EACjC,iBAAiB,EACjB,YAAY,EAAE,EACd,cAAc,EAAE,EAChB,gBAAgB;IAAC;IAAQ;IAAO;IAAQ;IAAQ;CAAQ,EACxC;;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,iDAAiD;IACjD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO;YAC5C,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;YAE5D,OAAQ;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO,MAAM,KAAK,IAAI;gBAExB,KAAK;gBACL,KAAK;oBACH,uFAAuF;oBACvF,IAAI;wBACF,OAAO,MAAM,KAAK,IAAI;oBACxB,EAAE,OAAM;wBACN,MAAM,IAAI,MAAM;oBAClB;gBAEF;oBACE,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,eAAe;YAC7D;QACF;qDAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YACtC,gBAAgB;YAChB,MAAM,mBAAmC,EAAE;YAE3C,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;gBACpC,kBAAkB;gBAClB,IAAI,KAAK,IAAI,GAAG,cAAc,OAAO,MAAM;oBACzC,iBAAiB,IAAI,CAAC;wBACpB,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;wBACT;wBACA,SAAS;wBACT,QAAQ;wBACR,OAAO,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC;oBACnD;oBACA;gBACF;gBAEA,kBAAkB;gBAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;gBAClE,IAAI,CAAC,cAAc,QAAQ,CAAC,gBAAgB;oBAC1C,iBAAiB,IAAI,CAAC;wBACpB,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;wBACT;wBACA,SAAS;wBACT,QAAQ;wBACR,OAAO,CAAC,uCAAuC,EAAE,cAAc,IAAI,CAAC,OAAO;oBAC7E;oBACA;gBACF;gBAEA,MAAM,eAA6B;oBACjC,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;oBACT;oBACA,SAAS;oBACT,QAAQ;gBACV;gBAEA,iBAAiB,IAAI,CAAC;gBAEtB,IAAI;oBACF,uBAAuB;oBACvB,MAAM,UAAU,MAAM,mBAAmB;oBAEzC,uBAAuB;oBACvB,MAAM,QAAsB;wBAC1B,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;wBACT,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;wBACtC,SAAS,QAAQ,IAAI;wBACrB,MAAM;4BAAC;yBAAW;wBAClB,WAAW,IAAI,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG;wBACjD,WAAW,IAAI;wBACf,WAAW,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;oEAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;mEAAG,MAAM;oBAC/E;oBAEA,+CAA+C;oBAC/C,aAAa,OAAO,GAAG;oBACvB,aAAa,MAAM,GAAG;oBACtB,aAAa,KAAK,GAAG;gBAEvB,EAAE,OAAO,OAAO;oBACd,aAAa,MAAM,GAAG;oBACtB,aAAa,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChE;YACF;YAEA;wDAAiB,CAAA,OAAQ;2BAAI;2BAAS;qBAAiB;;YACvD,gBAAgB;QAClB;+CAAG;QAAC;QAAa;QAAe;KAAmB;IAEnD,2BAA2B;IAC3B,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACzC,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;YAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,aAAa;YACf;YACA,2DAA2D;YAC3D,EAAE,MAAM,CAAC,KAAK,GAAG;QACnB;wDAAG;QAAC;KAAa;IAEjB,uBAAuB;IACvB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,cAAc;QAChB;iDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;kDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,cAAc;YAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;YAClC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,aAAa;YACf;QACF;6CAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YACtC,MAAM,oBAAoB,cACvB,MAAM;iFAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,aAAa,KAAK,KAAK;gFACtD,GAAG;iFAAC,CAAA,OAAQ,KAAK,KAAK;;YAEzB,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,uBAAuB;gBACvB,kBAAkB,OAAO;mEAAC,CAAA;wBACxB,6HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;oBAChC;;gBAEA,uBAAuB;gBACvB,kBAAkB;gBAElB,uBAAuB;gBACvB,iBAAiB,EAAE;YACrB;QACF;sDAAG;QAAC;QAAe;KAAkB;IAErC,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACtC;8DAAiB,CAAA,OAAQ,KAAK,MAAM;sEAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;QAC3D;qDAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC,iBAAiB,EAAE;QACrB;gDAAG,EAAE;IAEL,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IACrE,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;IAE7D,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,aACE,mDACA,wFACH;QACH,CAAC;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,aAAa,OAAO,EAAE;;kCAErC,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAQ,cAAc,IAAI,CAAC;wBAC3B,UAAU;wBACV,WAAU;;;;;;kCAGZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAE;4CAAoB,cAAc,IAAI,CAAC;;;;;;;kDAC1C,6LAAC;;4CAAE;4CAAoB;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;YAMxC,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;YAKtD,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoD;oCAC/C,cAAc,MAAM;oCAAC;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;oCACZ,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;wCACC,SAAS;wCACT,WAAU;;4CACX;4CACS,gBAAgB,MAAM;4CAAC;;;;;;;kDAInC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAA,6BACjB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,MAAM,KAAK,8BACvB,6LAAC;gDAAI,WAAU;;;;;;4CAEhB,aAAa,MAAM,KAAK,2BACvB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAElB,aAAa,MAAM,KAAK,yBACvB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAI3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEACb,aAAa,IAAI,CAAC,IAAI;;;;;;;;;;;;4CAI1B,aAAa,MAAM,KAAK,aAAa,aAAa,KAAK,kBACtD,6LAAC;gDAAE,WAAU;;oDACV,aAAa,KAAK,CAAC,SAAS;oDAAC;oDAAU,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;4CAIxE,aAAa,MAAM,KAAK,WAAW,aAAa,KAAK,kBACpD,6LAAC;gDAAE,WAAU;0DACV,aAAa,KAAK;;;;;;;;;;;;kDAKzB,6LAAC;wCACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;wCACjD,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAxCV,aAAa,EAAE;;;;;;;;;;oBA+CzB,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,MAAM;4BAAC;4BAAc,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;4BAAC;4BAC5F,2BACC,6LAAC;gCAAK,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;;;AAUtE;GA3TwB;KAAA", "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/SearchComponent.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { JournalEntry, Tag, SearchFilters } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { Search, X, Filter, Calendar, Hash, SortAsc, SortDesc } from 'lucide-react';\n\ninterface SearchComponentProps {\n  entries: JournalEntry[];\n  tags: Tag[];\n  onSearchResults: (results: JournalEntry[]) => void;\n  className?: string;\n}\n\ninterface SearchResult extends JournalEntry {\n  matchType: 'title' | 'content' | 'tag';\n  matchText: string;\n  score: number;\n}\n\nexport default function SearchComponent({\n  entries,\n  tags,\n  onSearchResults,\n  className = ''\n}: SearchComponentProps) {\n  const [query, setQuery] = useState('');\n  const [filters, setFilters] = useState<SearchFilters>({\n    sortBy: 'updatedAt',\n    sortOrder: 'desc'\n  });\n  const [showFilters, setShowFilters] = useState(false);\n  const [isSearching, setIsSearching] = useState(false);\n  const [searchHistory, setSearchHistory] = useState<string[]>([]);\n  const [showHistory, setShowHistory] = useState(false);\n  \n  const searchInputRef = useRef<HTMLInputElement>(null);\n  const filtersRef = useRef<HTMLDivElement>(null);\n  const historyRef = useRef<HTMLDivElement>(null);\n\n  // Load search history from localStorage\n  useEffect(() => {\n    const history = localStorage.getItem('journal_search_history');\n    if (history) {\n      setSearchHistory(JSON.parse(history));\n    }\n  }, []);\n\n  // Save search history to localStorage\n  const saveSearchHistory = useCallback((newQuery: string) => {\n    if (!newQuery.trim()) return;\n    \n    const updatedHistory = [\n      newQuery,\n      ...searchHistory.filter(q => q !== newQuery)\n    ].slice(0, 10); // Keep only last 10 searches\n    \n    setSearchHistory(updatedHistory);\n    localStorage.setItem('journal_search_history', JSON.stringify(updatedHistory));\n  }, [searchHistory]);\n\n  // Highlight search terms in text\n  const highlightText = useCallback((text: string, searchQuery: string): string => {\n    if (!searchQuery.trim()) return text;\n    \n    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')})`, 'gi');\n    return text.replace(regex, '<mark class=\"bg-yellow-200 dark:bg-yellow-800\">$1</mark>');\n  }, []);\n\n  // Calculate search score\n  const calculateScore = useCallback((entry: JournalEntry, searchQuery: string): number => {\n    let score = 0;\n    const lowerQuery = searchQuery.toLowerCase();\n    const lowerTitle = entry.title.toLowerCase();\n    const lowerContent = entry.content.toLowerCase();\n    \n    // Title matches get higher score\n    if (lowerTitle.includes(lowerQuery)) {\n      score += lowerTitle === lowerQuery ? 100 : 50;\n    }\n    \n    // Content matches\n    const contentMatches = (lowerContent.match(new RegExp(lowerQuery, 'g')) || []).length;\n    score += contentMatches * 10;\n    \n    // Tag matches get high score\n    const tagMatches = entry.tags.filter(tag => \n      tag.toLowerCase().includes(lowerQuery)\n    ).length;\n    score += tagMatches * 30;\n    \n    // Boost recent entries slightly\n    const daysSinceUpdate = (Date.now() - entry.updatedAt.getTime()) / (1000 * 60 * 60 * 24);\n    score += Math.max(0, 10 - daysSinceUpdate);\n    \n    return score;\n  }, []);\n\n  // Perform search\n  const performSearch = useCallback(() => {\n    setIsSearching(true);\n    \n    try {\n      let results: SearchResult[] = [];\n      \n      if (query.trim()) {\n        // Text search\n        results = entries\n          .map(entry => {\n            const score = calculateScore(entry, query);\n            let matchType: 'title' | 'content' | 'tag' = 'content';\n            let matchText = '';\n            \n            // Determine match type and extract match text\n            if (entry.title.toLowerCase().includes(query.toLowerCase())) {\n              matchType = 'title';\n              matchText = entry.title;\n            } else if (entry.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))) {\n              matchType = 'tag';\n              matchText = entry.tags.find(tag => \n                tag.toLowerCase().includes(query.toLowerCase())\n              ) || '';\n            } else {\n              matchType = 'content';\n              // Extract snippet around the match\n              const contentLower = entry.content.toLowerCase();\n              const queryLower = query.toLowerCase();\n              const matchIndex = contentLower.indexOf(queryLower);\n              if (matchIndex !== -1) {\n                const start = Math.max(0, matchIndex - 50);\n                const end = Math.min(entry.content.length, matchIndex + query.length + 50);\n                matchText = entry.content.slice(start, end);\n                if (start > 0) matchText = '...' + matchText;\n                if (end < entry.content.length) matchText = matchText + '...';\n              }\n            }\n            \n            return {\n              ...entry,\n              matchType,\n              matchText,\n              score\n            } as SearchResult;\n          })\n          .filter(result => result.score > 0);\n      } else {\n        // No query, return all entries\n        results = entries.map(entry => ({\n          ...entry,\n          matchType: 'content' as const,\n          matchText: '',\n          score: 0\n        }));\n      }\n      \n      // Apply filters\n      if (filters.tags && filters.tags.length > 0) {\n        results = results.filter(entry =>\n          filters.tags!.every(tag => entry.tags.includes(tag))\n        );\n      }\n      \n      if (filters.dateFrom) {\n        results = results.filter(entry =>\n          entry.createdAt >= filters.dateFrom!\n        );\n      }\n      \n      if (filters.dateTo) {\n        results = results.filter(entry =>\n          entry.createdAt <= filters.dateTo!\n        );\n      }\n      \n      // Sort results\n      results.sort((a, b) => {\n        if (query.trim() && a.score !== b.score) {\n          return b.score - a.score; // Sort by relevance first when searching\n        }\n        \n        const sortBy = filters.sortBy || 'updatedAt';\n        const sortOrder = filters.sortOrder || 'desc';\n        \n        let aValue: any = a[sortBy];\n        let bValue: any = b[sortBy];\n        \n        if (sortBy === 'createdAt' || sortBy === 'updatedAt') {\n          aValue = aValue.getTime();\n          bValue = bValue.getTime();\n        }\n        \n        if (sortOrder === 'desc') {\n          return bValue > aValue ? 1 : -1;\n        } else {\n          return aValue > bValue ? 1 : -1;\n        }\n      });\n      \n      onSearchResults(results);\n      \n      // Save to search history\n      if (query.trim()) {\n        saveSearchHistory(query);\n      }\n      \n    } finally {\n      setIsSearching(false);\n    }\n  }, [query, filters, entries, calculateScore, onSearchResults, saveSearchHistory]);\n\n  // Debounced search\n  useEffect(() => {\n    const timer = setTimeout(performSearch, 300);\n    return () => clearTimeout(timer);\n  }, [performSearch]);\n\n  // Handle filter changes\n  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {\n    setFilters(prev => ({ ...prev, [key]: value }));\n  }, []);\n\n  // Clear search\n  const clearSearch = useCallback(() => {\n    setQuery('');\n    setFilters({ sortBy: 'updatedAt', sortOrder: 'desc' });\n    onSearchResults(entries);\n  }, [entries, onSearchResults]);\n\n  // Handle search history selection\n  const selectFromHistory = useCallback((historyQuery: string) => {\n    setQuery(historyQuery);\n    setShowHistory(false);\n  }, []);\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (filtersRef.current && !filtersRef.current.contains(event.target as Node)) {\n        setShowFilters(false);\n      }\n      if (historyRef.current && !historyRef.current.contains(event.target as Node)) {\n        setShowHistory(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Search input */}\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            ref={searchInputRef}\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            onFocus={() => setShowHistory(searchHistory.length > 0 && !query)}\n            placeholder=\"Search entries, tags, or content...\"\n            className=\"w-full pl-10 pr-20 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          \n          <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1\">\n            {query && (\n              <button\n                onClick={clearSearch}\n                className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n              >\n                <X className=\"w-4 h-4\" />\n              </button>\n            )}\n            \n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className={`p-1 transition-colors ${\n                showFilters || filters.tags?.length || filters.dateFrom || filters.dateTo\n                  ? 'text-blue-600 dark:text-blue-400'\n                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'\n              }`}\n            >\n              <Filter className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Search history dropdown */}\n        {showHistory && searchHistory.length > 0 && (\n          <div\n            ref={historyRef}\n            className=\"absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto\"\n          >\n            <div className=\"px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700\">\n              Recent searches\n            </div>\n            {searchHistory.map((historyQuery, index) => (\n              <button\n                key={index}\n                onClick={() => selectFromHistory(historyQuery)}\n                className=\"w-full px-3 py-2 text-left text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n              >\n                {historyQuery}\n              </button>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Filters */}\n      {showFilters && (\n        <div\n          ref={filtersRef}\n          className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 space-y-4\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-sm font-medium text-gray-900 dark:text-white\">Filters</h3>\n            <button\n              onClick={() => setShowFilters(false)}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* Tag filter */}\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                Tags\n              </label>\n              <select\n                multiple\n                value={filters.tags || []}\n                onChange={(e) => updateFilter('tags', Array.from(e.target.selectedOptions, option => option.value))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n                size={3}\n              >\n                {tags.map(tag => (\n                  <option key={tag.id} value={tag.name}>\n                    {tag.name} ({tag.usageCount})\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort options */}\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                Sort by\n              </label>\n              <div className=\"flex space-x-2\">\n                <select\n                  value={filters.sortBy || 'updatedAt'}\n                  onChange={(e) => updateFilter('sortBy', e.target.value)}\n                  className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n                >\n                  <option value=\"updatedAt\">Last Modified</option>\n                  <option value=\"createdAt\">Created Date</option>\n                  <option value=\"title\">Title</option>\n                  <option value=\"wordCount\">Word Count</option>\n                </select>\n                \n                <button\n                  onClick={() => updateFilter('sortOrder', filters.sortOrder === 'desc' ? 'asc' : 'desc')}\n                  className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  {filters.sortOrder === 'desc' ? <SortDesc className=\"w-4 h-4\" /> : <SortAsc className=\"w-4 h-4\" />}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Date range */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                From Date\n              </label>\n              <input\n                type=\"date\"\n                value={filters.dateFrom ? filters.dateFrom.toISOString().split('T')[0] : ''}\n                onChange={(e) => updateFilter('dateFrom', e.target.value ? new Date(e.target.value) : undefined)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n            \n            <div className=\"space-y-2\">\n              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300\">\n                To Date\n              </label>\n              <input\n                type=\"date\"\n                value={filters.dateTo ? filters.dateTo.toISOString().split('T')[0] : ''}\n                onChange={(e) => updateFilter('dateTo', e.target.value ? new Date(e.target.value) : undefined)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Search status */}\n      {isSearching && (\n        <div className=\"flex items-center justify-center py-4\">\n          <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3\"></div>\n          <span className=\"text-sm text-gray-600 dark:text-gray-400\">Searching...</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;AAoBe,SAAS,gBAAgB,EACtC,OAAO,EACP,IAAI,EACJ,eAAe,EACf,YAAY,EAAE,EACO;;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,WAAW;IACb;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,UAAU,aAAa,OAAO,CAAC;YACrC,IAAI,SAAS;gBACX,iBAAiB,KAAK,KAAK,CAAC;YAC9B;QACF;oCAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACrC,IAAI,CAAC,SAAS,IAAI,IAAI;YAEtB,MAAM,iBAAiB;gBACrB;mBACG,cAAc,MAAM;qFAAC,CAAA,IAAK,MAAM;;aACpC,CAAC,KAAK,CAAC,GAAG,KAAK,6BAA6B;YAE7C,iBAAiB;YACjB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;QAChE;yDAAG;QAAC;KAAc;IAElB,iCAAiC;IACjC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAC/C,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO;YAEhC,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EAAE;YACpF,OAAO,KAAK,OAAO,CAAC,OAAO;QAC7B;qDAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,OAAqB;YACvD,IAAI,QAAQ;YACZ,MAAM,aAAa,YAAY,WAAW;YAC1C,MAAM,aAAa,MAAM,KAAK,CAAC,WAAW;YAC1C,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;YAE9C,iCAAiC;YACjC,IAAI,WAAW,QAAQ,CAAC,aAAa;gBACnC,SAAS,eAAe,aAAa,MAAM;YAC7C;YAEA,kBAAkB;YAClB,MAAM,iBAAiB,CAAC,aAAa,KAAK,CAAC,IAAI,OAAO,YAAY,SAAS,EAAE,EAAE,MAAM;YACrF,SAAS,iBAAiB;YAE1B,6BAA6B;YAC7B,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM;+DAAC,CAAA,MACnC,IAAI,WAAW,GAAG,QAAQ,CAAC;8DAC3B,MAAM;YACR,SAAS,aAAa;YAEtB,gCAAgC;YAChC,MAAM,kBAAkB,CAAC,KAAK,GAAG,KAAK,MAAM,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACvF,SAAS,KAAK,GAAG,CAAC,GAAG,KAAK;YAE1B,OAAO;QACT;sDAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,eAAe;YAEf,IAAI;gBACF,IAAI,UAA0B,EAAE;gBAEhC,IAAI,MAAM,IAAI,IAAI;oBAChB,cAAc;oBACd,UAAU,QACP,GAAG;sEAAC,CAAA;4BACH,MAAM,QAAQ,eAAe,OAAO;4BACpC,IAAI,YAAyC;4BAC7C,IAAI,YAAY;4BAEhB,8CAA8C;4BAC9C,IAAI,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,KAAK;gCAC3D,YAAY;gCACZ,YAAY,MAAM,KAAK;4BACzB,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI;8EAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;8EAAM;gCAClF,YAAY;gCACZ,YAAY,MAAM,IAAI,CAAC,IAAI;kFAAC,CAAA,MAC1B,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;oFACzC;4BACP,OAAO;gCACL,YAAY;gCACZ,mCAAmC;gCACnC,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW;gCAC9C,MAAM,aAAa,MAAM,WAAW;gCACpC,MAAM,aAAa,aAAa,OAAO,CAAC;gCACxC,IAAI,eAAe,CAAC,GAAG;oCACrB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa;oCACvC,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,OAAO,CAAC,MAAM,EAAE,aAAa,MAAM,MAAM,GAAG;oCACvE,YAAY,MAAM,OAAO,CAAC,KAAK,CAAC,OAAO;oCACvC,IAAI,QAAQ,GAAG,YAAY,QAAQ;oCACnC,IAAI,MAAM,MAAM,OAAO,CAAC,MAAM,EAAE,YAAY,YAAY;gCAC1D;4BACF;4BAEA,OAAO;gCACL,GAAG,KAAK;gCACR;gCACA;gCACA;4BACF;wBACF;qEACC,MAAM;sEAAC,CAAA,SAAU,OAAO,KAAK,GAAG;;gBACrC,OAAO;oBACL,+BAA+B;oBAC/B,UAAU,QAAQ,GAAG;sEAAC,CAAA,QAAS,CAAC;gCAC9B,GAAG,KAAK;gCACR,WAAW;gCACX,WAAW;gCACX,OAAO;4BACT,CAAC;;gBACH;gBAEA,gBAAgB;gBAChB,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC3C,UAAU,QAAQ,MAAM;sEAAC,CAAA,QACvB,QAAQ,IAAI,CAAE,KAAK;8EAAC,CAAA,MAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;;;gBAEnD;gBAEA,IAAI,QAAQ,QAAQ,EAAE;oBACpB,UAAU,QAAQ,MAAM;sEAAC,CAAA,QACvB,MAAM,SAAS,IAAI,QAAQ,QAAQ;;gBAEvC;gBAEA,IAAI,QAAQ,MAAM,EAAE;oBAClB,UAAU,QAAQ,MAAM;sEAAC,CAAA,QACvB,MAAM,SAAS,IAAI,QAAQ,MAAM;;gBAErC;gBAEA,eAAe;gBACf,QAAQ,IAAI;kEAAC,CAAC,GAAG;wBACf,IAAI,MAAM,IAAI,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;4BACvC,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,yCAAyC;wBACrE;wBAEA,MAAM,SAAS,QAAQ,MAAM,IAAI;wBACjC,MAAM,YAAY,QAAQ,SAAS,IAAI;wBAEvC,IAAI,SAAc,CAAC,CAAC,OAAO;wBAC3B,IAAI,SAAc,CAAC,CAAC,OAAO;wBAE3B,IAAI,WAAW,eAAe,WAAW,aAAa;4BACpD,SAAS,OAAO,OAAO;4BACvB,SAAS,OAAO,OAAO;wBACzB;wBAEA,IAAI,cAAc,QAAQ;4BACxB,OAAO,SAAS,SAAS,IAAI,CAAC;wBAChC,OAAO;4BACL,OAAO,SAAS,SAAS,IAAI,CAAC;wBAChC;oBACF;;gBAEA,gBAAgB;gBAEhB,yBAAyB;gBACzB,IAAI,MAAM,IAAI,IAAI;oBAChB,kBAAkB;gBACpB;YAEF,SAAU;gBACR,eAAe;YACjB;QACF;qDAAG;QAAC;QAAO;QAAS;QAAS;QAAgB;QAAiB;KAAkB;IAEhF,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,QAAQ,WAAW,eAAe;YACxC;6CAAO,IAAM,aAAa;;QAC5B;oCAAG;QAAC;KAAc;IAElB,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC,KAA0B;YAC1D;6DAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,IAAI,EAAE;oBAAM,CAAC;;QAC/C;oDAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC9B,SAAS;YACT,WAAW;gBAAE,QAAQ;gBAAa,WAAW;YAAO;YACpD,gBAAgB;QAClB;mDAAG;QAAC;QAAS;KAAgB;IAE7B,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YACrC,SAAS;YACT,eAAe;QACjB;yDAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5E,eAAe;oBACjB;oBACA,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC5E,eAAe;oBACjB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,SAAS,IAAM,eAAe,cAAc,MAAM,GAAG,KAAK,CAAC;gCAC3D,aAAY;gCACZ,WAAU;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;;oCACZ,uBACC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;kDAIjB,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,sBAAsB,EAChC,eAAe,QAAQ,IAAI,EAAE,UAAU,QAAQ,QAAQ,IAAI,QAAQ,MAAM,GACrE,qCACA,8DACJ;kDAEF,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMvB,eAAe,cAAc,MAAM,GAAG,mBACrC,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAA+G;;;;;;4BAG7H,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CAET;mCAJI;;;;;;;;;;;;;;;;;YAYd,6BACC,6LAAC;gBACC,KAAK;gBACL,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,6LAAC;wCACC,QAAQ;wCACR,OAAO,QAAQ,IAAI,IAAI,EAAE;wCACzB,UAAU,CAAC,IAAM,aAAa,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;wCACjG,WAAU;wCACV,MAAM;kDAEL,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;gDAAoB,OAAO,IAAI,IAAI;;oDACjC,IAAI,IAAI;oDAAC;oDAAG,IAAI,UAAU;oDAAC;;+CADjB,IAAI,EAAE;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO,QAAQ,MAAM,IAAI;gDACzB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;gDACtD,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAY;;;;;;;;;;;;0DAG5B,6LAAC;gDACC,SAAS,IAAM,aAAa,aAAa,QAAQ,SAAS,KAAK,SAAS,QAAQ;gDAChF,WAAU;0DAET,QAAQ,SAAS,KAAK,uBAAS,6LAAC,oOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;yEAAe,6LAAC,iOAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wCACzE,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI;wCACtF,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA6D;;;;;;kDAG9E,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wCACrE,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI;wCACpF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;YAQnB,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAK,WAAU;kCAA2C;;;;;;;;;;;;;;;;;;AAKrE;GAxYwB;KAAA", "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/Settings.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useTheme, ThemeToggle } from './ThemeProvider';\nimport { UserPreferences } from '@/lib/types';\nimport { Settings as SettingsIcon, Type, Palette, Save, RotateCcw, Zap } from 'lucide-react';\n\ninterface SettingsProps {\n  isOpen: boolean;\n  onClose: () => void;\n  className?: string;\n}\n\nconst FONT_FAMILIES = [\n  { value: 'Inter', label: 'Inter (Default)' },\n  { value: 'Georgia', label: 'Georgia (Serif)' },\n  { value: 'Times New Roman', label: 'Times New Roman' },\n  { value: 'Arial', label: 'Arial (Sans-serif)' },\n  { value: 'Helvetica', label: 'Helvetica' },\n  { value: 'Courier New', label: 'Courier New (Monospace)' },\n  { value: 'Verdana', label: 'Verdana' },\n  { value: 'Trebuchet MS', label: 'Trebuchet MS' }\n];\n\nconst FONT_SIZES = [\n  { value: 12, label: 'Extra Small' },\n  { value: 14, label: 'Small' },\n  { value: 16, label: 'Medium (Default)' },\n  { value: 18, label: 'Large' },\n  { value: 20, label: 'Extra Large' },\n  { value: 24, label: 'Huge' }\n];\n\nconst AUTO_SAVE_INTERVALS = [\n  { value: 1000, label: '1 second' },\n  { value: 2000, label: '2 seconds (Default)' },\n  { value: 5000, label: '5 seconds' },\n  { value: 10000, label: '10 seconds' },\n  { value: 30000, label: '30 seconds' },\n  { value: 60000, label: '1 minute' }\n];\n\nexport default function Settings({ isOpen, onClose, className = '' }: SettingsProps) {\n  const { preferences, updatePreferences } = useTheme();\n  const [tempPreferences, setTempPreferences] = useState<UserPreferences | null>(preferences);\n  const [hasChanges, setHasChanges] = useState(false);\n\n  // Update temp preferences when actual preferences change\n  React.useEffect(() => {\n    if (preferences && !hasChanges) {\n      setTempPreferences(preferences);\n    }\n  }, [preferences, hasChanges]);\n\n  if (!isOpen || !tempPreferences) return null;\n\n  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {\n    setTempPreferences(prev => prev ? { ...prev, [key]: value } : null);\n    setHasChanges(true);\n  };\n\n  const handleSave = () => {\n    if (tempPreferences) {\n      updatePreferences(tempPreferences);\n      setHasChanges(false);\n    }\n  };\n\n  const handleReset = () => {\n    setTempPreferences(preferences);\n    setHasChanges(false);\n  };\n\n  const handleResetToDefaults = () => {\n    const defaultPreferences: UserPreferences = {\n      id: tempPreferences.id,\n      theme: 'system',\n      fontSize: 16,\n      fontFamily: 'Inter',\n      spellCheckEnabled: true,\n      autoSaveInterval: 2000\n    };\n    setTempPreferences(defaultPreferences);\n    setHasChanges(true);\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        <div className=\"relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-lg shadow-xl\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3\">\n              <SettingsIcon className=\"w-6 h-6 text-gray-600 dark:text-gray-400\" />\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                Settings\n              </h2>\n            </div>\n            \n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 space-y-8 max-h-96 overflow-y-auto\">\n            {/* Theme Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Palette className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Appearance\n                </h3>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Theme\n                </label>\n                <ThemeToggle />\n              </div>\n            </div>\n\n            {/* Typography Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Type className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Typography\n                </h3>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {/* Font Family */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Font Family\n                  </label>\n                  <select\n                    value={tempPreferences.fontFamily}\n                    onChange={(e) => handlePreferenceChange('fontFamily', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {FONT_FAMILIES.map(font => (\n                      <option key={font.value} value={font.value}>\n                        {font.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* Font Size */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Font Size\n                  </label>\n                  <select\n                    value={tempPreferences.fontSize}\n                    onChange={(e) => handlePreferenceChange('fontSize', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {FONT_SIZES.map(size => (\n                      <option key={size.value} value={size.value}>\n                        {size.label} ({size.value}px)\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Font Preview */}\n              <div className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Preview:</p>\n                <p \n                  style={{ \n                    fontFamily: tempPreferences.fontFamily,\n                    fontSize: `${tempPreferences.fontSize}px`\n                  }}\n                  className=\"text-gray-900 dark:text-white\"\n                >\n                  The quick brown fox jumps over the lazy dog. This is how your journal entries will look with the selected font settings.\n                </p>\n              </div>\n            </div>\n\n            {/* Editor Settings */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Zap className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  Editor\n                </h3>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {/* Auto-save Interval */}\n                <div className=\"space-y-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Auto-save Interval\n                  </label>\n                  <select\n                    value={tempPreferences.autoSaveInterval}\n                    onChange={(e) => handlePreferenceChange('autoSaveInterval', parseInt(e.target.value))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    {AUTO_SAVE_INTERVALS.map(interval => (\n                      <option key={interval.value} value={interval.value}>\n                        {interval.label}\n                      </option>\n                    ))}\n                  </select>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    How often to automatically save your work while typing\n                  </p>\n                </div>\n\n                {/* Spell Check */}\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Spell Check\n                    </label>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      Enable spell checking while typing\n                    </p>\n                  </div>\n                  <button\n                    onClick={() => handlePreferenceChange('spellCheckEnabled', !tempPreferences.spellCheckEnabled)}\n                    className={`\n                      relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                      ${tempPreferences.spellCheckEnabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                    `}\n                  >\n                    <span\n                      className={`\n                        inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                        ${tempPreferences.spellCheckEnabled ? 'translate-x-6' : 'translate-x-1'}\n                      `}\n                    />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={handleResetToDefaults}\n              className=\"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n            >\n              <RotateCcw className=\"w-4 h-4\" />\n              <span>Reset to Defaults</span>\n            </button>\n            \n            <div className=\"flex space-x-3\">\n              {hasChanges && (\n                <button\n                  onClick={handleReset}\n                  className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n                >\n                  Cancel\n                </button>\n              )}\n              \n              <button\n                onClick={handleSave}\n                disabled={!hasChanges}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                <Save className=\"w-4 h-4\" />\n                <span>Save Changes</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAaA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAS,OAAO;IAAkB;IAC3C;QAAE,OAAO;QAAW,OAAO;IAAkB;IAC7C;QAAE,OAAO;QAAmB,OAAO;IAAkB;IACrD;QAAE,OAAO;QAAS,OAAO;IAAqB;IAC9C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAe,OAAO;IAA0B;IACzD;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAI,OAAO;IAAc;IAClC;QAAE,OAAO;QAAI,OAAO;IAAQ;IAC5B;QAAE,OAAO;QAAI,OAAO;IAAmB;IACvC;QAAE,OAAO;QAAI,OAAO;IAAQ;IAC5B;QAAE,OAAO;QAAI,OAAO;IAAc;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;CAC5B;AAED,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAM,OAAO;IAAW;IACjC;QAAE,OAAO;QAAM,OAAO;IAAsB;IAC5C;QAAE,OAAO;QAAM,OAAO;IAAY;IAClC;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAO,OAAO;IAAW;CACnC;AAEc,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,EAAiB;;IACjF,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yDAAyD;IACzD,6JAAA,CAAA,UAAK,CAAC,SAAS;8BAAC;YACd,IAAI,eAAe,CAAC,YAAY;gBAC9B,mBAAmB;YACrB;QACF;6BAAG;QAAC;QAAa;KAAW;IAE5B,IAAI,CAAC,UAAU,CAAC,iBAAiB,OAAO;IAExC,MAAM,yBAAyB,CAAC,KAA4B;QAC1D,mBAAmB,CAAA,OAAQ,OAAO;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,IAAI;QAC9D,cAAc;IAChB;IAEA,MAAM,aAAa;QACjB,IAAI,iBAAiB;YACnB,kBAAkB;YAClB,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,mBAAmB;QACnB,cAAc;IAChB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,qBAAsC;YAC1C,IAAI,gBAAgB,EAAE;YACtB,OAAO;YACP,UAAU;YACV,YAAY;YACZ,mBAAmB;YACnB,kBAAkB;QACpB;QACA,mBAAmB;QACnB,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;4CAAG,WAAU;sDAAsD;;;;;;;;;;;;8CAKtE,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAA6D;;;;;;8DAG9E,6LAAC,sIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,6LAAC;4DACC,OAAO,gBAAgB,UAAU;4DACjC,UAAU,CAAC,IAAM,uBAAuB,cAAc,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAU;sEAET,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;oEAAwB,OAAO,KAAK,KAAK;8EACvC,KAAK,KAAK;mEADA,KAAK,KAAK;;;;;;;;;;;;;;;;8DAQ7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,6LAAC;4DACC,OAAO,gBAAgB,QAAQ;4DAC/B,UAAU,CAAC,IAAM,uBAAuB,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;4DAC3E,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oEAAwB,OAAO,KAAK,KAAK;;wEACvC,KAAK,KAAK;wEAAC;wEAAG,KAAK,KAAK;wEAAC;;mEADf,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sDAS/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAC7D,6LAAC;oDACC,OAAO;wDACL,YAAY,gBAAgB,UAAU;wDACtC,UAAU,GAAG,gBAAgB,QAAQ,CAAC,EAAE,CAAC;oDAC3C;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;oDAAG,WAAU;8DAAoD;;;;;;;;;;;;sDAKpE,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA6D;;;;;;sEAG9E,6LAAC;4DACC,OAAO,gBAAgB,gBAAgB;4DACvC,UAAU,CAAC,IAAM,uBAAuB,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;4DACnF,WAAU;sEAET,oBAAoB,GAAG,CAAC,CAAA,yBACvB,6LAAC;oEAA4B,OAAO,SAAS,KAAK;8EAC/C,SAAS,KAAK;mEADJ,SAAS,KAAK;;;;;;;;;;sEAK/B,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;8DAM1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAAuD;;;;;;8EAGxE,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;sEAI1D,6LAAC;4DACC,SAAS,IAAM,uBAAuB,qBAAqB,CAAC,gBAAgB,iBAAiB;4DAC7F,WAAW,CAAC;;sBAEV,EAAE,gBAAgB,iBAAiB,GAAG,gBAAgB,+BAA+B;oBACvF,CAAC;sEAED,cAAA,6LAAC;gEACC,WAAW,CAAC;;wBAEV,EAAE,gBAAgB,iBAAiB,GAAG,kBAAkB,gBAAgB;sBAC1E,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;wCACZ,4BACC,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAKH,6LAAC;4CACC,SAAS;4CACT,UAAU,CAAC;4CACX,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAzPwB;;QACqB,sIAAA,CAAA,WAAQ;;;KAD7B", "debugId": null}}, {"offset": {"line": 2741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/JournalApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { JournalEntry, Tag, UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\nimport { useTheme } from './ThemeProvider';\nimport TextEditor from './TextEditor';\nimport TagManager from './TagManager';\nimport FileUpload from './FileUpload';\nimport SearchComponent from './SearchComponent';\nimport Settings from './Settings';\nimport { \n  PlusCircle, \n  Search, \n  Upload, \n  Settings as SettingsIcon, \n  BookOpen, \n  Menu,\n  X,\n  Edit3,\n  Calendar,\n  Hash\n} from 'lucide-react';\nimport { v4 as uuidv4 } from 'uuid';\n\ntype ViewMode = 'list' | 'editor' | 'search' | 'upload';\n\nexport default function JournalApp() {\n  const { preferences } = useTheme();\n  const [entries, setEntries] = useState<JournalEntry[]>([]);\n  const [tags, setTags] = useState<Tag[]>([]);\n  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);\n  const [viewMode, setViewMode] = useState<ViewMode>('list');\n  const [searchResults, setSearchResults] = useState<JournalEntry[]>([]);\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    const loadData = () => {\n      try {\n        const savedEntries = localStorageManager.getEntries();\n        const savedTags = localStorageManager.getTags();\n        \n        setEntries(savedEntries);\n        setTags(savedTags);\n        setSearchResults(savedEntries);\n      } catch (error) {\n        console.error('Failed to load data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Create new entry\n  const createNewEntry = useCallback(() => {\n    const newEntry: JournalEntry = {\n      id: uuidv4(),\n      title: '',\n      content: '',\n      tags: [],\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      wordCount: 0\n    };\n    \n    setCurrentEntry(newEntry);\n    setViewMode('editor');\n    setShowMobileMenu(false);\n  }, []);\n\n  // Save entry\n  const handleSaveEntry = useCallback((entry: JournalEntry) => {\n    try {\n      // Save to localStorage\n      localStorageManager.saveEntry(entry);\n      \n      // Update state\n      setEntries(prev => {\n        const existingIndex = prev.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          const updated = [...prev];\n          updated[existingIndex] = entry;\n          return updated;\n        } else {\n          return [entry, ...prev];\n        }\n      });\n      \n      // Update search results if needed\n      setSearchResults(prev => {\n        const existingIndex = prev.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          const updated = [...prev];\n          updated[existingIndex] = entry;\n          return updated;\n        } else {\n          return [entry, ...prev];\n        }\n      });\n      \n      setCurrentEntry(entry);\n    } catch (error) {\n      console.error('Failed to save entry:', error);\n    }\n  }, []);\n\n  // Delete entry\n  const handleDeleteEntry = useCallback((entryId: string) => {\n    try {\n      localStorageManager.deleteEntry(entryId);\n      setEntries(prev => prev.filter(e => e.id !== entryId));\n      setSearchResults(prev => prev.filter(e => e.id !== entryId));\n      \n      if (currentEntry?.id === entryId) {\n        setCurrentEntry(null);\n        setViewMode('list');\n      }\n    } catch (error) {\n      console.error('Failed to delete entry:', error);\n    }\n  }, [currentEntry]);\n\n  // Create new tag\n  const handleCreateTag = useCallback((tag: Tag) => {\n    setTags(prev => [...prev, tag]);\n  }, []);\n\n  // Handle file uploads\n  const handleEntriesImported = useCallback((importedEntries: JournalEntry[]) => {\n    setEntries(prev => [...importedEntries, ...prev]);\n    setSearchResults(prev => [...importedEntries, ...prev]);\n    setViewMode('list');\n  }, []);\n\n  // Edit entry\n  const editEntry = useCallback((entry: JournalEntry) => {\n    setCurrentEntry(entry);\n    setViewMode('editor');\n    setShowMobileMenu(false);\n  }, []);\n\n  // Format date for display\n  const formatDate = (date: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    \n    if (days === 0) return 'Today';\n    if (days === 1) return 'Yesterday';\n    if (days < 7) return `${days} days ago`;\n    \n    return date.toLocaleDateString();\n  };\n\n  // Mobile navigation\n  const MobileNav = () => (\n    <div className=\"md:hidden\">\n      <button\n        onClick={() => setShowMobileMenu(!showMobileMenu)}\n        className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n      >\n        {showMobileMenu ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n      </button>\n      \n      {showMobileMenu && (\n        <div className=\"absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-20\">\n          <div className=\"p-4 space-y-2\">\n            <button\n              onClick={() => { setViewMode('list'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <BookOpen className=\"w-5 h-5\" />\n              <span>All Entries</span>\n            </button>\n            \n            <button\n              onClick={() => { setViewMode('search'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <Search className=\"w-5 h-5\" />\n              <span>Search</span>\n            </button>\n            \n            <button\n              onClick={() => { setViewMode('upload'); setShowMobileMenu(false); }}\n              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md transition-colors ${\n                viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n              }`}\n            >\n              <Upload className=\"w-5 h-5\" />\n              <span>Import</span>\n            </button>\n            \n            <button\n              onClick={createNewEntry}\n              className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-colors\"\n            >\n              <PlusCircle className=\"w-5 h-5\" />\n              <span>New Entry</span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white\">\n      {/* Header */}\n      <header className=\"sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                AllJournal\n              </h1>\n              <MobileNav />\n            </div>\n            \n            {/* Desktop navigation */}\n            <nav className=\"hidden md:flex items-center space-x-4\">\n              <button\n                onClick={() => setViewMode('list')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'list' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <BookOpen className=\"w-4 h-4\" />\n                <span>Entries</span>\n              </button>\n              \n              <button\n                onClick={() => setViewMode('search')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'search' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <Search className=\"w-4 h-4\" />\n                <span>Search</span>\n              </button>\n              \n              <button\n                onClick={() => setViewMode('upload')}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${\n                  viewMode === 'upload' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <Upload className=\"w-4 h-4\" />\n                <span>Import</span>\n              </button>\n            </nav>\n            \n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={createNewEntry}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                <PlusCircle className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">New Entry</span>\n              </button>\n              \n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors\"\n              >\n                <SettingsIcon className=\"w-5 h-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {viewMode === 'editor' && currentEntry && preferences && (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden\">\n              <TextEditor\n                entry={currentEntry}\n                onSave={handleSaveEntry}\n                preferences={preferences}\n              />\n              \n              {/* Tag manager */}\n              <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n                <TagManager\n                  tags={tags}\n                  selectedTags={currentEntry.tags}\n                  onTagsChange={(newTags) => setCurrentEntry(prev => prev ? { ...prev, tags: newTags } : null)}\n                  onCreateTag={handleCreateTag}\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {viewMode === 'list' && (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                Your Journal Entries\n              </h2>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                {entries.length} {entries.length === 1 ? 'entry' : 'entries'}\n              </span>\n            </div>\n            \n            {entries.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <BookOpen className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  No entries yet\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                  Start your journaling journey by creating your first entry.\n                </p>\n                <button\n                  onClick={createNewEntry}\n                  className=\"inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n                >\n                  <PlusCircle className=\"w-4 h-4\" />\n                  <span>Create First Entry</span>\n                </button>\n              </div>\n            ) : (\n              <div className=\"grid gap-4\">\n                {searchResults.map(entry => (\n                  <div\n                    key={entry.id}\n                    className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer\"\n                    onClick={() => editEntry(entry)}\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white truncate\">\n                        {entry.title || 'Untitled'}\n                      </h3>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          editEntry(entry);\n                        }}\n                        className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n                      >\n                        <Edit3 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                      {entry.content.slice(0, 200)}\n                      {entry.content.length > 200 && '...'}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-4 h-4\" />\n                          <span>{formatDate(entry.updatedAt)}</span>\n                        </div>\n                        <span>{entry.wordCount} words</span>\n                      </div>\n                      \n                      {entry.tags.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Hash className=\"w-3 h-3\" />\n                          <span>{entry.tags.slice(0, 2).join(', ')}</span>\n                          {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {viewMode === 'search' && (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Search Entries\n            </h2>\n            \n            <SearchComponent\n              entries={entries}\n              tags={tags}\n              onSearchResults={setSearchResults}\n            />\n            \n            <div className=\"grid gap-4\">\n              {searchResults.map(entry => (\n                <div\n                  key={entry.id}\n                  className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer\"\n                  onClick={() => editEntry(entry)}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white truncate\">\n                      {entry.title || 'Untitled'}\n                    </h3>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        editEntry(entry);\n                      }}\n                      className=\"p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\"\n                    >\n                      <Edit3 className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                  \n                  <p className=\"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3\">\n                    {entry.content.slice(0, 200)}\n                    {entry.content.length > 200 && '...'}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span>{formatDate(entry.updatedAt)}</span>\n                      </div>\n                      <span>{entry.wordCount} words</span>\n                    </div>\n                    \n                    {entry.tags.length > 0 && (\n                      <div className=\"flex items-center space-x-1\">\n                        <Hash className=\"w-3 h-3\" />\n                        <span>{entry.tags.slice(0, 2).join(', ')}</span>\n                        {entry.tags.length > 2 && <span>+{entry.tags.length - 2}</span>}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {viewMode === 'upload' && (\n          <div className=\"max-w-2xl mx-auto\">\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n              Import Writings\n            </h2>\n            \n            <FileUpload onEntriesImported={handleEntriesImported} />\n          </div>\n        )}\n      </main>\n\n      {/* Settings modal */}\n      <Settings\n        isOpen={showSettings}\n        onClose={() => setShowSettings(false)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAvBA;;;;;;;;;;;AA2Be,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;iDAAW;oBACf,IAAI;wBACF,MAAM,eAAe,6HAAA,CAAA,sBAAmB,CAAC,UAAU;wBACnD,MAAM,YAAY,6HAAA,CAAA,sBAAmB,CAAC,OAAO;wBAE7C,WAAW;wBACX,QAAQ;wBACR,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACxC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YACjC,MAAM,WAAyB;gBAC7B,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,SAAS;gBACT,MAAM,EAAE;gBACR,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,WAAW;YACb;YAEA,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;QACpB;iDAAG,EAAE;IAEL,aAAa;IACb,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACnC,IAAI;gBACF,uBAAuB;gBACvB,6HAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC;gBAE9B,eAAe;gBACf;+DAAW,CAAA;wBACT,MAAM,gBAAgB,KAAK,SAAS;qFAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;;wBAC3D,IAAI,iBAAiB,GAAG;4BACtB,MAAM,UAAU;mCAAI;6BAAK;4BACzB,OAAO,CAAC,cAAc,GAAG;4BACzB,OAAO;wBACT,OAAO;4BACL,OAAO;gCAAC;mCAAU;6BAAK;wBACzB;oBACF;;gBAEA,kCAAkC;gBAClC;+DAAiB,CAAA;wBACf,MAAM,gBAAgB,KAAK,SAAS;qFAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;;wBAC3D,IAAI,iBAAiB,GAAG;4BACtB,MAAM,UAAU;mCAAI;6BAAK;4BACzB,OAAO,CAAC,cAAc,GAAG;4BACzB,OAAO;wBACT,OAAO;4BACL,OAAO;gCAAC;mCAAU;6BAAK;wBACzB;oBACF;;gBAEA,gBAAgB;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;kDAAG,EAAE;IAEL,eAAe;IACf,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACrC,IAAI;gBACF,6HAAA,CAAA,sBAAmB,CAAC,WAAW,CAAC;gBAChC;iEAAW,CAAA,OAAQ,KAAK,MAAM;yEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;gBAC7C;iEAAiB,CAAA,OAAQ,KAAK,MAAM;yEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;gBAEnD,IAAI,cAAc,OAAO,SAAS;oBAChC,gBAAgB;oBAChB,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;oDAAG;QAAC;KAAa;IAEjB,iBAAiB;IACjB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACnC;2DAAQ,CAAA,OAAQ;2BAAI;wBAAM;qBAAI;;QAChC;kDAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACzC;iEAAW,CAAA,OAAQ;2BAAI;2BAAoB;qBAAK;;YAChD;iEAAiB,CAAA,OAAQ;2BAAI;2BAAoB;qBAAK;;YACtD,YAAY;QACd;wDAAG,EAAE;IAEL,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC7B,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;QACpB;4CAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QACzC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;QAEnD,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,SAAS,CAAC;QAEvC,OAAO,KAAK,kBAAkB;IAChC;IAEA,oBAAoB;IACpB,MAAM,YAAY,kBAChB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS,IAAM,kBAAkB,CAAC;oBAClC,WAAU;8BAET,+BAAiB,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;gBAG/D,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAS,kBAAkB;gCAAQ;gCAChE,WAAW,CAAC,0EAA0E,EACpF,aAAa,SAAS,kEAAkE,6EACxF;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAW,kBAAkB;gCAAQ;gCAClE,WAAW,CAAC,0EAA0E,EACpF,aAAa,WAAW,kEAAkE,6EAC1F;;kDAEF,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCACC,SAAS;oCAAQ,YAAY;oCAAW,kBAAkB;gCAAQ;gCAClE,WAAW,CAAC,0EAA0E,EACpF,aAAa,WAAW,kEAAkE,6EAC1F;;kDAEF,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlB,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAGhE,6LAAC;;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,SAAS,kEAAkE,6EACxF;;0DAEF,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,WAAW,kEAAkE,6EAC1F;;0DAEF,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,WAAW,CAAC,mEAAmE,EAC7E,aAAa,WAAW,kEAAkE,6EAC1F;;0DAEF,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAGrC,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC;gBAAK,WAAU;;oBACb,aAAa,YAAY,gBAAgB,6BACxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,UAAU;oCACT,OAAO;oCACP,QAAQ;oCACR,aAAa;;;;;;8CAIf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mIAAA,CAAA,UAAU;wCACT,MAAM;wCACN,cAAc,aAAa,IAAI;wCAC/B,cAAc,CAAC,UAAY,gBAAgB,CAAA,OAAQ,OAAO;oDAAE,GAAG,IAAI;oDAAE,MAAM;gDAAQ,IAAI;wCACvF,aAAa;;;;;;;;;;;;;;;;;;;;;;oBAOtB,aAAa,wBACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAK,WAAU;;4CACb,QAAQ,MAAM;4CAAC;4CAAE,QAAQ,MAAM,KAAK,IAAI,UAAU;;;;;;;;;;;;;4BAItD,QAAQ,MAAM,KAAK,kBAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;qDAIV,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK,IAAI;;;;;;kEAElB,6LAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,UAAU;wDACZ;wDACA,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;oDACvB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;0DAGjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;kFAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;0EAEnC,6LAAC;;oEAAM,MAAM,SAAS;oEAAC;;;;;;;;;;;;;oDAGxB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAM,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;4DAClC,MAAM,IAAI,CAAC,MAAM,GAAG,mBAAK,6LAAC;;oEAAK;oEAAE,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;uCArCvD,MAAM,EAAE;;;;;;;;;;;;;;;;oBAgDxB,aAAa,0BACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAIjE,6LAAC,wIAAA,CAAA,UAAe;gCACd,SAAS;gCACT,MAAM;gCACN,iBAAiB;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA,sBACjB,6LAAC;wCAEC,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK,IAAI;;;;;;kEAElB,6LAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,UAAU;wDACZ;wDACA,WAAU;kEAEV,cAAA,6LAAC,6MAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAE,WAAU;;oDACV,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;oDACvB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;0DAGjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;kFAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;0EAEnC,6LAAC;;oEAAM,MAAM,SAAS;oEAAC;;;;;;;;;;;;;oDAGxB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAM,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;4DAClC,MAAM,IAAI,CAAC,MAAM,GAAG,mBAAK,6LAAC;;oEAAK;oEAAE,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;uCArCvD,MAAM,EAAE;;;;;;;;;;;;;;;;oBA+CtB,aAAa,0BACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAItE,6LAAC,mIAAA,CAAA,UAAU;gCAAC,mBAAmB;;;;;;;;;;;;;;;;;;0BAMrC,6LAAC,iIAAA,CAAA,UAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAIvC;GA9bwB;;QACE,sIAAA,CAAA,WAAQ;;;KADV", "debugId": null}}]}