/**
 * @jest-environment jsdom
 */

import { getLocalStorage } from '../lib/localStorage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('LocalStorage Manager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Settings Management', () => {
    it('should return default settings when localStorage is empty', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const storage = getLocalStorage();
      const settings = storage.getSettings();
      
      expect(settings).toEqual({
        theme: 'system',
        fontSize: 16,
        fontFamily: 'Inter, system-ui, sans-serif',
        autoSave: true,
        autoSaveInterval: 2000,
        spellCheck: true,
      });
    });

    it('should merge stored settings with defaults', () => {
      const storedSettings = JSON.stringify({
        theme: 'dark',
        fontSize: 18,
      });
      localStorageMock.getItem.mockReturnValue(storedSettings);
      
      const storage = getLocalStorage();
      const settings = storage.getSettings();
      
      expect(settings.theme).toBe('dark');
      expect(settings.fontSize).toBe(18);
      expect(settings.autoSave).toBe(true); // Should keep default
    });

    it('should save settings to localStorage', () => {
      const storage = getLocalStorage();
      const newSettings = { theme: 'light' as const, fontSize: 20 };
      
      // Mock current settings
      localStorageMock.getItem.mockReturnValue(JSON.stringify({
        theme: 'system',
        fontSize: 16,
        fontFamily: 'Inter, system-ui, sans-serif',
        autoSave: true,
        autoSaveInterval: 2000,
        spellCheck: true,
      }));
      
      storage.saveSettings(newSettings);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'journal_settings',
        expect.stringContaining('"theme":"light"')
      );
    });
  });

  describe('Draft Management', () => {
    it('should return empty array when no drafts exist', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const storage = getLocalStorage();
      const drafts = storage.getDrafts();
      
      expect(drafts).toEqual([]);
    });

    it('should save and retrieve drafts', () => {
      const storage = getLocalStorage();
      const draft = {
        id: 'test-id',
        title: 'Test Draft',
        content: 'Test content',
        tags: ['test'],
        lastModified: new Date().toISOString(),
      };
      
      // Mock empty drafts initially
      localStorageMock.getItem.mockReturnValue('[]');
      
      storage.saveDraft(draft);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'journal_drafts',
        expect.stringContaining('"title":"Test Draft"')
      );
    });

    it('should update existing draft', () => {
      const storage = getLocalStorage();
      const existingDrafts = [
        {
          id: 'test-id',
          title: 'Old Title',
          content: 'Old content',
          tags: [],
          lastModified: '2023-01-01T00:00:00.000Z',
        },
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(existingDrafts));
      
      const updatedDraft = {
        id: 'test-id',
        title: 'New Title',
        content: 'New content',
        tags: ['updated'],
        lastModified: new Date().toISOString(),
      };
      
      storage.saveDraft(updatedDraft);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'journal_drafts',
        expect.stringContaining('"title":"New Title"')
      );
    });
  });

  describe('Entry Caching', () => {
    it('should cache entries with timestamp', () => {
      const storage = getLocalStorage();
      const entries = [
        {
          id: 'entry-1',
          title: 'Test Entry',
          content: 'Test content',
          tags: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          word_count: 2,
        },
      ];
      
      storage.cacheEntries(entries);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'journal_entries',
        expect.stringContaining('"lastUpdated"')
      );
    });

    it('should return null when no cached entries exist', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const storage = getLocalStorage();
      const cached = storage.getCachedEntries();
      
      expect(cached).toBeNull();
    });
  });

  describe('Storage Usage', () => {
    it('should calculate storage usage', () => {
      // Mock localStorage.length and key/getItem
      Object.defineProperty(localStorageMock, 'length', { value: 2 });
      localStorageMock.key = jest.fn()
        .mockReturnValueOnce('journal_settings')
        .mockReturnValueOnce('journal_entries');
      localStorageMock.getItem
        .mockReturnValueOnce('{"theme":"dark"}')
        .mockReturnValueOnce('{"entries":[]}');
      
      const storage = getLocalStorage();
      const usage = storage.getStorageUsage();
      
      expect(usage.used).toBeGreaterThan(0);
      expect(usage.available).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      const storage = getLocalStorage();
      const settings = storage.getSettings();
      
      // Should return defaults when localStorage fails
      expect(settings.theme).toBe('system');
    });

    it('should handle JSON parsing errors', () => {
      localStorageMock.getItem.mockReturnValue('invalid json');
      
      const storage = getLocalStorage();
      const settings = storage.getSettings();
      
      // Should return defaults when JSON parsing fails
      expect(settings.theme).toBe('system');
    });
  });
});
