{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/conventions.js"], "sourcesContent": ["'use strict'\n\n/**\n * Ponyfill for `Array.prototype.find` which is only available in ES6 runtimes.\n *\n * Works with anything that has a `length` property and index access properties, including NodeList.\n *\n * @template {unknown} T\n * @param {Array<T> | ({length:number, [number]: T})} list\n * @param {function (item: T, index: number, list:Array<T> | ({length:number, [number]: T})):boolean} predicate\n * @param {Partial<Pick<ArrayConstructor['prototype'], 'find'>>?} ac `Array.prototype` by default,\n * \t\t\t\tallows injecting a custom implementation in tests\n * @returns {T | undefined}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n * @see https://tc39.es/ecma262/multipage/indexed-collections.html#sec-array.prototype.find\n */\nfunction find(list, predicate, ac) {\n\tif (ac === undefined) {\n\t\tac = Array.prototype;\n\t}\n\tif (list && typeof ac.find === 'function') {\n\t\treturn ac.find.call(list, predicate);\n\t}\n\tfor (var i = 0; i < list.length; i++) {\n\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\tvar item = list[i];\n\t\t\tif (predicate.call(undefined, item, i, list)) {\n\t\t\t\treturn item;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * \"Shallow freezes\" an object to render it immutable.\n * Uses `Object.freeze` if available,\n * otherwise the immutability is only in the type.\n *\n * Is used to create \"enum like\" objects.\n *\n * @template T\n * @param {T} object the object to freeze\n * @param {Pick<ObjectConstructor, 'freeze'> = Object} oc `Object` by default,\n * \t\t\t\tallows to inject custom object constructor for tests\n * @returns {Readonly<T>}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n */\nfunction freeze(object, oc) {\n\tif (oc === undefined) {\n\t\toc = Object\n\t}\n\treturn oc && typeof oc.freeze === 'function' ? oc.freeze(object) : object\n}\n\n/**\n * Since we can not rely on `Object.assign` we provide a simplified version\n * that is sufficient for our needs.\n *\n * @param {Object} target\n * @param {Object | null | undefined} source\n *\n * @returns {Object} target\n * @throws TypeError if target is not an object\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n * @see https://tc39.es/ecma262/multipage/fundamental-objects.html#sec-object.assign\n */\nfunction assign(target, source) {\n\tif (target === null || typeof target !== 'object') {\n\t\tthrow new TypeError('target is not an object')\n\t}\n\tfor (var key in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, key)) {\n\t\t\ttarget[key] = source[key]\n\t\t}\n\t}\n\treturn target\n}\n\n/**\n * All mime types that are allowed as input to `DOMParser.parseFromString`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString#Argument02 MDN\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#domparsersupportedtype WHATWG HTML Spec\n * @see DOMParser.prototype.parseFromString\n */\nvar MIME_TYPE = freeze({\n\t/**\n\t * `text/html`, the only mime type that triggers treating an XML document as HTML.\n\t *\n\t * @see DOMParser.SupportedType.isHTML\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring WHATWG HTML Spec\n\t */\n\tHTML: 'text/html',\n\n\t/**\n\t * Helper method to check a mime type if it indicates an HTML document\n\t *\n\t * @param {string} [value]\n\t * @returns {boolean}\n\t *\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring \t */\n\tisHTML: function (value) {\n\t\treturn value === MIME_TYPE.HTML\n\t},\n\n\t/**\n\t * `application/xml`, the standard mime type for XML documents.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xml IANA MimeType registration\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.1 RFC 7303\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_APPLICATION: 'application/xml',\n\n\t/**\n\t * `text/html`, an alias for `application/xml`.\n\t *\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.2 RFC 7303\n\t * @see https://www.iana.org/assignments/media-types/text/xml IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_TEXT: 'text/xml',\n\n\t/**\n\t * `application/xhtml+xml`, indicates an XML document that has the default HTML namespace,\n\t * but is parsed as an XML document.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xhtml+xml IANA MimeType registration\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument WHATWG DOM Spec\n\t * @see https://en.wikipedia.org/wiki/XHTML Wikipedia\n\t */\n\tXML_XHTML_APPLICATION: 'application/xhtml+xml',\n\n\t/**\n\t * `image/svg+xml`,\n\t *\n\t * @see https://www.iana.org/assignments/media-types/image/svg+xml IANA MimeType registration\n\t * @see https://www.w3.org/TR/SVG11/ W3C SVG 1.1\n\t * @see https://en.wikipedia.org/wiki/Scalable_Vector_Graphics Wikipedia\n\t */\n\tXML_SVG_IMAGE: 'image/svg+xml',\n})\n\n/**\n * Namespaces that are used in this code base.\n *\n * @see http://www.w3.org/TR/REC-xml-names\n */\nvar NAMESPACE = freeze({\n\t/**\n\t * The XHTML namespace.\n\t *\n\t * @see http://www.w3.org/1999/xhtml\n\t */\n\tHTML: 'http://www.w3.org/1999/xhtml',\n\n\t/**\n\t * Checks if `uri` equals `NAMESPACE.HTML`.\n\t *\n\t * @param {string} [uri]\n\t *\n\t * @see NAMESPACE.HTML\n\t */\n\tisHTML: function (uri) {\n\t\treturn uri === NAMESPACE.HTML\n\t},\n\n\t/**\n\t * The SVG namespace.\n\t *\n\t * @see http://www.w3.org/2000/svg\n\t */\n\tSVG: 'http://www.w3.org/2000/svg',\n\n\t/**\n\t * The `xml:` namespace.\n\t *\n\t * @see http://www.w3.org/XML/1998/namespace\n\t */\n\tXML: 'http://www.w3.org/XML/1998/namespace',\n\n\t/**\n\t * The `xmlns:` namespace\n\t *\n\t * @see https://www.w3.org/2000/xmlns/\n\t */\n\tXMLNS: 'http://www.w3.org/2000/xmlns/',\n})\n\nexports.assign = assign;\nexports.find = find;\nexports.freeze = freeze;\nexports.MIME_TYPE = MIME_TYPE;\nexports.NAMESPACE = NAMESPACE;\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,KAAK,IAAI,EAAE,SAAS,EAAE,EAAE;IAChC,IAAI,OAAO,WAAW;QACrB,KAAK,MAAM,SAAS;IACrB;IACA,IAAI,QAAQ,OAAO,GAAG,IAAI,KAAK,YAAY;QAC1C,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;IAC3B;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACrC,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,IAAI;YAClD,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,UAAU,IAAI,CAAC,WAAW,MAAM,GAAG,OAAO;gBAC7C,OAAO;YACR;QACD;IACD;AACD;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,OAAO,MAAM,EAAE,EAAE;IACzB,IAAI,OAAO,WAAW;QACrB,KAAK;IACN;IACA,OAAO,MAAM,OAAO,GAAG,MAAM,KAAK,aAAa,GAAG,MAAM,CAAC,UAAU;AACpE;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,OAAO,MAAM,EAAE,MAAM;IAC7B,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU;QAClD,MAAM,IAAI,UAAU;IACrB;IACA,IAAK,IAAI,OAAO,OAAQ;QACvB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,MAAM;YACtD,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC1B;IACD;IACA,OAAO;AACR;AAEA;;;;;;CAMC,GACD,IAAI,YAAY,OAAO;IACtB;;;;;;;;EAQC,GACD,MAAM;IAEN;;;;;;;;;8GAS6G,GAC7G,QAAQ,SAAU,KAAK;QACtB,OAAO,UAAU,UAAU,IAAI;IAChC;IAEA;;;;;;EAMC,GACD,iBAAiB;IAEjB;;;;;;EAMC,GACD,UAAU;IAEV;;;;;;;EAOC,GACD,uBAAuB;IAEvB;;;;;;EAMC,GACD,eAAe;AAChB;AAEA;;;;CAIC,GACD,IAAI,YAAY,OAAO;IACtB;;;;EAIC,GACD,MAAM;IAEN;;;;;;EAMC,GACD,QAAQ,SAAU,GAAG;QACpB,OAAO,QAAQ,UAAU,IAAI;IAC9B;IAEA;;;;EAIC,GACD,KAAK;IAEL;;;;EAIC,GACD,KAAK;IAEL;;;;EAIC,GACD,OAAO;AACR;AAEA,QAAQ,MAAM,GAAG;AACjB,QAAQ,IAAI,GAAG;AACf,QAAQ,MAAM,GAAG;AACjB,QAAQ,SAAS,GAAG;AACpB,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/dom.js"], "sourcesContent": ["var conventions = require(\"./conventions\");\n\nvar find = conventions.find;\nvar NAMESPACE = conventions.NAMESPACE;\n\n/**\n * A prerequisite for `[].filter`, to drop elements that are empty\n * @param {string} input\n * @returns {boolean}\n */\nfunction notEmptyString (input) {\n\treturn input !== ''\n}\n/**\n * @see https://infra.spec.whatwg.org/#split-on-ascii-whitespace\n * @see https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * @param {string} input\n * @returns {string[]} (can be empty)\n */\nfunction splitOnASCIIWhitespace(input) {\n\t// U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, U+0020 SPACE\n\treturn input ? input.split(/[\\t\\n\\f\\r ]+/).filter(notEmptyString) : []\n}\n\n/**\n * Adds element as a key to current if it is not already present.\n *\n * @param {Record<string, boolean | undefined>} current\n * @param {string} element\n * @returns {Record<string, boolean | undefined>}\n */\nfunction orderedSetReducer (current, element) {\n\tif (!current.hasOwnProperty(element)) {\n\t\tcurrent[element] = true;\n\t}\n\treturn current;\n}\n\n/**\n * @see https://infra.spec.whatwg.org/#ordered-set\n * @param {string} input\n * @returns {string[]}\n */\nfunction toOrderedSet(input) {\n\tif (!input) return [];\n\tvar list = splitOnASCIIWhitespace(input);\n\treturn Object.keys(list.reduce(orderedSetReducer, {}))\n}\n\n/**\n * Uses `list.indexOf` to implement something like `Array.prototype.includes`,\n * which we can not rely on being available.\n *\n * @param {any[]} list\n * @returns {function(any): boolean}\n */\nfunction arrayIncludes (list) {\n\treturn function(element) {\n\t\treturn list && list.indexOf(element) !== -1;\n\t}\n}\n\nfunction copy(src,dest){\n\tfor(var p in src){\n\t\tif (Object.prototype.hasOwnProperty.call(src, p)) {\n\t\t\tdest[p] = src[p];\n\t\t}\n\t}\n}\n\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknown Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\n\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0,\n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long\n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index.\n\t */\n\titem: function(index) {\n\t\treturn index >= 0 && index < this.length ? this[index] : null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t},\n\t/**\n\t * @private\n\t * @param {function (Node):boolean} predicate\n\t * @returns {Node[]}\n\t */\n\tfilter: function (predicate) {\n\t\treturn Array.prototype.filter.call(this, predicate);\n\t},\n\t/**\n\t * @private\n\t * @param {Node} item\n\t * @returns {number}\n\t */\n\tindexOf: function (item) {\n\t\treturn Array.prototype.indexOf.call(this, item);\n\t},\n};\n\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif (list._inc !== inc) {\n\t\tvar ls = list._refresh(list._node);\n\t\t__set__(list,'length',ls.length);\n\t\tif (!list.$$length || ls.length < list.$$length) {\n\t\t\tfor (var i = ls.length; i in list; i++) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\t\t\tdelete list[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i] || null;\n}\n\n_extends(LiveNodeList,NodeList);\n\n/**\n * Objects implementing the NamedNodeMap interface are used\n * to represent collections of nodes that can be accessed by name.\n * Note that NamedNodeMap does not inherit from NodeList;\n * NamedNodeMaps are not maintained in any particular order.\n * Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index,\n * but this is simply to allow convenient enumeration of the contents of a NamedNodeMap,\n * and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities\n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow new DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\n\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n\n/**\n * The DOMImplementation interface represents an object providing methods\n * which are not dependent on any particular document.\n * Such an object is returned by the `Document.implementation` property.\n *\n * __The individual methods describe the differences compared to the specs.__\n *\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation MDN\n * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490 DOM Level 1 Core (Initial)\n * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#ID-102161490 DOM Level 2 Core\n * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#ID-102161490 DOM Level 3 Core\n * @see https://dom.spec.whatwg.org/#domimplementation DOM Living Standard\n */\nfunction DOMImplementation() {\n}\n\nDOMImplementation.prototype = {\n\t/**\n\t * The DOMImplementation.hasFeature() method returns a Boolean flag indicating if a given feature is supported.\n\t * The different implementations fairly diverged in what kind of features were reported.\n\t * The latest version of the spec settled to force this method to always return true, where the functionality was accurate and in use.\n\t *\n\t * @deprecated It is deprecated and modern browsers return true in all cases.\n\t *\n\t * @param {string} feature\n\t * @param {string} [version]\n\t * @returns {boolean} always true\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/hasFeature MDN\n\t * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-5CED94D7 DOM Level 1 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-hasfeature DOM Living Standard\n\t */\n\thasFeature: function(feature, version) {\n\t\t\treturn true;\n\t},\n\t/**\n\t * Creates an XML Document object of the specified type with its document element.\n\t *\n\t * __It behaves slightly different from the description in the living standard__:\n\t * - There is no interface/class `XMLDocument`, it returns a `Document` instance.\n\t * - `contentType`, `encoding`, `mode`, `origin`, `url` fields are currently not declared.\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string|null} namespaceURI\n\t * @param {string} qualifiedName\n\t * @param {DocumentType=null} doctype\n\t * @returns {Document}\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocument MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocument DOM Level 2 Core (initial)\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument  DOM Level 2 Core\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocument: function(namespaceURI,  qualifiedName, doctype){\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype || null;\n\t\tif (doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif (qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI, qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t/**\n\t * Returns a doctype, with the given `qualifiedName`, `publicId`, and `systemId`.\n\t *\n\t * __This behavior is slightly different from the in the specs__:\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string} qualifiedName\n\t * @param {string} [publicId]\n\t * @param {string} [systemId]\n\t * @returns {DocumentType} which can either be used with `DOMImplementation.createDocument` upon document creation\n\t * \t\t\t\t  or can be put into the document via methods like `Node.insertBefore()` or `Node.replaceChild()`\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocumentType MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocType DOM Level 2 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocumenttype DOM Living Standard\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocumentType: function(qualifiedName, publicId, systemId){\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId || '';\n\t\tnode.systemId = systemId || '';\n\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises\n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises\n\t\t_insertBefore(this, newChild,oldChild, assertPreReplacementValidityInDocument);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n\t/**\n\t * Look up the prefix associated to the given namespace URI, starting from this node.\n\t * **The default namespace declarations are ignored by this method.**\n\t * See Namespace Prefix Lookup for details on the algorithm used by this method.\n\t *\n\t * _Note: The implementation seems to be incomplete when compared to the algorithm described in the specs._\n\t *\n\t * @param {string | null} namespaceURI\n\t * @returns {string | null}\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#Node3-lookupNamespacePrefix\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/namespaces-algorithms.html#lookupNamespacePrefixAlgo\n\t * @see https://dom.spec.whatwg.org/#dom-node-lookupprefix\n\t * @see https://github.com/xmldom/xmldom/issues/322\n\t */\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(map, n) && map[n] === namespaceURI) {\n\t\t\t\t\t\t\treturn n;\n\t\t\t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(Object.prototype.hasOwnProperty.call(map, prefix)){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n\tthis.ownerDocument = this;\n}\n\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\n\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\n\n/**\n * Updates `el.childNodes`, updating the indexed items and it's `length`.\n * Passing `newChild` means it will be appended.\n * Otherwise it's assumed that an item has been removed,\n * and `el.firstNode` and it's `.nextSibling` are used\n * to walk the current list of child nodes.\n *\n * @param {Document} doc\n * @param {Node} el\n * @param {Node} [newChild]\n * @private\n */\nfunction _onUpdateChild (doc, el, newChild) {\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif (newChild) {\n\t\t\tcs[cs.length++] = newChild;\n\t\t} else {\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile (child) {\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild = child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t\tdelete cs[cs.length];\n\t\t}\n\t}\n}\n\n/**\n * Removes the connections between `parentNode` and `child`\n * and any existing `child.previousSibling` or `child.nextSibling`.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n *\n * @param {Node} parentNode\n * @param {Node} child\n * @returns {Node} the child that was removed.\n * @private\n */\nfunction _removeChild (parentNode, child) {\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif (previous) {\n\t\tprevious.nextSibling = next;\n\t} else {\n\t\tparentNode.firstChild = next;\n\t}\n\tif (next) {\n\t\tnext.previousSibling = previous;\n\t} else {\n\t\tparentNode.lastChild = previous;\n\t}\n\tchild.parentNode = null;\n\tchild.previousSibling = null;\n\tchild.nextSibling = null;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode);\n\treturn child;\n}\n\n/**\n * Returns `true` if `node` can be a parent for insertion.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasValidParentNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(node.nodeType === Node.DOCUMENT_NODE || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE)\n\t);\n}\n\n/**\n * Returns `true` if `node` can be inserted according to it's `nodeType`.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasInsertableNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(isElementNode(node) ||\n\t\t\tisTextNode(node) ||\n\t\t\tisDocTypeNode(node) ||\n\t\t\tnode.nodeType === Node.DOCUMENT_FRAGMENT_NODE ||\n\t\t\tnode.nodeType === Node.COMMENT_NODE ||\n\t\t\tnode.nodeType === Node.PROCESSING_INSTRUCTION_NODE)\n\t);\n}\n\n/**\n * Returns true if `node` is a DOCTYPE node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isDocTypeNode(node) {\n\treturn node && node.nodeType === Node.DOCUMENT_TYPE_NODE;\n}\n\n/**\n * Returns true if the node is an element\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isElementNode(node) {\n\treturn node && node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Returns true if `node` is a text node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isTextNode(node) {\n\treturn node && node.nodeType === Node.TEXT_NODE;\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Document} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementInsertionPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\tif (find(parentChildNodes, isElementNode) || isDocTypeNode(child)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Node} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementReplacementPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\n\tfunction hasElementChildThatIsNotChild(node) {\n\t\treturn isElementNode(node) && node !== child;\n\t}\n\n\tif (find(parentChildNodes, hasElementChildThatIsNotChild)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * @private\n * Steps 1-5 of the checks before inserting and before replacing a child are the same.\n *\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidity1to5(parent, node, child) {\n\t// 1. If `parent` is not a Document, DocumentFragment, or Element node, then throw a \"HierarchyRequestError\" DOMException.\n\tif (!hasValidParentNodeType(parent)) {\n\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected parent node type ' + parent.nodeType);\n\t}\n\t// 2. If `node` is a host-including inclusive ancestor of `parent`, then throw a \"HierarchyRequestError\" DOMException.\n\t// not implemented!\n\t// 3. If `child` is non-null and its parent is not `parent`, then throw a \"NotFoundError\" DOMException.\n\tif (child && child.parentNode !== parent) {\n\t\tthrow new DOMException(NOT_FOUND_ERR, 'child not in parent');\n\t}\n\tif (\n\t\t// 4. If `node` is not a DocumentFragment, DocumentType, Element, or CharacterData node, then throw a \"HierarchyRequestError\" DOMException.\n\t\t!hasInsertableNodeType(node) ||\n\t\t// 5. If either `node` is a Text node and `parent` is a document,\n\t\t// the sax parser currently adds top level text nodes, this will be fixed in 0.9.0\n\t\t// || (node.nodeType === Node.TEXT_NODE && parent.nodeType === Node.DOCUMENT_NODE)\n\t\t// or `node` is a doctype and `parent` is not a document, then throw a \"HierarchyRequestError\" DOMException.\n\t\t(isDocTypeNode(node) && parent.nodeType !== Node.DOCUMENT_NODE)\n\t) {\n\t\tthrow new DOMException(\n\t\t\tHIERARCHY_REQUEST_ERR,\n\t\t\t'Unexpected node type ' + node.nodeType + ' for parent node type ' + parent.nodeType\n\t\t);\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If node has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child,\n\t\t// `child` is a doctype, or `child` is non-null and a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child, `child` is a doctype,\n\t\t// or `child` is non-null and a doctype is following `child`.\n\t\tif (!isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\t// `parent` has a doctype child,\n\t\tif (find(parentChildNodes, isDocTypeNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// `child` is non-null and an element is preceding `child`,\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t\t// or `child` is null and `parent` has an element child.\n\t\tif (!child && parentElementChild) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can not be appended since element is present');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreReplacementValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If `node` has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (!isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\tfunction hasDoctypeChildThatIsNotChild(node) {\n\t\t\treturn isDocTypeNode(node) && node !== child;\n\t\t}\n\n\t\t// `parent` has a doctype child that is not `child`,\n\t\tif (find(parentChildNodes, hasDoctypeChildThatIsNotChild)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// or an element is preceding `child`.\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction _insertBefore(parent, node, child, _inDocumentAssertion) {\n\t// To ensure pre-insertion validity of a node into a parent before a child, run these steps:\n\tassertPreInsertionValidity1to5(parent, node, child);\n\n\t// If parent is a document, and any of the statements below, switched on the interface node implements,\n\t// are true, then throw a \"HierarchyRequestError\" DOMException.\n\tif (parent.nodeType === Node.DOCUMENT_NODE) {\n\t\t(_inDocumentAssertion || assertPreInsertionValidityInDocument)(parent, node, child);\n\t}\n\n\tvar cp = node.parentNode;\n\tif(cp){\n\t\tcp.removeChild(node);//remove and update\n\t}\n\tif(node.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = node.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn node;\n\t\t}\n\t\tvar newLast = node.lastChild;\n\t}else{\n\t\tnewFirst = newLast = node;\n\t}\n\tvar pre = child ? child.previousSibling : parent.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = child;\n\n\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparent.firstChild = newFirst;\n\t}\n\tif(child == null){\n\t\tparent.lastChild = newLast;\n\t}else{\n\t\tchild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parent;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parent.ownerDocument||parent, parent);\n\t//console.log(parent.lastChild.nextSibling == null)\n\tif (node.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnode.firstChild = node.lastChild = null;\n\t}\n\treturn node;\n}\n\n/**\n * Appends `newChild` to `parentNode`.\n * If `newChild` is already connected to a `parentNode` it is first removed from it.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n * @param {Node} parentNode\n * @param {Node} newChild\n * @returns {Node}\n * @private\n */\nfunction _appendSingleChild (parentNode, newChild) {\n\tif (newChild.parentNode) {\n\t\tnewChild.parentNode.removeChild(newChild);\n\t}\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = parentNode.lastChild;\n\tnewChild.nextSibling = null;\n\tif (newChild.previousSibling) {\n\t\tnewChild.previousSibling.nextSibling = newChild;\n\t} else {\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n\treturn newChild;\n}\n\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\t/**\n\t * The DocumentType node of the document.\n\t *\n\t * @readonly\n\t * @type DocumentType\n\t */\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\n\tinsertBefore :  function(newChild, refChild){//raises\n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\t_insertBefore(this, newChild, refChild);\n\t\tnewChild.ownerDocument = this;\n\t\tif (this.documentElement === null && newChild.nodeType === ELEMENT_NODE) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\n\t\treturn newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\treplaceChild: function (newChild, oldChild) {\n\t\t//raises\n\t\t_insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n\t\tnewChild.ownerDocument = this;\n\t\tif (oldChild) {\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t\tif (isElementNode(newChild)) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\n\t/**\n\t * The `getElementsByClassName` method of `Document` interface returns an array-like object\n\t * of all child elements which have **all** of the given class name(s).\n\t *\n\t * Returns an empty list if `classeNames` is an empty string or only contains HTML white space characters.\n\t *\n\t *\n\t * Warning: This is a live LiveNodeList.\n\t * Changes in the DOM will reflect in the array as the changes occur.\n\t * If an element selected by this array no longer qualifies for the selector,\n\t * it will automatically be removed. Be aware of this for iteration purposes.\n\t *\n\t * @param {string} classNames is a string representing the class name(s) to match; multiple class names are separated by (ASCII-)whitespace\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByClassName\n\t * @see https://dom.spec.whatwg.org/#concept-getelementsbyclassname\n\t */\n\tgetElementsByClassName: function(classNames) {\n\t\tvar classNamesSet = toOrderedSet(classNames)\n\t\treturn new LiveNodeList(this, function(base) {\n\t\t\tvar ls = [];\n\t\t\tif (classNamesSet.length > 0) {\n\t\t\t\t_visitNode(base.documentElement, function(node) {\n\t\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE) {\n\t\t\t\t\t\tvar nodeClassNames = node.getAttribute('class')\n\t\t\t\t\t\t// can be null if the attribute does not exist\n\t\t\t\t\t\tif (nodeClassNames) {\n\t\t\t\t\t\t\t// before splitting and iterating just compare them for the most common case\n\t\t\t\t\t\t\tvar matches = classNames === nodeClassNames;\n\t\t\t\t\t\t\tif (!matches) {\n\t\t\t\t\t\t\t\tvar nodeClassNamesSet = toOrderedSet(nodeClassNames)\n\t\t\t\t\t\t\t\tmatches = classNamesSet.every(arrayIncludes(nodeClassNamesSet))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(matches) {\n\t\t\t\t\t\t\t\tls.push(node);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn ls;\n\t\t});\n\t},\n\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.localName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.nodeName = node.target = target;\n\t\tnode.nodeValue = node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9 && this.documentElement || this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\n\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix || '';\n\tvar uri = node.namespaceURI;\n\t// According to [Namespaces in XML 1.0](https://www.w3.org/TR/REC-xml-names/#ns-using) ,\n\t// and more specifically https://www.w3.org/TR/REC-xml-names/#nsc-NoPrefixUndecl :\n\t// > In a namespace declaration for a prefix [...], the attribute value MUST NOT be empty.\n\t// in a similar manner [Namespaces in XML 1.1](https://www.w3.org/TR/xml-names11/#ns-using)\n\t// and more specifically https://www.w3.org/TR/xml-names11/#nsc-NSDeclared :\n\t// > [...] Furthermore, the attribute value [...] must not be an empty string.\n\t// so serializing empty namespace value like xmlns:ds=\"\" would produce an invalid XML document.\n\tif (!uri) {\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === NAMESPACE.XML || uri === NAMESPACE.XMLNS) {\n\t\treturn false;\n\t}\n\n\tvar i = visibleNamespaces.length\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\tif (ns.prefix === prefix) {\n\t\t\treturn ns.namespace !== uri;\n\t\t}\n\t}\n\treturn true;\n}\n/**\n * Well-formed constraint: No < in Attribute Values\n * > The replacement text of any entity referred to directly or indirectly\n * > in an attribute value must not contain a <.\n * @see https://www.w3.org/TR/xml11/#CleanAttrVals\n * @see https://www.w3.org/TR/xml11/#NT-AttValue\n *\n * Literal whitespace other than space that appear in attribute values\n * are serialized as their entity references, so they will be preserved.\n * (In contrast to whitespace literals in the input which are normalized to spaces)\n * @see https://www.w3.org/TR/xml11/#AVNormalize\n * @see https://w3c.github.io/DOM-Parsing/#serializing-an-element-s-attributes\n */\nfunction addSerializedAttribute(buf, qualifiedName, value) {\n\tbuf.push(' ', qualifiedName, '=\"', value.replace(/[<>&\"\\t\\n\\r]/g, _xmlEncoder), '\"')\n}\n\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif (!visibleNamespaces) {\n\t\tvisibleNamespaces = [];\n\t}\n\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\n\t\tisHTML = NAMESPACE.isHTML(node.namespaceURI) || isHTML\n\n\t\tvar prefixedNodeName = nodeName\n\t\tif (!isHTML && !node.prefix && node.namespaceURI) {\n\t\t\tvar defaultNS\n\t\t\t// lookup current default ns from `xmlns` attribute\n\t\t\tfor (var ai = 0; ai < attrs.length; ai++) {\n\t\t\t\tif (attrs.item(ai).name === 'xmlns') {\n\t\t\t\t\tdefaultNS = attrs.item(ai).value\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!defaultNS) {\n\t\t\t\t// lookup current default ns in visibleNamespaces\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.prefix === '' && namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tdefaultNS = namespace.namespace\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (defaultNS !== node.namespaceURI) {\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tif (namespace.prefix) {\n\t\t\t\t\t\t\tprefixedNodeName = namespace.prefix + ':' + nodeName\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tbuf.push('<', prefixedNodeName);\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\n\t\t// add namespace for current node\n\t\tif (nodeName === prefixedNodeName && needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t}\n\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',prefixedNodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\treturn addSerializedAttribute(buf, node.name, node.value);\n\tcase TEXT_NODE:\n\t\t/**\n\t\t * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n\t\t * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n\t\t * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n\t\t * `&amp;` and `&lt;` respectively.\n\t\t * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n\t\t * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n\t\t * when that string is not marking the end of a CDATA section.\n\t\t *\n\t\t * In the content of elements, character data is any string of characters\n\t\t * which does not contain the start-delimiter of any markup\n\t\t * and does not include the CDATA-section-close delimiter, `]]>`.\n\t\t *\n\t\t * @see https://www.w3.org/TR/xml/#NT-CharData\n\t\t * @see https://w3c.github.io/DOM-Parsing/#xml-serializing-a-text-node\n\t\t */\n\t\treturn buf.push(node.data\n\t\t\t.replace(/[<&>]/g,_xmlEncoder)\n\t\t);\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC ', pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push(' ', sysid);\n\t\t\t}\n\t\t\tbuf.push('>');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM ', sysid, '>');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor (var n in node) {\n\t\tif (Object.prototype.hasOwnProperty.call(node, n)) {\n\t\t\tvar v = node[n];\n\t\t\tif (typeof v != \"object\") {\n\t\t\t\tif (v != node2[n]) {\n\t\t\t\t\tnode2[n] = v;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.DocumentType = DocumentType;\n\texports.DOMException = DOMException;\n\texports.DOMImplementation = DOMImplementation;\n\texports.Element = Element;\n\texports.Node = Node;\n\texports.NodeList = NodeList;\n\texports.XMLSerializer = XMLSerializer;\n//}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI,OAAO,YAAY,IAAI;AAC3B,IAAI,YAAY,YAAY,SAAS;AAErC;;;;CAIC,GACD,SAAS,eAAgB,KAAK;IAC7B,OAAO,UAAU;AAClB;AACA;;;;;;CAMC,GACD,SAAS,uBAAuB,KAAK;IACpC,4DAA4D;IAC5D,OAAO,QAAQ,MAAM,KAAK,CAAC,gBAAgB,MAAM,CAAC,kBAAkB,EAAE;AACvE;AAEA;;;;;;CAMC,GACD,SAAS,kBAAmB,OAAO,EAAE,OAAO;IAC3C,IAAI,CAAC,QAAQ,cAAc,CAAC,UAAU;QACrC,OAAO,CAAC,QAAQ,GAAG;IACpB;IACA,OAAO;AACR;AAEA;;;;CAIC,GACD,SAAS,aAAa,KAAK;IAC1B,IAAI,CAAC,OAAO,OAAO,EAAE;IACrB,IAAI,OAAO,uBAAuB;IAClC,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,mBAAmB,CAAC;AACpD;AAEA;;;;;;CAMC,GACD,SAAS,cAAe,IAAI;IAC3B,OAAO,SAAS,OAAO;QACtB,OAAO,QAAQ,KAAK,OAAO,CAAC,aAAa,CAAC;IAC3C;AACD;AAEA,SAAS,KAAK,GAAG,EAAC,IAAI;IACrB,IAAI,IAAI,KAAK,IAAI;QAChB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI;YACjD,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACjB;IACD;AACD;AAEA;;;CAGC,GACD,SAAS,SAAS,KAAK,EAAC,KAAK;IAC5B,IAAI,KAAK,MAAM,SAAS;IACxB,IAAG,CAAC,CAAC,cAAc,KAAK,GAAE;QACzB,SAAS,KAAI;;QACb,EAAE,SAAS,GAAG,MAAM,SAAS;QAC7B,IAAI,IAAI;QACR,KAAK,IAAG;QACR,MAAM,SAAS,GAAG,KAAK;IACxB;IACA,IAAG,GAAG,WAAW,IAAI,OAAM;QAC1B,IAAG,OAAO,SAAS,YAAW;YAC7B,QAAQ,KAAK,CAAC,mBAAiB;QAChC;QACA,GAAG,WAAW,GAAG;IAClB;AACD;AAEA,aAAa;AACb,IAAI,WAAW,CAAC;AAChB,IAAI,eAA8B,SAAS,YAAY,GAAkB;AACzE,IAAI,iBAA8B,SAAS,cAAc,GAAgB;AACzE,IAAI,YAA8B,SAAS,SAAS,GAAqB;AACzE,IAAI,qBAA8B,SAAS,kBAAkB,GAAY;AACzE,IAAI,wBAA8B,SAAS,qBAAqB,GAAS;AACzE,IAAI,cAA8B,SAAS,WAAW,GAAmB;AACzE,IAAI,8BAA8B,SAAS,2BAA2B,GAAG;AACzE,IAAI,eAA8B,SAAS,YAAY,GAAkB;AACzE,IAAI,gBAA8B,SAAS,aAAa,GAAiB;AACzE,IAAI,qBAA8B,SAAS,kBAAkB,GAAY;AACzE,IAAI,yBAA8B,SAAS,sBAAsB,GAAQ;AACzE,IAAI,gBAA8B,SAAS,aAAa,GAAiB;AAEzE,gBAAgB;AAChB,IAAI,gBAAgB,CAAC;AACrB,IAAI,mBAAmB,CAAC;AACxB,IAAI,iBAA8B,cAAc,cAAc,GAAgB,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,oBAAoB,CAAC;AACzH,IAAI,qBAA8B,cAAc,kBAAkB,GAAY,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,wBAAwB,CAAC;AAC7H,IAAI,wBAA8B,cAAc,qBAAqB,GAAS,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,2BAA2B,CAAC;AAChI,IAAI,qBAA8B,cAAc,kBAAkB,GAAY,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,kBAAkB,CAAC;AACvH,IAAI,wBAA8B,cAAc,qBAAqB,GAAS,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,qBAAqB,CAAC;AAC1H,IAAI,sBAA8B,cAAc,mBAAmB,GAAW,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,mBAAmB,CAAC;AACxH,IAAI,8BAA8B,cAAc,2BAA2B,GAAG,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,2BAA2B,CAAC;AAChI,IAAI,gBAA8B,cAAc,aAAa,GAAiB,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,aAAa,CAAC;AAClH,IAAI,oBAA8B,cAAc,iBAAiB,GAAa,CAAC,AAAC,gBAAgB,CAAC,EAAE,GAAC,iBAAiB,CAAC;AACtH,IAAI,sBAA8B,cAAc,mBAAmB,GAAW,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,oBAAoB,EAAE;AAC3H,QAAQ;AACR,IAAI,oBAA4B,cAAc,iBAAiB,GAAW,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,iBAAiB,EAAE;AACpH,IAAI,aAA4B,cAAc,UAAU,GAAkB,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,gBAAgB,EAAE;AACnH,IAAI,2BAA4B,cAAc,wBAAwB,GAAI,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,wBAAwB,EAAE;AAC3H,IAAI,gBAA4B,cAAc,aAAa,GAAc,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,qBAAqB,EAAE;AACvH,IAAI,qBAA4B,cAAc,kBAAkB,GAAS,CAAC,AAAC,gBAAgB,CAAC,GAAG,GAAC,kBAAkB,EAAE;AAEpH;;;;;CAKC,GACD,SAAS,aAAa,IAAI,EAAE,OAAO;IAClC,IAAG,mBAAmB,OAAM;QAC3B,IAAI,QAAQ;IACb,OAAK;QACJ,QAAQ,IAAI;QACZ,MAAM,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC,KAAK;QACvC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,KAAK;QACrC,IAAG,MAAM,iBAAiB,EAAE,MAAM,iBAAiB,CAAC,IAAI,EAAE;IAC3D;IACA,MAAM,IAAI,GAAG;IACb,IAAG,SAAS,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO;IACjD,OAAO;AACR;;AACA,aAAa,SAAS,GAAG,MAAM,SAAS;AACxC,KAAK,eAAc;AAEnB;;;;CAIC,GACD,SAAS,YACT;;AACA,SAAS,SAAS,GAAG;IACpB;;;EAGC,GACD,QAAO;IACP;;;;;;;EAOC,GACD,MAAM,SAAS,KAAK;QACnB,OAAO,SAAS,KAAK,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG;IAC1D;IACA,UAAS,SAAS,MAAM,EAAC,UAAU;QAClC,IAAI,IAAI,MAAM,EAAE,EAAE,IAAI,GAAE,IAAE,IAAI,CAAC,MAAM,EAAC,IAAI;YACzC,kBAAkB,IAAI,CAAC,EAAE,EAAC,KAAI,QAAO;QACtC;QACA,OAAO,IAAI,IAAI,CAAC;IACjB;IACA;;;;EAIC,GACD,QAAQ,SAAU,SAAS;QAC1B,OAAO,MAAM,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;IAC1C;IACA;;;;EAIC,GACD,SAAS,SAAU,IAAI;QACtB,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;IAC3C;AACD;AAEA,SAAS,aAAa,IAAI,EAAC,OAAO;IACjC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,QAAQ,GAAG;IAChB,gBAAgB,IAAI;AACrB;AACA,SAAS,gBAAgB,IAAI;IAC5B,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,CAAC,aAAa,CAAC,IAAI;IAC1D,IAAI,KAAK,IAAI,KAAK,KAAK;QACtB,IAAI,KAAK,KAAK,QAAQ,CAAC,KAAK,KAAK;QACjC,QAAQ,MAAK,UAAS,GAAG,MAAM;QAC/B,IAAI,CAAC,KAAK,QAAQ,IAAI,GAAG,MAAM,GAAG,KAAK,QAAQ,EAAE;YAChD,IAAK,IAAI,IAAI,GAAG,MAAM,EAAE,KAAK,MAAM,IAAK;gBACvC,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,IAAI;oBAClD,OAAO,IAAI,CAAC,EAAE;gBACf;YACD;QACD;QACA,KAAK,IAAG;QACR,KAAK,IAAI,GAAG;IACb;AACD;AACA,aAAa,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC;IACvC,gBAAgB,IAAI;IACpB,OAAO,IAAI,CAAC,EAAE,IAAI;AACnB;AAEA,SAAS,cAAa;AAEtB;;;;;;;;;;CAUC,GACD,SAAS,gBACT;;AAEA,SAAS,eAAe,IAAI,EAAC,IAAI;IAChC,IAAI,IAAI,KAAK,MAAM;IACnB,MAAM,IAAI;QACT,IAAG,IAAI,CAAC,EAAE,KAAK,MAAK;YAAC,OAAO;QAAC;IAC9B;AACD;AAEA,SAAS,cAAc,EAAE,EAAC,IAAI,EAAC,OAAO,EAAC,OAAO;IAC7C,IAAG,SAAQ;QACV,IAAI,CAAC,eAAe,MAAK,SAAS,GAAG;IACtC,OAAK;QACJ,IAAI,CAAC,KAAK,MAAM,GAAG,GAAG;IACvB;IACA,IAAG,IAAG;QACL,QAAQ,YAAY,GAAG;QACvB,IAAI,MAAM,GAAG,aAAa;QAC1B,IAAG,KAAI;YACN,WAAW,mBAAmB,KAAI,IAAG;YACrC,gBAAgB,KAAI,IAAG;QACxB;IACD;AACD;AACA,SAAS,iBAAiB,EAAE,EAAC,IAAI,EAAC,IAAI;IACrC,kCAAkC;IAClC,IAAI,IAAI,eAAe,MAAK;IAC5B,IAAG,KAAG,GAAE;QACP,IAAI,YAAY,KAAK,MAAM,GAAC;QAC5B,MAAM,IAAE,UAAU;YACjB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE;QACpB;QACA,KAAK,MAAM,GAAG;QACd,IAAG,IAAG;YACL,IAAI,MAAM,GAAG,aAAa;YAC1B,IAAG,KAAI;gBACN,mBAAmB,KAAI,IAAG;gBAC1B,KAAK,YAAY,GAAG;YACrB;QACD;IACD,OAAK;QACJ,MAAM,IAAI,aAAa,eAAc,IAAI,MAAM,GAAG,OAAO,GAAC,MAAI;IAC/D;AACD;AACA,aAAa,SAAS,GAAG;IACxB,QAAO;IACP,MAAK,SAAS,SAAS,CAAC,IAAI;IAC5B,cAAc,SAAS,GAAG;QAC3B,6CAA6C;QAC7C,iBAAiB;QACjB,KAAK;QACH,eAAe;QACf,IAAI,IAAI,IAAI,CAAC,MAAM;QACnB,MAAM,IAAI;YACT,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,gCAAgC;YAChC,IAAG,KAAK,QAAQ,IAAI,KAAI;gBACvB,OAAO;YACR;QACD;IACD;IACA,cAAc,SAAS,IAAI;QAC1B,IAAI,KAAK,KAAK,YAAY;QAC1B,IAAG,MAAM,MAAI,IAAI,CAAC,aAAa,EAAC;YAC/B,MAAM,IAAI,aAAa;QACxB;QACA,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,QAAQ;QAC7C,cAAc,IAAI,CAAC,aAAa,EAAC,IAAI,EAAC,MAAK;QAC3C,OAAO;IACR;IACA,gBAAgB,GAChB,gBAAgB,SAAS,IAAI;QAC5B,IAAI,KAAK,KAAK,YAAY,EAAE;QAC5B,IAAG,MAAM,MAAI,IAAI,CAAC,aAAa,EAAC;YAC/B,MAAM,IAAI,aAAa;QACxB;QACA,UAAU,IAAI,CAAC,cAAc,CAAC,KAAK,YAAY,EAAC,KAAK,SAAS;QAC9D,cAAc,IAAI,CAAC,aAAa,EAAC,IAAI,EAAC,MAAK;QAC3C,OAAO;IACR;IAEA,gBAAgB,GAChB,iBAAiB,SAAS,GAAG;QAC5B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,iBAAiB,IAAI,CAAC,aAAa,EAAC,IAAI,EAAC;QACzC,OAAO;IAGR;IAEA,YAAY;IACZ,mBAAkB,SAAS,YAAY,EAAC,SAAS;QAChD,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,cAAa;QAC5C,iBAAiB,IAAI,CAAC,aAAa,EAAC,IAAI,EAAC;QACzC,OAAO;IACR;IACA,gBAAgB,SAAS,YAAY,EAAE,SAAS;QAC/C,IAAI,IAAI,IAAI,CAAC,MAAM;QACnB,MAAM,IAAI;YACT,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAG,KAAK,SAAS,IAAI,aAAa,KAAK,YAAY,IAAI,cAAa;gBACnE,OAAO;YACR;QACD;QACA,OAAO;IACR;AACD;AAEA;;;;;;;;;;;;;;CAcC,GACD,SAAS,qBACT;AAEA,kBAAkB,SAAS,GAAG;IAC7B;;;;;;;;;;;;;;EAcC,GACD,YAAY,SAAS,OAAO,EAAE,OAAO;QACnC,OAAO;IACT;IACA;;;;;;;;;;;;;;;;;;;;;EAqBC,GACD,gBAAgB,SAAS,YAAY,EAAG,aAAa,EAAE,OAAO;QAC7D,IAAI,MAAM,IAAI;QACd,IAAI,cAAc,GAAG,IAAI;QACzB,IAAI,UAAU,GAAG,IAAI;QACrB,IAAI,OAAO,GAAG,WAAW;QACzB,IAAI,SAAQ;YACX,IAAI,WAAW,CAAC;QACjB;QACA,IAAI,eAAc;YACjB,IAAI,OAAO,IAAI,eAAe,CAAC,cAAc;YAC7C,IAAI,WAAW,CAAC;QACjB;QACA,OAAO;IACR;IACA;;;;;;;;;;;;;;;;;;;;EAoBC,GACD,oBAAoB,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ;QAC7D,IAAI,OAAO,IAAI;QACf,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,QAAQ,GAAG,YAAY;QAC5B,KAAK,QAAQ,GAAG,YAAY;QAE5B,OAAO;IACR;AACD;AAGA;;CAEC,GAED,SAAS,QACT;;AAEA,KAAK,SAAS,GAAG;IAChB,YAAa;IACb,WAAY;IACZ,iBAAkB;IAClB,aAAc;IACd,YAAa;IACb,YAAa;IACb,YAAa;IACb,eAAgB;IAChB,WAAY;IACZ,cAAe;IACf,QAAS;IACT,WAAY;IACZ,2BAA2B;IAC3B,cAAa,SAAS,QAAQ,EAAE,QAAQ;QACvC,OAAO,cAAc,IAAI,EAAC,UAAS;IACpC;IACA,cAAa,SAAS,QAAQ,EAAE,QAAQ;QACvC,cAAc,IAAI,EAAE,UAAS,UAAU;QACvC,IAAG,UAAS;YACX,IAAI,CAAC,WAAW,CAAC;QAClB;IACD;IACA,aAAY,SAAS,QAAQ;QAC5B,OAAO,aAAa,IAAI,EAAC;IAC1B;IACA,aAAY,SAAS,QAAQ;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,UAAS;IACnC;IACA,eAAc;QACb,OAAO,IAAI,CAAC,UAAU,IAAI;IAC3B;IACA,WAAU,SAAS,IAAI;QACtB,OAAO,UAAU,IAAI,CAAC,aAAa,IAAE,IAAI,EAAC,IAAI,EAAC;IAChD;IACA,2BAA2B;IAC3B,WAAU;QACT,IAAI,QAAQ,IAAI,CAAC,UAAU;QAC3B,MAAM,MAAM;YACX,IAAI,OAAO,MAAM,WAAW;YAC5B,IAAG,QAAQ,KAAK,QAAQ,IAAI,aAAa,MAAM,QAAQ,IAAI,WAAU;gBACpE,IAAI,CAAC,WAAW,CAAC;gBACjB,MAAM,UAAU,CAAC,KAAK,IAAI;YAC3B,OAAK;gBACJ,MAAM,SAAS;gBACf,QAAQ;YACT;QACD;IACD;IACE,6BAA6B;IAC/B,aAAY,SAAS,OAAO,EAAE,OAAO;QACpC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,SAAQ;IAC7D;IACG,6BAA6B;IAC7B,eAAc;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAC;IAC/B;IACH;;;;;;;;;;;;;EAaC,GACE,cAAa,SAAS,YAAY;QACjC,IAAI,KAAK,IAAI;QACb,MAAM,GAAG;YACR,IAAI,MAAM,GAAG,MAAM;YACnB,kBAAkB;YAClB,IAAG,KAAI;gBACN,IAAI,IAAI,KAAK,IAAI;oBAClB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,cAAc;wBAC5E,OAAO;oBACR;gBACC;YACD;YACA,KAAK,GAAG,QAAQ,IAAI,iBAAe,GAAG,aAAa,GAAG,GAAG,UAAU;QACpE;QACA,OAAO;IACR;IACA,6BAA6B;IAC7B,oBAAmB,SAAS,MAAM;QACjC,IAAI,KAAK,IAAI;QACb,MAAM,GAAG;YACR,IAAI,MAAM,GAAG,MAAM;YACnB,kBAAkB;YAClB,IAAG,KAAI;gBACN,IAAG,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,SAAQ;oBACpD,OAAO,GAAG,CAAC,OAAO;gBACnB;YACD;YACA,KAAK,GAAG,QAAQ,IAAI,iBAAe,GAAG,aAAa,GAAG,GAAG,UAAU;QACpE;QACA,OAAO;IACR;IACA,6BAA6B;IAC7B,oBAAmB,SAAS,YAAY;QACvC,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC;QAC/B,OAAO,UAAU;IAClB;AACJ;AAGA,SAAS,YAAY,CAAC;IACrB,OAAO,KAAK,OAAO,UACX,KAAK,OAAO,UACZ,KAAK,OAAO,WACZ,KAAK,OAAO,YACZ,OAAK,EAAE,UAAU,KAAG;AAC7B;AAGA,KAAK,UAAS;AACd,KAAK,UAAS,KAAK,SAAS;AAE5B;;;CAGC,GACD,SAAS,WAAW,IAAI,EAAC,QAAQ;IAChC,IAAG,SAAS,OAAM;QACjB,OAAO;IACR;IACA,IAAG,OAAO,KAAK,UAAU,EAAC;QACzB,GAAE;YACD,IAAG,WAAW,MAAK,WAAU;gBAAC,OAAO;YAAI;QACpC,QAAO,OAAK,KAAK,WAAW,CAAC;IACjC;AACJ;AAIA,SAAS;IACR,IAAI,CAAC,aAAa,GAAG,IAAI;AAC1B;AAEA,SAAS,gBAAgB,GAAG,EAAC,EAAE,EAAC,OAAO;IACtC,OAAO,IAAI,IAAI;IACf,IAAI,KAAK,QAAQ,YAAY;IAC7B,IAAG,OAAO,UAAU,KAAK,EAAC;QACzB,kBAAkB;QAClB,GAAG,MAAM,CAAC,QAAQ,MAAM,GAAC,QAAQ,SAAS,GAAC,GAAG,GAAG,QAAQ,KAAK;IAC/D;AACD;AAEA,SAAS,mBAAmB,GAAG,EAAC,EAAE,EAAC,OAAO,EAAC,MAAM;IAChD,OAAO,IAAI,IAAI;IACf,IAAI,KAAK,QAAQ,YAAY;IAC7B,IAAG,OAAO,UAAU,KAAK,EAAC;QACzB,kBAAkB;QAClB,OAAO,GAAG,MAAM,CAAC,QAAQ,MAAM,GAAC,QAAQ,SAAS,GAAC,GAAG;IACtD;AACD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,eAAgB,GAAG,EAAE,EAAE,EAAE,QAAQ;IACzC,IAAG,OAAO,IAAI,IAAI,EAAC;QAClB,IAAI,IAAI;QACR,mBAAmB;QACnB,IAAI,KAAK,GAAG,UAAU;QACtB,IAAI,UAAU;YACb,EAAE,CAAC,GAAG,MAAM,GAAG,GAAG;QACnB,OAAO;YACN,IAAI,QAAQ,GAAG,UAAU;YACzB,IAAI,IAAI;YACR,MAAO,MAAO;gBACb,EAAE,CAAC,IAAI,GAAG;gBACV,QAAQ,MAAM,WAAW;YAC1B;YACA,GAAG,MAAM,GAAG;YACZ,OAAO,EAAE,CAAC,GAAG,MAAM,CAAC;QACrB;IACD;AACD;AAEA;;;;;;;;;;;CAWC,GACD,SAAS,aAAc,UAAU,EAAE,KAAK;IACvC,IAAI,WAAW,MAAM,eAAe;IACpC,IAAI,OAAO,MAAM,WAAW;IAC5B,IAAI,UAAU;QACb,SAAS,WAAW,GAAG;IACxB,OAAO;QACN,WAAW,UAAU,GAAG;IACzB;IACA,IAAI,MAAM;QACT,KAAK,eAAe,GAAG;IACxB,OAAO;QACN,WAAW,SAAS,GAAG;IACxB;IACA,MAAM,UAAU,GAAG;IACnB,MAAM,eAAe,GAAG;IACxB,MAAM,WAAW,GAAG;IACpB,eAAe,WAAW,aAAa,EAAE;IACzC,OAAO;AACR;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,IAAI;IACnC,OACC,QACA,CAAC,KAAK,QAAQ,KAAK,KAAK,aAAa,IAAI,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY;AAE/H;AAEA;;;;CAIC,GACD,SAAS,sBAAsB,IAAI;IAClC,OACC,QACA,CAAC,cAAc,SACd,WAAW,SACX,cAAc,SACd,KAAK,QAAQ,KAAK,KAAK,sBAAsB,IAC7C,KAAK,QAAQ,KAAK,KAAK,YAAY,IACnC,KAAK,QAAQ,KAAK,KAAK,2BAA2B;AAErD;AAEA;;;;CAIC,GACD,SAAS,cAAc,IAAI;IAC1B,OAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK,kBAAkB;AACzD;AAEA;;;;CAIC,GACD,SAAS,cAAc,IAAI;IAC1B,OAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY;AACnD;AACA;;;;CAIC,GACD,SAAS,WAAW,IAAI;IACvB,OAAO,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS;AAChD;AAEA;;;;;;;;;CASC,GACD,SAAS,2BAA2B,GAAG,EAAE,KAAK;IAC7C,IAAI,mBAAmB,IAAI,UAAU,IAAI,EAAE;IAC3C,IAAI,KAAK,kBAAkB,kBAAkB,cAAc,QAAQ;QAClE,OAAO;IACR;IACA,IAAI,cAAc,KAAK,kBAAkB;IACzC,OAAO,CAAC,CAAC,SAAS,eAAe,iBAAiB,OAAO,CAAC,eAAe,iBAAiB,OAAO,CAAC,MAAM;AACzG;AAEA;;;;;;;;;CASC,GACD,SAAS,6BAA6B,GAAG,EAAE,KAAK;IAC/C,IAAI,mBAAmB,IAAI,UAAU,IAAI,EAAE;IAE3C,SAAS,8BAA8B,IAAI;QAC1C,OAAO,cAAc,SAAS,SAAS;IACxC;IAEA,IAAI,KAAK,kBAAkB,gCAAgC;QAC1D,OAAO;IACR;IACA,IAAI,cAAc,KAAK,kBAAkB;IACzC,OAAO,CAAC,CAAC,SAAS,eAAe,iBAAiB,OAAO,CAAC,eAAe,iBAAiB,OAAO,CAAC,MAAM;AACzG;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,+BAA+B,MAAM,EAAE,IAAI,EAAE,KAAK;IAC1D,0HAA0H;IAC1H,IAAI,CAAC,uBAAuB,SAAS;QACpC,MAAM,IAAI,aAAa,uBAAuB,iCAAiC,OAAO,QAAQ;IAC/F;IACA,sHAAsH;IACtH,mBAAmB;IACnB,uGAAuG;IACvG,IAAI,SAAS,MAAM,UAAU,KAAK,QAAQ;QACzC,MAAM,IAAI,aAAa,eAAe;IACvC;IACA,IACC,2IAA2I;IAC3I,CAAC,sBAAsB,SAKtB,cAAc,SAAS,OAAO,QAAQ,KAAK,KAAK,aAAa,EAC7D;QACD,MAAM,IAAI,aACT,uBACA,0BAA0B,KAAK,QAAQ,GAAG,2BAA2B,OAAO,QAAQ;IAEtF;AACD;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,qCAAqC,MAAM,EAAE,IAAI,EAAE,KAAK;IAChE,IAAI,mBAAmB,OAAO,UAAU,IAAI,EAAE;IAC9C,IAAI,iBAAiB,KAAK,UAAU,IAAI,EAAE;IAE1C,mBAAmB;IACnB,IAAI,KAAK,QAAQ,KAAK,KAAK,sBAAsB,EAAE;QAClD,IAAI,oBAAoB,eAAe,MAAM,CAAC;QAC9C,oEAAoE;QACpE,IAAI,kBAAkB,MAAM,GAAG,KAAK,KAAK,gBAAgB,aAAa;YACrE,MAAM,IAAI,aAAa,uBAAuB;QAC/C;QACA,uFAAuF;QACvF,mFAAmF;QACnF,IAAI,kBAAkB,MAAM,KAAK,KAAK,CAAC,2BAA2B,QAAQ,QAAQ;YACjF,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;IACA,UAAU;IACV,IAAI,cAAc,OAAO;QACxB,uDAAuD;QACvD,6DAA6D;QAC7D,IAAI,CAAC,2BAA2B,QAAQ,QAAQ;YAC/C,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;IACA,eAAe;IACf,IAAI,cAAc,OAAO;QACxB,gCAAgC;QAChC,IAAI,KAAK,kBAAkB,gBAAgB;YAC1C,MAAM,IAAI,aAAa,uBAAuB;QAC/C;QACA,IAAI,qBAAqB,KAAK,kBAAkB;QAChD,2DAA2D;QAC3D,IAAI,SAAS,iBAAiB,OAAO,CAAC,sBAAsB,iBAAiB,OAAO,CAAC,QAAQ;YAC5F,MAAM,IAAI,aAAa,uBAAuB;QAC/C;QACA,wDAAwD;QACxD,IAAI,CAAC,SAAS,oBAAoB;YACjC,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;AACD;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,uCAAuC,MAAM,EAAE,IAAI,EAAE,KAAK;IAClE,IAAI,mBAAmB,OAAO,UAAU,IAAI,EAAE;IAC9C,IAAI,iBAAiB,KAAK,UAAU,IAAI,EAAE;IAE1C,mBAAmB;IACnB,IAAI,KAAK,QAAQ,KAAK,KAAK,sBAAsB,EAAE;QAClD,IAAI,oBAAoB,eAAe,MAAM,CAAC;QAC9C,sEAAsE;QACtE,IAAI,kBAAkB,MAAM,GAAG,KAAK,KAAK,gBAAgB,aAAa;YACrE,MAAM,IAAI,aAAa,uBAAuB;QAC/C;QACA,6IAA6I;QAC7I,IAAI,kBAAkB,MAAM,KAAK,KAAK,CAAC,6BAA6B,QAAQ,QAAQ;YACnF,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;IACA,UAAU;IACV,IAAI,cAAc,OAAO;QACxB,uFAAuF;QACvF,IAAI,CAAC,6BAA6B,QAAQ,QAAQ;YACjD,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;IACA,eAAe;IACf,IAAI,cAAc,OAAO;QACxB,SAAS,8BAA8B,IAAI;YAC1C,OAAO,cAAc,SAAS,SAAS;QACxC;QAEA,oDAAoD;QACpD,IAAI,KAAK,kBAAkB,gCAAgC;YAC1D,MAAM,IAAI,aAAa,uBAAuB;QAC/C;QACA,IAAI,qBAAqB,KAAK,kBAAkB;QAChD,sCAAsC;QACtC,IAAI,SAAS,iBAAiB,OAAO,CAAC,sBAAsB,iBAAiB,OAAO,CAAC,QAAQ;YAC5F,MAAM,IAAI,aAAa,uBAAuB;QAC/C;IACD;AACD;AAEA;;;;;;;;;CASC,GACD,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,oBAAoB;IAC/D,4FAA4F;IAC5F,+BAA+B,QAAQ,MAAM;IAE7C,uGAAuG;IACvG,+DAA+D;IAC/D,IAAI,OAAO,QAAQ,KAAK,KAAK,aAAa,EAAE;QAC3C,CAAC,wBAAwB,oCAAoC,EAAE,QAAQ,MAAM;IAC9E;IAEA,IAAI,KAAK,KAAK,UAAU;IACxB,IAAG,IAAG;QACL,GAAG,WAAW,CAAC,OAAM,mBAAmB;IACzC;IACA,IAAG,KAAK,QAAQ,KAAK,wBAAuB;QAC3C,IAAI,WAAW,KAAK,UAAU;QAC9B,IAAI,YAAY,MAAM;YACrB,OAAO;QACR;QACA,IAAI,UAAU,KAAK,SAAS;IAC7B,OAAK;QACJ,WAAW,UAAU;IACtB;IACA,IAAI,MAAM,QAAQ,MAAM,eAAe,GAAG,OAAO,SAAS;IAE1D,SAAS,eAAe,GAAG;IAC3B,QAAQ,WAAW,GAAG;IAGtB,IAAG,KAAI;QACN,IAAI,WAAW,GAAG;IACnB,OAAK;QACJ,OAAO,UAAU,GAAG;IACrB;IACA,IAAG,SAAS,MAAK;QAChB,OAAO,SAAS,GAAG;IACpB,OAAK;QACJ,MAAM,eAAe,GAAG;IACzB;IACA,GAAE;QACD,SAAS,UAAU,GAAG;IACvB,QAAO,aAAa,WAAW,CAAC,WAAU,SAAS,WAAW,EAAE;IAChE,eAAe,OAAO,aAAa,IAAE,QAAQ;IAC7C,mDAAmD;IACnD,IAAI,KAAK,QAAQ,IAAI,wBAAwB;QAC5C,KAAK,UAAU,GAAG,KAAK,SAAS,GAAG;IACpC;IACA,OAAO;AACR;AAEA;;;;;;;;;;CAUC,GACD,SAAS,mBAAoB,UAAU,EAAE,QAAQ;IAChD,IAAI,SAAS,UAAU,EAAE;QACxB,SAAS,UAAU,CAAC,WAAW,CAAC;IACjC;IACA,SAAS,UAAU,GAAG;IACtB,SAAS,eAAe,GAAG,WAAW,SAAS;IAC/C,SAAS,WAAW,GAAG;IACvB,IAAI,SAAS,eAAe,EAAE;QAC7B,SAAS,eAAe,CAAC,WAAW,GAAG;IACxC,OAAO;QACN,WAAW,UAAU,GAAG;IACzB;IACA,WAAW,SAAS,GAAG;IACvB,eAAe,WAAW,aAAa,EAAE,YAAY;IACrD,OAAO;AACR;AAEA,SAAS,SAAS,GAAG;IACpB,wBAAwB;IACxB,UAAY;IACZ,UAAY;IACZ;;;;;EAKC,GACD,SAAW;IACX,iBAAmB;IACnB,MAAO;IAEP,cAAgB,SAAS,QAAQ,EAAE,QAAQ;QAC1C,IAAG,SAAS,QAAQ,IAAI,wBAAuB;YAC9C,IAAI,QAAQ,SAAS,UAAU;YAC/B,MAAM,MAAM;gBACX,IAAI,OAAO,MAAM,WAAW;gBAC5B,IAAI,CAAC,YAAY,CAAC,OAAM;gBACxB,QAAQ;YACT;YACA,OAAO;QACR;QACA,cAAc,IAAI,EAAE,UAAU;QAC9B,SAAS,aAAa,GAAG,IAAI;QAC7B,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,SAAS,QAAQ,KAAK,cAAc;YACxE,IAAI,CAAC,eAAe,GAAG;QACxB;QAEA,OAAO;IACR;IACA,aAAe,SAAS,QAAQ;QAC/B,IAAG,IAAI,CAAC,eAAe,IAAI,UAAS;YACnC,IAAI,CAAC,eAAe,GAAG;QACxB;QACA,OAAO,aAAa,IAAI,EAAC;IAC1B;IACA,cAAc,SAAU,QAAQ,EAAE,QAAQ;QACzC,QAAQ;QACR,cAAc,IAAI,EAAE,UAAU,UAAU;QACxC,SAAS,aAAa,GAAG,IAAI;QAC7B,IAAI,UAAU;YACb,IAAI,CAAC,WAAW,CAAC;QAClB;QACA,IAAI,cAAc,WAAW;YAC5B,IAAI,CAAC,eAAe,GAAG;QACxB;IACD;IACA,6BAA6B;IAC7B,YAAa,SAAS,YAAY,EAAC,IAAI;QACtC,OAAO,WAAW,IAAI,EAAC,cAAa;IACrC;IACA,6BAA6B;IAC7B,gBAAiB,SAAS,EAAE;QAC3B,IAAI,MAAM;QACV,WAAW,IAAI,CAAC,eAAe,EAAC,SAAS,IAAI;YAC5C,IAAG,KAAK,QAAQ,IAAI,cAAa;gBAChC,IAAG,KAAK,YAAY,CAAC,SAAS,IAAG;oBAChC,MAAM;oBACN,OAAO;gBACR;YACD;QACD;QACA,OAAO;IACR;IAEA;;;;;;;;;;;;;;;;EAgBC,GACD,wBAAwB,SAAS,UAAU;QAC1C,IAAI,gBAAgB,aAAa;QACjC,OAAO,IAAI,aAAa,IAAI,EAAE,SAAS,IAAI;YAC1C,IAAI,KAAK,EAAE;YACX,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC7B,WAAW,KAAK,eAAe,EAAE,SAAS,IAAI;oBAC7C,IAAG,SAAS,QAAQ,KAAK,QAAQ,KAAK,cAAc;wBACnD,IAAI,iBAAiB,KAAK,YAAY,CAAC;wBACvC,8CAA8C;wBAC9C,IAAI,gBAAgB;4BACnB,4EAA4E;4BAC5E,IAAI,UAAU,eAAe;4BAC7B,IAAI,CAAC,SAAS;gCACb,IAAI,oBAAoB,aAAa;gCACrC,UAAU,cAAc,KAAK,CAAC,cAAc;4BAC7C;4BACA,IAAG,SAAS;gCACX,GAAG,IAAI,CAAC;4BACT;wBACD;oBACD;gBACD;YACD;YACA,OAAO;QACR;IACD;IAEA,0BAA0B;IAC1B,eAAgB,SAAS,OAAO;QAC/B,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,QAAQ,GAAG;QAChB,KAAK,OAAO,GAAG;QACf,KAAK,SAAS,GAAG;QACjB,KAAK,UAAU,GAAG,IAAI;QACtB,IAAI,QAAQ,KAAK,UAAU,GAAG,IAAI;QAClC,MAAM,aAAa,GAAG;QACtB,OAAO;IACR;IACA,wBAAyB;QACxB,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,UAAU,GAAG,IAAI;QACtB,OAAO;IACR;IACA,gBAAiB,SAAS,IAAI;QAC7B,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,UAAU,CAAC;QAChB,OAAO;IACR;IACA,eAAgB,SAAS,IAAI;QAC5B,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,UAAU,CAAC;QAChB,OAAO;IACR;IACA,oBAAqB,SAAS,IAAI;QACjC,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,UAAU,CAAC;QAChB,OAAO;IACR;IACA,6BAA8B,SAAS,MAAM,EAAC,IAAI;QACjD,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM,GAAG;QAC7C,KAAK,SAAS,GAAG,KAAK,IAAI,GAAG;QAC7B,OAAO;IACR;IACA,iBAAkB,SAAS,IAAI;QAC9B,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,KAAK,SAAS,GAAG;QACjB,KAAK,SAAS,GAAG;QACjB,OAAO;IACR;IACA,uBAAwB,SAAS,IAAI;QACpC,IAAI,OAAO,IAAI;QACf,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,QAAQ,GAAG;QAChB,OAAO;IACR;IACA,6BAA6B;IAC7B,iBAAkB,SAAS,YAAY,EAAC,aAAa;QACpD,IAAI,OAAO,IAAI;QACf,IAAI,KAAK,cAAc,KAAK,CAAC;QAC7B,IAAI,QAAQ,KAAK,UAAU,GAAG,IAAI;QAClC,KAAK,UAAU,GAAG,IAAI;QACtB,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,QAAQ,GAAG;QAChB,KAAK,OAAO,GAAG;QACf,KAAK,YAAY,GAAG;QACpB,IAAG,GAAG,MAAM,IAAI,GAAE;YACjB,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE;YACnB,KAAK,SAAS,GAAG,EAAE,CAAC,EAAE;QACvB,OAAK;YACJ,mBAAmB;YACnB,KAAK,SAAS,GAAG;QAClB;QACA,MAAM,aAAa,GAAG;QACtB,OAAO;IACR;IACA,6BAA6B;IAC7B,mBAAoB,SAAS,YAAY,EAAC,aAAa;QACtD,IAAI,OAAO,IAAI;QACf,IAAI,KAAK,cAAc,KAAK,CAAC;QAC7B,KAAK,aAAa,GAAG,IAAI;QACzB,KAAK,QAAQ,GAAG;QAChB,KAAK,IAAI,GAAG;QACZ,KAAK,YAAY,GAAG;QACpB,KAAK,SAAS,GAAG;QACjB,IAAG,GAAG,MAAM,IAAI,GAAE;YACjB,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE;YACnB,KAAK,SAAS,GAAG,EAAE,CAAC,EAAE;QACvB,OAAK;YACJ,mBAAmB;YACnB,KAAK,SAAS,GAAG;QAClB;QACA,OAAO;IACR;AACD;AACA,SAAS,UAAS;AAGlB,SAAS;IACR,IAAI,CAAC,MAAM,GAAG,CAAC;AAChB;;AACA,QAAQ,SAAS,GAAG;IACnB,UAAW;IACX,cAAe,SAAS,IAAI;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAO;IACrC;IACA,cAAe,SAAS,IAAI;QAC3B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,OAAO,QAAQ,KAAK,KAAK,IAAI;IAC9B;IACA,kBAAmB,SAAS,IAAI;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IACrC;IACA,cAAe,SAAS,IAAI,EAAE,KAAK;QAClC,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;QAC9C,KAAK,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,gBAAgB,CAAC;IACvB;IACA,iBAAkB,SAAS,IAAI;QAC9B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,QAAQ,IAAI,CAAC,mBAAmB,CAAC;IAClC;IAEA,4BAA4B;IAC5B,aAAY,SAAS,QAAQ;QAC5B,IAAG,SAAS,QAAQ,KAAK,wBAAuB;YAC/C,OAAO,IAAI,CAAC,YAAY,CAAC,UAAS;QACnC,OAAK;YACJ,OAAO,mBAAmB,IAAI,EAAC;QAChC;IACD;IACA,kBAAmB,SAAS,OAAO;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IACrC;IACA,oBAAqB,SAAS,OAAO;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IACvC;IACA,qBAAsB,SAAS,OAAO;QACrC,2CAA2C;QAC3C,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,QAAQ;IACxD;IACA,8DAA8D;IAC9D,mBAAoB,SAAS,YAAY,EAAE,SAAS;QACnD,IAAI,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc;QAChD,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACjC;IAEA,gBAAiB,SAAS,YAAY,EAAE,SAAS;QAChD,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,cAAY;IAC1D;IACA,gBAAiB,SAAS,YAAY,EAAE,SAAS;QAChD,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;QACjD,OAAO,QAAQ,KAAK,KAAK,IAAI;IAC9B;IACA,gBAAiB,SAAS,YAAY,EAAE,aAAa,EAAE,KAAK;QAC3D,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc;QAC9D,KAAK,KAAK,GAAG,KAAK,SAAS,GAAG,KAAK;QACnC,IAAI,CAAC,gBAAgB,CAAC;IACvB;IACA,oBAAqB,SAAS,YAAY,EAAE,SAAS;QACpD,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc;IACrD;IAEA,sBAAuB,SAAS,OAAO;QACtC,OAAO,IAAI,aAAa,IAAI,EAAC,SAAS,IAAI;YACzC,IAAI,KAAK,EAAE;YACX,WAAW,MAAK,SAAS,IAAI;gBAC5B,IAAG,SAAS,QAAQ,KAAK,QAAQ,IAAI,gBAAgB,CAAC,YAAY,OAAO,KAAK,OAAO,IAAI,OAAO,GAAE;oBACjG,GAAG,IAAI,CAAC;gBACT;YACD;YACA,OAAO;QACR;IACD;IACA,wBAAyB,SAAS,YAAY,EAAE,SAAS;QACxD,OAAO,IAAI,aAAa,IAAI,EAAC,SAAS,IAAI;YACzC,IAAI,KAAK,EAAE;YACX,WAAW,MAAK,SAAS,IAAI;gBAC5B,IAAG,SAAS,QAAQ,KAAK,QAAQ,KAAK,gBAAgB,CAAC,iBAAiB,OAAO,KAAK,YAAY,KAAK,YAAY,KAAK,CAAC,cAAc,OAAO,KAAK,SAAS,IAAI,SAAS,GAAE;oBACxK,GAAG,IAAI,CAAC;gBACT;YACD;YACA,OAAO;QAER;IACD;AACD;AACA,SAAS,SAAS,CAAC,oBAAoB,GAAG,QAAQ,SAAS,CAAC,oBAAoB;AAChF,SAAS,SAAS,CAAC,sBAAsB,GAAG,QAAQ,SAAS,CAAC,sBAAsB;AAGpF,SAAS,SAAQ;AACjB,SAAS,QACT;;AACA,KAAK,SAAS,CAAC,QAAQ,GAAG;AAC1B,SAAS,MAAK;AAGd,SAAS,iBACT;;AACA,cAAc,SAAS,GAAG;IACzB,MAAO;IACP,eAAgB,SAAS,MAAM,EAAE,KAAK;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,SAAO;IAC3C;IACA,YAAY,SAAS,IAAI;QACxB,OAAO,IAAI,CAAC,IAAI,GAAC;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG;QAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC1B;IACA,YAAY,SAAS,MAAM,EAAC,IAAI;QAC/B,IAAI,CAAC,WAAW,CAAC,QAAO,GAAE;IAE3B;IACA,aAAY,SAAS,QAAQ;QAC5B,MAAM,IAAI,MAAM,gBAAgB,CAAC,sBAAsB;IACxD;IACA,YAAY,SAAS,MAAM,EAAE,KAAK;QACjC,IAAI,CAAC,WAAW,CAAC,QAAO,OAAM;IAC/B;IACA,aAAa,SAAS,MAAM,EAAE,KAAK,EAAE,IAAI;QACxC,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAE;QAClC,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAO;QACrC,OAAO,QAAQ,OAAO;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG;QAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;IAC1B;AACD;AACA,SAAS,eAAc;AACvB,SAAS,QACT;;AACA,KAAK,SAAS,GAAG;IAChB,UAAW;IACX,UAAW;IACX,WAAY,SAAS,MAAM;QAC1B,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,UAAU,KAAK,SAAS,CAAC;QAC7B,OAAO,KAAK,SAAS,CAAC,GAAG;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG;QAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QAChD,IAAG,IAAI,CAAC,UAAU,EAAC;YAClB,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,WAAW;QACvD;QACA,OAAO;IACR;AACD;AACA,SAAS,MAAK;AACd,SAAS,WACT;;AACA,QAAQ,SAAS,GAAG;IACnB,UAAW;IACX,UAAW;AACZ;AACA,SAAS,SAAQ;AAEjB,SAAS,gBACT;;AACA,aAAa,SAAS,GAAG;IACxB,UAAW;IACX,UAAW;AACZ;AACA,SAAS,cAAa;AAGtB,SAAS,gBACT;;AACA,aAAa,SAAS,CAAC,QAAQ,GAAG;AAClC,SAAS,cAAa;AAEtB,SAAS,YACT;;AACA,SAAS,SAAS,CAAC,QAAQ,GAAG;AAC9B,SAAS,UAAS;AAElB,SAAS,UACT;;AACA,OAAO,SAAS,CAAC,QAAQ,GAAG;AAC5B,SAAS,QAAO;AAEhB,SAAS,mBACT;;AACA,gBAAgB,SAAS,CAAC,QAAQ,GAAG;AACrC,SAAS,iBAAgB;AAEzB,SAAS,oBACT;;AACA,iBAAiB,SAAS,CAAC,QAAQ,GAAG;AACtC,iBAAiB,SAAS,CAAC,QAAQ,GAAG;AACtC,SAAS,kBAAiB;AAG1B,SAAS,yBACT;AACA,sBAAsB,SAAS,CAAC,QAAQ,GAAG;AAC3C,SAAS,uBAAsB;AAC/B,SAAS,iBAAgB;AACzB,cAAc,SAAS,CAAC,iBAAiB,GAAG,SAAS,IAAI,EAAC,MAAM,EAAC,UAAU;IAC1E,OAAO,sBAAsB,IAAI,CAAC,MAAK,QAAO;AAC/C;AACA,KAAK,SAAS,CAAC,QAAQ,GAAG;AAC1B,SAAS,sBAAsB,MAAM,EAAC,UAAU;IAC/C,IAAI,MAAM,EAAE;IACZ,IAAI,UAAU,IAAI,CAAC,QAAQ,IAAI,KAAK,IAAI,CAAC,eAAe,IAAI,IAAI;IAChE,IAAI,SAAS,QAAQ,MAAM;IAC3B,IAAI,MAAM,QAAQ,YAAY;IAE9B,IAAG,OAAO,UAAU,MAAK;QACxB,qBAAqB;QACrB,IAAI,SAAS,QAAQ,YAAY,CAAC;QAClC,IAAG,UAAU,MAAK;YACjB,gBAAgB;YAChB,IAAI,oBAAkB;gBACtB;oBAAC,WAAU;oBAAI,QAAO;gBAAI;aAEzB;QACF;IACD;IACA,kBAAkB,IAAI,EAAC,KAAI,QAAO,YAAW;IAC7C,0DAA0D;IAC1D,OAAO,IAAI,IAAI,CAAC;AACjB;AAEA,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAAE,iBAAiB;IAC3D,IAAI,SAAS,KAAK,MAAM,IAAI;IAC5B,IAAI,MAAM,KAAK,YAAY;IAC3B,wFAAwF;IACxF,kFAAkF;IAClF,0FAA0F;IAC1F,2FAA2F;IAC3F,4EAA4E;IAC5E,8EAA8E;IAC9E,+FAA+F;IAC/F,IAAI,CAAC,KAAK;QACT,OAAO;IACR;IACA,IAAI,WAAW,SAAS,QAAQ,UAAU,GAAG,IAAI,QAAQ,UAAU,KAAK,EAAE;QACzE,OAAO;IACR;IAEA,IAAI,IAAI,kBAAkB,MAAM;IAChC,MAAO,IAAK;QACX,IAAI,KAAK,iBAAiB,CAAC,EAAE;QAC7B,uBAAuB;QACvB,IAAI,GAAG,MAAM,KAAK,QAAQ;YACzB,OAAO,GAAG,SAAS,KAAK;QACzB;IACD;IACA,OAAO;AACR;AACA;;;;;;;;;;;;CAYC,GACD,SAAS,uBAAuB,GAAG,EAAE,aAAa,EAAE,KAAK;IACxD,IAAI,IAAI,CAAC,KAAK,eAAe,MAAM,MAAM,OAAO,CAAC,iBAAiB,cAAc;AACjF;AAEA,SAAS,kBAAkB,IAAI,EAAC,GAAG,EAAC,MAAM,EAAC,UAAU,EAAC,iBAAiB;IACtE,IAAI,CAAC,mBAAmB;QACvB,oBAAoB,EAAE;IACvB;IAEA,IAAG,YAAW;QACb,OAAO,WAAW;QAClB,IAAG,MAAK;YACP,IAAG,OAAO,QAAQ,UAAS;gBAC1B,IAAI,IAAI,CAAC;gBACT;YACD;QACD,OAAK;YACJ;QACD;IACA,yCAAyC;IAC1C;IAEA,OAAO,KAAK,QAAQ;QACpB,KAAK;YACJ,IAAI,QAAQ,KAAK,UAAU;YAC3B,IAAI,MAAM,MAAM,MAAM;YACtB,IAAI,QAAQ,KAAK,UAAU;YAC3B,IAAI,WAAW,KAAK,OAAO;YAE3B,SAAS,UAAU,MAAM,CAAC,KAAK,YAAY,KAAK;YAEhD,IAAI,mBAAmB;YACvB,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,IAAI,KAAK,YAAY,EAAE;gBACjD,IAAI;gBACJ,mDAAmD;gBACnD,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,EAAE,KAAM;oBACzC,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,KAAK,SAAS;wBACpC,YAAY,MAAM,IAAI,CAAC,IAAI,KAAK;wBAChC;oBACD;gBACD;gBACA,IAAI,CAAC,WAAW;oBACf,iDAAiD;oBACjD,IAAK,IAAI,MAAM,kBAAkB,MAAM,GAAG,GAAG,OAAO,GAAG,MAAO;wBAC7D,IAAI,YAAY,iBAAiB,CAAC,IAAI;wBACtC,IAAI,UAAU,MAAM,KAAK,MAAM,UAAU,SAAS,KAAK,KAAK,YAAY,EAAE;4BACzE,YAAY,UAAU,SAAS;4BAC/B;wBACD;oBACD;gBACD;gBACA,IAAI,cAAc,KAAK,YAAY,EAAE;oBACpC,IAAK,IAAI,MAAM,kBAAkB,MAAM,GAAG,GAAG,OAAO,GAAG,MAAO;wBAC7D,IAAI,YAAY,iBAAiB,CAAC,IAAI;wBACtC,IAAI,UAAU,SAAS,KAAK,KAAK,YAAY,EAAE;4BAC9C,IAAI,UAAU,MAAM,EAAE;gCACrB,mBAAmB,UAAU,MAAM,GAAG,MAAM;4BAC7C;4BACA;wBACD;oBACD;gBACD;YACD;YAEA,IAAI,IAAI,CAAC,KAAK;YAEd,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,IAAI;gBACrB,gCAAgC;gBAChC,IAAI,OAAO,MAAM,IAAI,CAAC;gBACtB,IAAI,KAAK,MAAM,IAAI,SAAS;oBAC3B,kBAAkB,IAAI,CAAC;wBAAE,QAAQ,KAAK,SAAS;wBAAE,WAAW,KAAK,KAAK;oBAAC;gBACxE,OAAM,IAAG,KAAK,QAAQ,IAAI,SAAQ;oBACjC,kBAAkB,IAAI,CAAC;wBAAE,QAAQ;wBAAI,WAAW,KAAK,KAAK;oBAAC;gBAC5D;YACD;YAEA,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,IAAI;gBACrB,IAAI,OAAO,MAAM,IAAI,CAAC;gBACtB,IAAI,oBAAoB,MAAK,QAAQ,oBAAoB;oBACxD,IAAI,SAAS,KAAK,MAAM,IAAE;oBAC1B,IAAI,MAAM,KAAK,YAAY;oBAC3B,uBAAuB,KAAK,SAAS,WAAW,SAAS,SAAS;oBAClE,kBAAkB,IAAI,CAAC;wBAAE,QAAQ;wBAAQ,WAAU;oBAAI;gBACxD;gBACA,kBAAkB,MAAK,KAAI,QAAO,YAAW;YAC9C;YAEA,iCAAiC;YACjC,IAAI,aAAa,oBAAoB,oBAAoB,MAAM,QAAQ,oBAAoB;gBAC1F,IAAI,SAAS,KAAK,MAAM,IAAE;gBAC1B,IAAI,MAAM,KAAK,YAAY;gBAC3B,uBAAuB,KAAK,SAAS,WAAW,SAAS,SAAS;gBAClE,kBAAkB,IAAI,CAAC;oBAAE,QAAQ;oBAAQ,WAAU;gBAAI;YACxD;YAEA,IAAG,SAAS,UAAU,CAAC,mCAAmC,IAAI,CAAC,WAAU;gBACxE,IAAI,IAAI,CAAC;gBACT,wBAAwB;gBACxB,IAAG,UAAU,YAAY,IAAI,CAAC,WAAU;oBACvC,MAAM,MAAM;wBACX,IAAG,MAAM,IAAI,EAAC;4BACb,IAAI,IAAI,CAAC,MAAM,IAAI;wBACpB,OAAK;4BACJ,kBAAkB,OAAO,KAAK,QAAQ,YAAY,kBAAkB,KAAK;wBAC1E;wBACA,QAAQ,MAAM,WAAW;oBAC1B;gBACD,OACA;oBACC,MAAM,MAAM;wBACX,kBAAkB,OAAO,KAAK,QAAQ,YAAY,kBAAkB,KAAK;wBACzE,QAAQ,MAAM,WAAW;oBAC1B;gBACD;gBACA,IAAI,IAAI,CAAC,MAAK,kBAAiB;YAChC,OAAK;gBACJ,IAAI,IAAI,CAAC;YACV;YACA,kCAAkC;YAClC,oDAAoD;YACpD;QACD,KAAK;QACL,KAAK;YACJ,IAAI,QAAQ,KAAK,UAAU;YAC3B,MAAM,MAAM;gBACX,kBAAkB,OAAO,KAAK,QAAQ,YAAY,kBAAkB,KAAK;gBACzE,QAAQ,MAAM,WAAW;YAC1B;YACA;QACD,KAAK;YACJ,OAAO,uBAAuB,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK;QACzD,KAAK;YACJ;;;;;;;;;;;;;;;GAeC,GACD,OAAO,IAAI,IAAI,CAAC,KAAK,IAAI,CACvB,OAAO,CAAC,UAAS;QAEpB,KAAK;YACJ,OAAO,IAAI,IAAI,CAAE,aAAY,KAAK,IAAI,EAAC;QACxC,KAAK;YACJ,OAAO,IAAI,IAAI,CAAE,QAAO,KAAK,IAAI,EAAC;QACnC,KAAK;YACJ,IAAI,QAAQ,KAAK,QAAQ;YACzB,IAAI,QAAQ,KAAK,QAAQ;YACzB,IAAI,IAAI,CAAC,cAAa,KAAK,IAAI;YAC/B,IAAG,OAAM;gBACR,IAAI,IAAI,CAAC,YAAY;gBACrB,IAAI,SAAS,SAAO,KAAK;oBACxB,IAAI,IAAI,CAAC,KAAK;gBACf;gBACA,IAAI,IAAI,CAAC;YACV,OAAM,IAAG,SAAS,SAAO,KAAI;gBAC5B,IAAI,IAAI,CAAC,YAAY,OAAO;YAC7B,OAAK;gBACJ,IAAI,MAAM,KAAK,cAAc;gBAC7B,IAAG,KAAI;oBACN,IAAI,IAAI,CAAC,MAAK,KAAI;gBACnB;gBACA,IAAI,IAAI,CAAC;YACV;YACA;QACD,KAAK;YACJ,OAAO,IAAI,IAAI,CAAE,MAAK,KAAK,MAAM,EAAC,KAAI,KAAK,IAAI,EAAC;QACjD,KAAK;YACJ,OAAO,IAAI,IAAI,CAAE,KAAI,KAAK,QAAQ,EAAC;QACpC,mBAAmB;QACnB,qBAAqB;QACrB;YACC,IAAI,IAAI,CAAC,MAAK,KAAK,QAAQ;IAC5B;AACD;AACA,SAAS,WAAW,GAAG,EAAC,IAAI,EAAC,IAAI;IAChC,IAAI;IACJ,OAAQ,KAAK,QAAQ;QACrB,KAAK;YACJ,QAAQ,KAAK,SAAS,CAAC;YACvB,MAAM,aAAa,GAAG;QACtB,+BAA+B;QAC/B,yBAAyB;QACzB,yBAAyB;QACxB,+DAA+D;QAChE,GAAG;QACJ,KAAK;YACJ;QACD,KAAK;YACJ,OAAO;YACP;IAeD;IACA,IAAG,CAAC,OAAM;QACT,QAAQ,KAAK,SAAS,CAAC,QAAO,OAAO;IACtC;IACA,MAAM,aAAa,GAAG;IACtB,MAAM,UAAU,GAAG;IACnB,IAAG,MAAK;QACP,IAAI,QAAQ,KAAK,UAAU;QAC3B,MAAM,MAAM;YACX,MAAM,WAAW,CAAC,WAAW,KAAI,OAAM;YACvC,QAAQ,MAAM,WAAW;QAC1B;IACD;IACA,OAAO;AACR;AACA,EAAE;AACF,+EAA+E;AAC/E,0EAA0E;AAC1E,SAAS,UAAU,GAAG,EAAC,IAAI,EAAC,IAAI;IAC/B,IAAI,QAAQ,IAAI,KAAK,WAAW;IAChC,IAAK,IAAI,KAAK,KAAM;QACnB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,IAAI;YAClD,IAAI,IAAI,IAAI,CAAC,EAAE;YACf,IAAI,OAAO,KAAK,UAAU;gBACzB,IAAI,KAAK,KAAK,CAAC,EAAE,EAAE;oBAClB,KAAK,CAAC,EAAE,GAAG;gBACZ;YACD;QACD;IACD;IACA,IAAG,KAAK,UAAU,EAAC;QAClB,MAAM,UAAU,GAAG,IAAI;IACxB;IACA,MAAM,aAAa,GAAG;IACtB,OAAQ,MAAM,QAAQ;QACtB,KAAK;YACJ,IAAI,QAAQ,KAAK,UAAU;YAC3B,IAAI,SAAS,MAAM,UAAU,GAAG,IAAI;YACpC,IAAI,MAAM,MAAM,MAAM;YACtB,OAAO,aAAa,GAAG;YACvB,IAAI,IAAI,IAAE,GAAE,IAAE,KAAI,IAAI;gBACrB,MAAM,gBAAgB,CAAC,UAAU,KAAI,MAAM,IAAI,CAAC,IAAG;YACpD;YACA;;QACD,KAAK;YACJ,OAAO;IACR;IACA,IAAG,MAAK;QACP,IAAI,QAAQ,KAAK,UAAU;QAC3B,MAAM,MAAM;YACX,MAAM,WAAW,CAAC,UAAU,KAAI,OAAM;YACtC,QAAQ,MAAM,WAAW;QAC1B;IACD;IACA,OAAO;AACR;AAEA,SAAS,QAAQ,MAAM,EAAC,GAAG,EAAC,KAAK;IAChC,MAAM,CAAC,IAAI,GAAG;AACf;AACA,YAAY;AACZ,IAAG;IACF,IAAG,OAAO,cAAc,EAAC;QACxB,OAAO,cAAc,CAAC,aAAa,SAAS,EAAC,UAAS;YACrD,KAAI;gBACH,gBAAgB,IAAI;gBACpB,OAAO,IAAI,CAAC,QAAQ;YACrB;QACD;QAEA,OAAO,cAAc,CAAC,KAAK,SAAS,EAAC,eAAc;YAClD,KAAI;gBACH,OAAO,eAAe,IAAI;YAC3B;YAEA,KAAI,SAAS,IAAI;gBAChB,OAAO,IAAI,CAAC,QAAQ;oBACpB,KAAK;oBACL,KAAK;wBACJ,MAAM,IAAI,CAAC,UAAU,CAAC;4BACrB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU;wBACjC;wBACA,IAAG,QAAQ,OAAO,OAAM;4BACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;wBACpD;wBACA;oBAED;wBACC,IAAI,CAAC,IAAI,GAAG;wBACZ,IAAI,CAAC,KAAK,GAAG;wBACb,IAAI,CAAC,SAAS,GAAG;gBAClB;YACD;QACD;QAEA,SAAS,eAAe,IAAI;YAC3B,OAAO,KAAK,QAAQ;gBACpB,KAAK;gBACL,KAAK;oBACJ,IAAI,MAAM,EAAE;oBACZ,OAAO,KAAK,UAAU;oBACtB,MAAM,KAAK;wBACV,IAAG,KAAK,QAAQ,KAAG,KAAK,KAAK,QAAQ,KAAI,GAAE;4BAC1C,IAAI,IAAI,CAAC,eAAe;wBACzB;wBACA,OAAO,KAAK,WAAW;oBACxB;oBACA,OAAO,IAAI,IAAI,CAAC;gBACjB;oBACC,OAAO,KAAK,SAAS;YACtB;QACD;QAEA,UAAU,SAAS,MAAM,EAAC,GAAG,EAAC,KAAK;YAClC,oBAAoB;YACpB,MAAM,CAAC,OAAK,IAAI,GAAG;QACpB;IACD;AACD,EAAC,OAAM,GAAE,CACT;AAEA,mCAAmC;AAClC,QAAQ,YAAY,GAAG;AACvB,QAAQ,YAAY,GAAG;AACvB,QAAQ,iBAAiB,GAAG;AAC5B,QAAQ,OAAO,GAAG;AAClB,QAAQ,IAAI,GAAG;AACf,QAAQ,QAAQ,GAAG;AACnB,QAAQ,aAAa,GAAG,eACzB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/entities.js"], "sourcesContent": ["'use strict';\n\nvar freeze = require('./conventions').freeze;\n\n/**\n * The entities that are predefined in every XML document.\n *\n * @see https://www.w3.org/TR/2006/REC-xml11-20060816/#sec-predefined-ent W3C XML 1.1\n * @see https://www.w3.org/TR/2008/REC-xml-20081126/#sec-predefined-ent W3C XML 1.0\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Predefined_entities_in_XML Wikipedia\n */\nexports.XML_ENTITIES = freeze({\n\tamp: '&',\n\tapos: \"'\",\n\tgt: '>',\n\tlt: '<',\n\tquot: '\"',\n});\n\n/**\n * A map of all entities that are detected in an HTML document.\n * They contain all entries from `XML_ENTITIES`.\n *\n * @see XML_ENTITIES\n * @see DOMParser.parseFromString\n * @see DOMImplementation.prototype.createHTMLDocument\n * @see https://html.spec.whatwg.org/#named-character-references WHATWG HTML(5) Spec\n * @see https://html.spec.whatwg.org/entities.json JSON\n * @see https://www.w3.org/TR/xml-entity-names/ W3C XML Entity Names\n * @see https://www.w3.org/TR/html4/sgml/entities.html W3C HTML4/SGML\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Character_entity_references_in_HTML Wikipedia (HTML)\n * @see https://en.wikipedia.org/wiki/List_of_XML_and_HTML_character_entity_references#Entities_representing_special_characters_in_XHTML Wikpedia (XHTML)\n */\nexports.HTML_ENTITIES = freeze({\n\tAacute: '\\u00C1',\n\taacute: '\\u00E1',\n\tAbreve: '\\u0102',\n\tabreve: '\\u0103',\n\tac: '\\u223E',\n\tacd: '\\u223F',\n\tacE: '\\u223E\\u0333',\n\tAcirc: '\\u00C2',\n\tacirc: '\\u00E2',\n\tacute: '\\u00B4',\n\tAcy: '\\u0410',\n\tacy: '\\u0430',\n\tAElig: '\\u00C6',\n\taelig: '\\u00E6',\n\taf: '\\u2061',\n\tAfr: '\\uD835\\uDD04',\n\tafr: '\\uD835\\uDD1E',\n\tAgrave: '\\u00C0',\n\tagrave: '\\u00E0',\n\talefsym: '\\u2135',\n\taleph: '\\u2135',\n\tAlpha: '\\u0391',\n\talpha: '\\u03B1',\n\tAmacr: '\\u0100',\n\tamacr: '\\u0101',\n\tamalg: '\\u2A3F',\n\tAMP: '\\u0026',\n\tamp: '\\u0026',\n\tAnd: '\\u2A53',\n\tand: '\\u2227',\n\tandand: '\\u2A55',\n\tandd: '\\u2A5C',\n\tandslope: '\\u2A58',\n\tandv: '\\u2A5A',\n\tang: '\\u2220',\n\tange: '\\u29A4',\n\tangle: '\\u2220',\n\tangmsd: '\\u2221',\n\tangmsdaa: '\\u29A8',\n\tangmsdab: '\\u29A9',\n\tangmsdac: '\\u29AA',\n\tangmsdad: '\\u29AB',\n\tangmsdae: '\\u29AC',\n\tangmsdaf: '\\u29AD',\n\tangmsdag: '\\u29AE',\n\tangmsdah: '\\u29AF',\n\tangrt: '\\u221F',\n\tangrtvb: '\\u22BE',\n\tangrtvbd: '\\u299D',\n\tangsph: '\\u2222',\n\tangst: '\\u00C5',\n\tangzarr: '\\u237C',\n\tAogon: '\\u0104',\n\taogon: '\\u0105',\n\tAopf: '\\uD835\\uDD38',\n\taopf: '\\uD835\\uDD52',\n\tap: '\\u2248',\n\tapacir: '\\u2A6F',\n\tapE: '\\u2A70',\n\tape: '\\u224A',\n\tapid: '\\u224B',\n\tapos: '\\u0027',\n\tApplyFunction: '\\u2061',\n\tapprox: '\\u2248',\n\tapproxeq: '\\u224A',\n\tAring: '\\u00C5',\n\taring: '\\u00E5',\n\tAscr: '\\uD835\\uDC9C',\n\tascr: '\\uD835\\uDCB6',\n\tAssign: '\\u2254',\n\tast: '\\u002A',\n\tasymp: '\\u2248',\n\tasympeq: '\\u224D',\n\tAtilde: '\\u00C3',\n\tatilde: '\\u00E3',\n\tAuml: '\\u00C4',\n\tauml: '\\u00E4',\n\tawconint: '\\u2233',\n\tawint: '\\u2A11',\n\tbackcong: '\\u224C',\n\tbackepsilon: '\\u03F6',\n\tbackprime: '\\u2035',\n\tbacksim: '\\u223D',\n\tbacksimeq: '\\u22CD',\n\tBackslash: '\\u2216',\n\tBarv: '\\u2AE7',\n\tbarvee: '\\u22BD',\n\tBarwed: '\\u2306',\n\tbarwed: '\\u2305',\n\tbarwedge: '\\u2305',\n\tbbrk: '\\u23B5',\n\tbbrktbrk: '\\u23B6',\n\tbcong: '\\u224C',\n\tBcy: '\\u0411',\n\tbcy: '\\u0431',\n\tbdquo: '\\u201E',\n\tbecaus: '\\u2235',\n\tBecause: '\\u2235',\n\tbecause: '\\u2235',\n\tbemptyv: '\\u29B0',\n\tbepsi: '\\u03F6',\n\tbernou: '\\u212C',\n\tBernoullis: '\\u212C',\n\tBeta: '\\u0392',\n\tbeta: '\\u03B2',\n\tbeth: '\\u2136',\n\tbetween: '\\u226C',\n\tBfr: '\\uD835\\uDD05',\n\tbfr: '\\uD835\\uDD1F',\n\tbigcap: '\\u22C2',\n\tbigcirc: '\\u25EF',\n\tbigcup: '\\u22C3',\n\tbigodot: '\\u2A00',\n\tbigoplus: '\\u2A01',\n\tbigotimes: '\\u2A02',\n\tbigsqcup: '\\u2A06',\n\tbigstar: '\\u2605',\n\tbigtriangledown: '\\u25BD',\n\tbigtriangleup: '\\u25B3',\n\tbiguplus: '\\u2A04',\n\tbigvee: '\\u22C1',\n\tbigwedge: '\\u22C0',\n\tbkarow: '\\u290D',\n\tblacklozenge: '\\u29EB',\n\tblacksquare: '\\u25AA',\n\tblacktriangle: '\\u25B4',\n\tblacktriangledown: '\\u25BE',\n\tblacktriangleleft: '\\u25C2',\n\tblacktriangleright: '\\u25B8',\n\tblank: '\\u2423',\n\tblk12: '\\u2592',\n\tblk14: '\\u2591',\n\tblk34: '\\u2593',\n\tblock: '\\u2588',\n\tbne: '\\u003D\\u20E5',\n\tbnequiv: '\\u2261\\u20E5',\n\tbNot: '\\u2AED',\n\tbnot: '\\u2310',\n\tBopf: '\\uD835\\uDD39',\n\tbopf: '\\uD835\\uDD53',\n\tbot: '\\u22A5',\n\tbottom: '\\u22A5',\n\tbowtie: '\\u22C8',\n\tboxbox: '\\u29C9',\n\tboxDL: '\\u2557',\n\tboxDl: '\\u2556',\n\tboxdL: '\\u2555',\n\tboxdl: '\\u2510',\n\tboxDR: '\\u2554',\n\tboxDr: '\\u2553',\n\tboxdR: '\\u2552',\n\tboxdr: '\\u250C',\n\tboxH: '\\u2550',\n\tboxh: '\\u2500',\n\tboxHD: '\\u2566',\n\tboxHd: '\\u2564',\n\tboxhD: '\\u2565',\n\tboxhd: '\\u252C',\n\tboxHU: '\\u2569',\n\tboxHu: '\\u2567',\n\tboxhU: '\\u2568',\n\tboxhu: '\\u2534',\n\tboxminus: '\\u229F',\n\tboxplus: '\\u229E',\n\tboxtimes: '\\u22A0',\n\tboxUL: '\\u255D',\n\tboxUl: '\\u255C',\n\tboxuL: '\\u255B',\n\tboxul: '\\u2518',\n\tboxUR: '\\u255A',\n\tboxUr: '\\u2559',\n\tboxuR: '\\u2558',\n\tboxur: '\\u2514',\n\tboxV: '\\u2551',\n\tboxv: '\\u2502',\n\tboxVH: '\\u256C',\n\tboxVh: '\\u256B',\n\tboxvH: '\\u256A',\n\tboxvh: '\\u253C',\n\tboxVL: '\\u2563',\n\tboxVl: '\\u2562',\n\tboxvL: '\\u2561',\n\tboxvl: '\\u2524',\n\tboxVR: '\\u2560',\n\tboxVr: '\\u255F',\n\tboxvR: '\\u255E',\n\tboxvr: '\\u251C',\n\tbprime: '\\u2035',\n\tBreve: '\\u02D8',\n\tbreve: '\\u02D8',\n\tbrvbar: '\\u00A6',\n\tBscr: '\\u212C',\n\tbscr: '\\uD835\\uDCB7',\n\tbsemi: '\\u204F',\n\tbsim: '\\u223D',\n\tbsime: '\\u22CD',\n\tbsol: '\\u005C',\n\tbsolb: '\\u29C5',\n\tbsolhsub: '\\u27C8',\n\tbull: '\\u2022',\n\tbullet: '\\u2022',\n\tbump: '\\u224E',\n\tbumpE: '\\u2AAE',\n\tbumpe: '\\u224F',\n\tBumpeq: '\\u224E',\n\tbumpeq: '\\u224F',\n\tCacute: '\\u0106',\n\tcacute: '\\u0107',\n\tCap: '\\u22D2',\n\tcap: '\\u2229',\n\tcapand: '\\u2A44',\n\tcapbrcup: '\\u2A49',\n\tcapcap: '\\u2A4B',\n\tcapcup: '\\u2A47',\n\tcapdot: '\\u2A40',\n\tCapitalDifferentialD: '\\u2145',\n\tcaps: '\\u2229\\uFE00',\n\tcaret: '\\u2041',\n\tcaron: '\\u02C7',\n\tCayleys: '\\u212D',\n\tccaps: '\\u2A4D',\n\tCcaron: '\\u010C',\n\tccaron: '\\u010D',\n\tCcedil: '\\u00C7',\n\tccedil: '\\u00E7',\n\tCcirc: '\\u0108',\n\tccirc: '\\u0109',\n\tCconint: '\\u2230',\n\tccups: '\\u2A4C',\n\tccupssm: '\\u2A50',\n\tCdot: '\\u010A',\n\tcdot: '\\u010B',\n\tcedil: '\\u00B8',\n\tCedilla: '\\u00B8',\n\tcemptyv: '\\u29B2',\n\tcent: '\\u00A2',\n\tCenterDot: '\\u00B7',\n\tcenterdot: '\\u00B7',\n\tCfr: '\\u212D',\n\tcfr: '\\uD835\\uDD20',\n\tCHcy: '\\u0427',\n\tchcy: '\\u0447',\n\tcheck: '\\u2713',\n\tcheckmark: '\\u2713',\n\tChi: '\\u03A7',\n\tchi: '\\u03C7',\n\tcir: '\\u25CB',\n\tcirc: '\\u02C6',\n\tcirceq: '\\u2257',\n\tcirclearrowleft: '\\u21BA',\n\tcirclearrowright: '\\u21BB',\n\tcircledast: '\\u229B',\n\tcircledcirc: '\\u229A',\n\tcircleddash: '\\u229D',\n\tCircleDot: '\\u2299',\n\tcircledR: '\\u00AE',\n\tcircledS: '\\u24C8',\n\tCircleMinus: '\\u2296',\n\tCirclePlus: '\\u2295',\n\tCircleTimes: '\\u2297',\n\tcirE: '\\u29C3',\n\tcire: '\\u2257',\n\tcirfnint: '\\u2A10',\n\tcirmid: '\\u2AEF',\n\tcirscir: '\\u29C2',\n\tClockwiseContourIntegral: '\\u2232',\n\tCloseCurlyDoubleQuote: '\\u201D',\n\tCloseCurlyQuote: '\\u2019',\n\tclubs: '\\u2663',\n\tclubsuit: '\\u2663',\n\tColon: '\\u2237',\n\tcolon: '\\u003A',\n\tColone: '\\u2A74',\n\tcolone: '\\u2254',\n\tcoloneq: '\\u2254',\n\tcomma: '\\u002C',\n\tcommat: '\\u0040',\n\tcomp: '\\u2201',\n\tcompfn: '\\u2218',\n\tcomplement: '\\u2201',\n\tcomplexes: '\\u2102',\n\tcong: '\\u2245',\n\tcongdot: '\\u2A6D',\n\tCongruent: '\\u2261',\n\tConint: '\\u222F',\n\tconint: '\\u222E',\n\tContourIntegral: '\\u222E',\n\tCopf: '\\u2102',\n\tcopf: '\\uD835\\uDD54',\n\tcoprod: '\\u2210',\n\tCoproduct: '\\u2210',\n\tCOPY: '\\u00A9',\n\tcopy: '\\u00A9',\n\tcopysr: '\\u2117',\n\tCounterClockwiseContourIntegral: '\\u2233',\n\tcrarr: '\\u21B5',\n\tCross: '\\u2A2F',\n\tcross: '\\u2717',\n\tCscr: '\\uD835\\uDC9E',\n\tcscr: '\\uD835\\uDCB8',\n\tcsub: '\\u2ACF',\n\tcsube: '\\u2AD1',\n\tcsup: '\\u2AD0',\n\tcsupe: '\\u2AD2',\n\tctdot: '\\u22EF',\n\tcudarrl: '\\u2938',\n\tcudarrr: '\\u2935',\n\tcuepr: '\\u22DE',\n\tcuesc: '\\u22DF',\n\tcularr: '\\u21B6',\n\tcularrp: '\\u293D',\n\tCup: '\\u22D3',\n\tcup: '\\u222A',\n\tcupbrcap: '\\u2A48',\n\tCupCap: '\\u224D',\n\tcupcap: '\\u2A46',\n\tcupcup: '\\u2A4A',\n\tcupdot: '\\u228D',\n\tcupor: '\\u2A45',\n\tcups: '\\u222A\\uFE00',\n\tcurarr: '\\u21B7',\n\tcurarrm: '\\u293C',\n\tcurlyeqprec: '\\u22DE',\n\tcurlyeqsucc: '\\u22DF',\n\tcurlyvee: '\\u22CE',\n\tcurlywedge: '\\u22CF',\n\tcurren: '\\u00A4',\n\tcurvearrowleft: '\\u21B6',\n\tcurvearrowright: '\\u21B7',\n\tcuvee: '\\u22CE',\n\tcuwed: '\\u22CF',\n\tcwconint: '\\u2232',\n\tcwint: '\\u2231',\n\tcylcty: '\\u232D',\n\tDagger: '\\u2021',\n\tdagger: '\\u2020',\n\tdaleth: '\\u2138',\n\tDarr: '\\u21A1',\n\tdArr: '\\u21D3',\n\tdarr: '\\u2193',\n\tdash: '\\u2010',\n\tDashv: '\\u2AE4',\n\tdashv: '\\u22A3',\n\tdbkarow: '\\u290F',\n\tdblac: '\\u02DD',\n\tDcaron: '\\u010E',\n\tdcaron: '\\u010F',\n\tDcy: '\\u0414',\n\tdcy: '\\u0434',\n\tDD: '\\u2145',\n\tdd: '\\u2146',\n\tddagger: '\\u2021',\n\tddarr: '\\u21CA',\n\tDDotrahd: '\\u2911',\n\tddotseq: '\\u2A77',\n\tdeg: '\\u00B0',\n\tDel: '\\u2207',\n\tDelta: '\\u0394',\n\tdelta: '\\u03B4',\n\tdemptyv: '\\u29B1',\n\tdfisht: '\\u297F',\n\tDfr: '\\uD835\\uDD07',\n\tdfr: '\\uD835\\uDD21',\n\tdHar: '\\u2965',\n\tdharl: '\\u21C3',\n\tdharr: '\\u21C2',\n\tDiacriticalAcute: '\\u00B4',\n\tDiacriticalDot: '\\u02D9',\n\tDiacriticalDoubleAcute: '\\u02DD',\n\tDiacriticalGrave: '\\u0060',\n\tDiacriticalTilde: '\\u02DC',\n\tdiam: '\\u22C4',\n\tDiamond: '\\u22C4',\n\tdiamond: '\\u22C4',\n\tdiamondsuit: '\\u2666',\n\tdiams: '\\u2666',\n\tdie: '\\u00A8',\n\tDifferentialD: '\\u2146',\n\tdigamma: '\\u03DD',\n\tdisin: '\\u22F2',\n\tdiv: '\\u00F7',\n\tdivide: '\\u00F7',\n\tdivideontimes: '\\u22C7',\n\tdivonx: '\\u22C7',\n\tDJcy: '\\u0402',\n\tdjcy: '\\u0452',\n\tdlcorn: '\\u231E',\n\tdlcrop: '\\u230D',\n\tdollar: '\\u0024',\n\tDopf: '\\uD835\\uDD3B',\n\tdopf: '\\uD835\\uDD55',\n\tDot: '\\u00A8',\n\tdot: '\\u02D9',\n\tDotDot: '\\u20DC',\n\tdoteq: '\\u2250',\n\tdoteqdot: '\\u2251',\n\tDotEqual: '\\u2250',\n\tdotminus: '\\u2238',\n\tdotplus: '\\u2214',\n\tdotsquare: '\\u22A1',\n\tdoublebarwedge: '\\u2306',\n\tDoubleContourIntegral: '\\u222F',\n\tDoubleDot: '\\u00A8',\n\tDoubleDownArrow: '\\u21D3',\n\tDoubleLeftArrow: '\\u21D0',\n\tDoubleLeftRightArrow: '\\u21D4',\n\tDoubleLeftTee: '\\u2AE4',\n\tDoubleLongLeftArrow: '\\u27F8',\n\tDoubleLongLeftRightArrow: '\\u27FA',\n\tDoubleLongRightArrow: '\\u27F9',\n\tDoubleRightArrow: '\\u21D2',\n\tDoubleRightTee: '\\u22A8',\n\tDoubleUpArrow: '\\u21D1',\n\tDoubleUpDownArrow: '\\u21D5',\n\tDoubleVerticalBar: '\\u2225',\n\tDownArrow: '\\u2193',\n\tDownarrow: '\\u21D3',\n\tdownarrow: '\\u2193',\n\tDownArrowBar: '\\u2913',\n\tDownArrowUpArrow: '\\u21F5',\n\tDownBreve: '\\u0311',\n\tdowndownarrows: '\\u21CA',\n\tdownharpoonleft: '\\u21C3',\n\tdownharpoonright: '\\u21C2',\n\tDownLeftRightVector: '\\u2950',\n\tDownLeftTeeVector: '\\u295E',\n\tDownLeftVector: '\\u21BD',\n\tDownLeftVectorBar: '\\u2956',\n\tDownRightTeeVector: '\\u295F',\n\tDownRightVector: '\\u21C1',\n\tDownRightVectorBar: '\\u2957',\n\tDownTee: '\\u22A4',\n\tDownTeeArrow: '\\u21A7',\n\tdrbkarow: '\\u2910',\n\tdrcorn: '\\u231F',\n\tdrcrop: '\\u230C',\n\tDscr: '\\uD835\\uDC9F',\n\tdscr: '\\uD835\\uDCB9',\n\tDScy: '\\u0405',\n\tdscy: '\\u0455',\n\tdsol: '\\u29F6',\n\tDstrok: '\\u0110',\n\tdstrok: '\\u0111',\n\tdtdot: '\\u22F1',\n\tdtri: '\\u25BF',\n\tdtrif: '\\u25BE',\n\tduarr: '\\u21F5',\n\tduhar: '\\u296F',\n\tdwangle: '\\u29A6',\n\tDZcy: '\\u040F',\n\tdzcy: '\\u045F',\n\tdzigrarr: '\\u27FF',\n\tEacute: '\\u00C9',\n\teacute: '\\u00E9',\n\teaster: '\\u2A6E',\n\tEcaron: '\\u011A',\n\tecaron: '\\u011B',\n\tecir: '\\u2256',\n\tEcirc: '\\u00CA',\n\tecirc: '\\u00EA',\n\tecolon: '\\u2255',\n\tEcy: '\\u042D',\n\tecy: '\\u044D',\n\teDDot: '\\u2A77',\n\tEdot: '\\u0116',\n\teDot: '\\u2251',\n\tedot: '\\u0117',\n\tee: '\\u2147',\n\tefDot: '\\u2252',\n\tEfr: '\\uD835\\uDD08',\n\tefr: '\\uD835\\uDD22',\n\teg: '\\u2A9A',\n\tEgrave: '\\u00C8',\n\tegrave: '\\u00E8',\n\tegs: '\\u2A96',\n\tegsdot: '\\u2A98',\n\tel: '\\u2A99',\n\tElement: '\\u2208',\n\telinters: '\\u23E7',\n\tell: '\\u2113',\n\tels: '\\u2A95',\n\telsdot: '\\u2A97',\n\tEmacr: '\\u0112',\n\temacr: '\\u0113',\n\tempty: '\\u2205',\n\temptyset: '\\u2205',\n\tEmptySmallSquare: '\\u25FB',\n\temptyv: '\\u2205',\n\tEmptyVerySmallSquare: '\\u25AB',\n\temsp: '\\u2003',\n\temsp13: '\\u2004',\n\temsp14: '\\u2005',\n\tENG: '\\u014A',\n\teng: '\\u014B',\n\tensp: '\\u2002',\n\tEogon: '\\u0118',\n\teogon: '\\u0119',\n\tEopf: '\\uD835\\uDD3C',\n\teopf: '\\uD835\\uDD56',\n\tepar: '\\u22D5',\n\teparsl: '\\u29E3',\n\teplus: '\\u2A71',\n\tepsi: '\\u03B5',\n\tEpsilon: '\\u0395',\n\tepsilon: '\\u03B5',\n\tepsiv: '\\u03F5',\n\teqcirc: '\\u2256',\n\teqcolon: '\\u2255',\n\teqsim: '\\u2242',\n\teqslantgtr: '\\u2A96',\n\teqslantless: '\\u2A95',\n\tEqual: '\\u2A75',\n\tequals: '\\u003D',\n\tEqualTilde: '\\u2242',\n\tequest: '\\u225F',\n\tEquilibrium: '\\u21CC',\n\tequiv: '\\u2261',\n\tequivDD: '\\u2A78',\n\teqvparsl: '\\u29E5',\n\terarr: '\\u2971',\n\terDot: '\\u2253',\n\tEscr: '\\u2130',\n\tescr: '\\u212F',\n\tesdot: '\\u2250',\n\tEsim: '\\u2A73',\n\tesim: '\\u2242',\n\tEta: '\\u0397',\n\teta: '\\u03B7',\n\tETH: '\\u00D0',\n\teth: '\\u00F0',\n\tEuml: '\\u00CB',\n\teuml: '\\u00EB',\n\teuro: '\\u20AC',\n\texcl: '\\u0021',\n\texist: '\\u2203',\n\tExists: '\\u2203',\n\texpectation: '\\u2130',\n\tExponentialE: '\\u2147',\n\texponentiale: '\\u2147',\n\tfallingdotseq: '\\u2252',\n\tFcy: '\\u0424',\n\tfcy: '\\u0444',\n\tfemale: '\\u2640',\n\tffilig: '\\uFB03',\n\tfflig: '\\uFB00',\n\tffllig: '\\uFB04',\n\tFfr: '\\uD835\\uDD09',\n\tffr: '\\uD835\\uDD23',\n\tfilig: '\\uFB01',\n\tFilledSmallSquare: '\\u25FC',\n\tFilledVerySmallSquare: '\\u25AA',\n\tfjlig: '\\u0066\\u006A',\n\tflat: '\\u266D',\n\tfllig: '\\uFB02',\n\tfltns: '\\u25B1',\n\tfnof: '\\u0192',\n\tFopf: '\\uD835\\uDD3D',\n\tfopf: '\\uD835\\uDD57',\n\tForAll: '\\u2200',\n\tforall: '\\u2200',\n\tfork: '\\u22D4',\n\tforkv: '\\u2AD9',\n\tFouriertrf: '\\u2131',\n\tfpartint: '\\u2A0D',\n\tfrac12: '\\u00BD',\n\tfrac13: '\\u2153',\n\tfrac14: '\\u00BC',\n\tfrac15: '\\u2155',\n\tfrac16: '\\u2159',\n\tfrac18: '\\u215B',\n\tfrac23: '\\u2154',\n\tfrac25: '\\u2156',\n\tfrac34: '\\u00BE',\n\tfrac35: '\\u2157',\n\tfrac38: '\\u215C',\n\tfrac45: '\\u2158',\n\tfrac56: '\\u215A',\n\tfrac58: '\\u215D',\n\tfrac78: '\\u215E',\n\tfrasl: '\\u2044',\n\tfrown: '\\u2322',\n\tFscr: '\\u2131',\n\tfscr: '\\uD835\\uDCBB',\n\tgacute: '\\u01F5',\n\tGamma: '\\u0393',\n\tgamma: '\\u03B3',\n\tGammad: '\\u03DC',\n\tgammad: '\\u03DD',\n\tgap: '\\u2A86',\n\tGbreve: '\\u011E',\n\tgbreve: '\\u011F',\n\tGcedil: '\\u0122',\n\tGcirc: '\\u011C',\n\tgcirc: '\\u011D',\n\tGcy: '\\u0413',\n\tgcy: '\\u0433',\n\tGdot: '\\u0120',\n\tgdot: '\\u0121',\n\tgE: '\\u2267',\n\tge: '\\u2265',\n\tgEl: '\\u2A8C',\n\tgel: '\\u22DB',\n\tgeq: '\\u2265',\n\tgeqq: '\\u2267',\n\tgeqslant: '\\u2A7E',\n\tges: '\\u2A7E',\n\tgescc: '\\u2AA9',\n\tgesdot: '\\u2A80',\n\tgesdoto: '\\u2A82',\n\tgesdotol: '\\u2A84',\n\tgesl: '\\u22DB\\uFE00',\n\tgesles: '\\u2A94',\n\tGfr: '\\uD835\\uDD0A',\n\tgfr: '\\uD835\\uDD24',\n\tGg: '\\u22D9',\n\tgg: '\\u226B',\n\tggg: '\\u22D9',\n\tgimel: '\\u2137',\n\tGJcy: '\\u0403',\n\tgjcy: '\\u0453',\n\tgl: '\\u2277',\n\tgla: '\\u2AA5',\n\tglE: '\\u2A92',\n\tglj: '\\u2AA4',\n\tgnap: '\\u2A8A',\n\tgnapprox: '\\u2A8A',\n\tgnE: '\\u2269',\n\tgne: '\\u2A88',\n\tgneq: '\\u2A88',\n\tgneqq: '\\u2269',\n\tgnsim: '\\u22E7',\n\tGopf: '\\uD835\\uDD3E',\n\tgopf: '\\uD835\\uDD58',\n\tgrave: '\\u0060',\n\tGreaterEqual: '\\u2265',\n\tGreaterEqualLess: '\\u22DB',\n\tGreaterFullEqual: '\\u2267',\n\tGreaterGreater: '\\u2AA2',\n\tGreaterLess: '\\u2277',\n\tGreaterSlantEqual: '\\u2A7E',\n\tGreaterTilde: '\\u2273',\n\tGscr: '\\uD835\\uDCA2',\n\tgscr: '\\u210A',\n\tgsim: '\\u2273',\n\tgsime: '\\u2A8E',\n\tgsiml: '\\u2A90',\n\tGt: '\\u226B',\n\tGT: '\\u003E',\n\tgt: '\\u003E',\n\tgtcc: '\\u2AA7',\n\tgtcir: '\\u2A7A',\n\tgtdot: '\\u22D7',\n\tgtlPar: '\\u2995',\n\tgtquest: '\\u2A7C',\n\tgtrapprox: '\\u2A86',\n\tgtrarr: '\\u2978',\n\tgtrdot: '\\u22D7',\n\tgtreqless: '\\u22DB',\n\tgtreqqless: '\\u2A8C',\n\tgtrless: '\\u2277',\n\tgtrsim: '\\u2273',\n\tgvertneqq: '\\u2269\\uFE00',\n\tgvnE: '\\u2269\\uFE00',\n\tHacek: '\\u02C7',\n\thairsp: '\\u200A',\n\thalf: '\\u00BD',\n\thamilt: '\\u210B',\n\tHARDcy: '\\u042A',\n\thardcy: '\\u044A',\n\thArr: '\\u21D4',\n\tharr: '\\u2194',\n\tharrcir: '\\u2948',\n\tharrw: '\\u21AD',\n\tHat: '\\u005E',\n\thbar: '\\u210F',\n\tHcirc: '\\u0124',\n\thcirc: '\\u0125',\n\thearts: '\\u2665',\n\theartsuit: '\\u2665',\n\thellip: '\\u2026',\n\thercon: '\\u22B9',\n\tHfr: '\\u210C',\n\thfr: '\\uD835\\uDD25',\n\tHilbertSpace: '\\u210B',\n\thksearow: '\\u2925',\n\thkswarow: '\\u2926',\n\thoarr: '\\u21FF',\n\thomtht: '\\u223B',\n\thookleftarrow: '\\u21A9',\n\thookrightarrow: '\\u21AA',\n\tHopf: '\\u210D',\n\thopf: '\\uD835\\uDD59',\n\thorbar: '\\u2015',\n\tHorizontalLine: '\\u2500',\n\tHscr: '\\u210B',\n\thscr: '\\uD835\\uDCBD',\n\thslash: '\\u210F',\n\tHstrok: '\\u0126',\n\thstrok: '\\u0127',\n\tHumpDownHump: '\\u224E',\n\tHumpEqual: '\\u224F',\n\thybull: '\\u2043',\n\thyphen: '\\u2010',\n\tIacute: '\\u00CD',\n\tiacute: '\\u00ED',\n\tic: '\\u2063',\n\tIcirc: '\\u00CE',\n\ticirc: '\\u00EE',\n\tIcy: '\\u0418',\n\ticy: '\\u0438',\n\tIdot: '\\u0130',\n\tIEcy: '\\u0415',\n\tiecy: '\\u0435',\n\tiexcl: '\\u00A1',\n\tiff: '\\u21D4',\n\tIfr: '\\u2111',\n\tifr: '\\uD835\\uDD26',\n\tIgrave: '\\u00CC',\n\tigrave: '\\u00EC',\n\tii: '\\u2148',\n\tiiiint: '\\u2A0C',\n\tiiint: '\\u222D',\n\tiinfin: '\\u29DC',\n\tiiota: '\\u2129',\n\tIJlig: '\\u0132',\n\tijlig: '\\u0133',\n\tIm: '\\u2111',\n\tImacr: '\\u012A',\n\timacr: '\\u012B',\n\timage: '\\u2111',\n\tImaginaryI: '\\u2148',\n\timagline: '\\u2110',\n\timagpart: '\\u2111',\n\timath: '\\u0131',\n\timof: '\\u22B7',\n\timped: '\\u01B5',\n\tImplies: '\\u21D2',\n\tin: '\\u2208',\n\tincare: '\\u2105',\n\tinfin: '\\u221E',\n\tinfintie: '\\u29DD',\n\tinodot: '\\u0131',\n\tInt: '\\u222C',\n\tint: '\\u222B',\n\tintcal: '\\u22BA',\n\tintegers: '\\u2124',\n\tIntegral: '\\u222B',\n\tintercal: '\\u22BA',\n\tIntersection: '\\u22C2',\n\tintlarhk: '\\u2A17',\n\tintprod: '\\u2A3C',\n\tInvisibleComma: '\\u2063',\n\tInvisibleTimes: '\\u2062',\n\tIOcy: '\\u0401',\n\tiocy: '\\u0451',\n\tIogon: '\\u012E',\n\tiogon: '\\u012F',\n\tIopf: '\\uD835\\uDD40',\n\tiopf: '\\uD835\\uDD5A',\n\tIota: '\\u0399',\n\tiota: '\\u03B9',\n\tiprod: '\\u2A3C',\n\tiquest: '\\u00BF',\n\tIscr: '\\u2110',\n\tiscr: '\\uD835\\uDCBE',\n\tisin: '\\u2208',\n\tisindot: '\\u22F5',\n\tisinE: '\\u22F9',\n\tisins: '\\u22F4',\n\tisinsv: '\\u22F3',\n\tisinv: '\\u2208',\n\tit: '\\u2062',\n\tItilde: '\\u0128',\n\titilde: '\\u0129',\n\tIukcy: '\\u0406',\n\tiukcy: '\\u0456',\n\tIuml: '\\u00CF',\n\tiuml: '\\u00EF',\n\tJcirc: '\\u0134',\n\tjcirc: '\\u0135',\n\tJcy: '\\u0419',\n\tjcy: '\\u0439',\n\tJfr: '\\uD835\\uDD0D',\n\tjfr: '\\uD835\\uDD27',\n\tjmath: '\\u0237',\n\tJopf: '\\uD835\\uDD41',\n\tjopf: '\\uD835\\uDD5B',\n\tJscr: '\\uD835\\uDCA5',\n\tjscr: '\\uD835\\uDCBF',\n\tJsercy: '\\u0408',\n\tjsercy: '\\u0458',\n\tJukcy: '\\u0404',\n\tjukcy: '\\u0454',\n\tKappa: '\\u039A',\n\tkappa: '\\u03BA',\n\tkappav: '\\u03F0',\n\tKcedil: '\\u0136',\n\tkcedil: '\\u0137',\n\tKcy: '\\u041A',\n\tkcy: '\\u043A',\n\tKfr: '\\uD835\\uDD0E',\n\tkfr: '\\uD835\\uDD28',\n\tkgreen: '\\u0138',\n\tKHcy: '\\u0425',\n\tkhcy: '\\u0445',\n\tKJcy: '\\u040C',\n\tkjcy: '\\u045C',\n\tKopf: '\\uD835\\uDD42',\n\tkopf: '\\uD835\\uDD5C',\n\tKscr: '\\uD835\\uDCA6',\n\tkscr: '\\uD835\\uDCC0',\n\tlAarr: '\\u21DA',\n\tLacute: '\\u0139',\n\tlacute: '\\u013A',\n\tlaemptyv: '\\u29B4',\n\tlagran: '\\u2112',\n\tLambda: '\\u039B',\n\tlambda: '\\u03BB',\n\tLang: '\\u27EA',\n\tlang: '\\u27E8',\n\tlangd: '\\u2991',\n\tlangle: '\\u27E8',\n\tlap: '\\u2A85',\n\tLaplacetrf: '\\u2112',\n\tlaquo: '\\u00AB',\n\tLarr: '\\u219E',\n\tlArr: '\\u21D0',\n\tlarr: '\\u2190',\n\tlarrb: '\\u21E4',\n\tlarrbfs: '\\u291F',\n\tlarrfs: '\\u291D',\n\tlarrhk: '\\u21A9',\n\tlarrlp: '\\u21AB',\n\tlarrpl: '\\u2939',\n\tlarrsim: '\\u2973',\n\tlarrtl: '\\u21A2',\n\tlat: '\\u2AAB',\n\tlAtail: '\\u291B',\n\tlatail: '\\u2919',\n\tlate: '\\u2AAD',\n\tlates: '\\u2AAD\\uFE00',\n\tlBarr: '\\u290E',\n\tlbarr: '\\u290C',\n\tlbbrk: '\\u2772',\n\tlbrace: '\\u007B',\n\tlbrack: '\\u005B',\n\tlbrke: '\\u298B',\n\tlbrksld: '\\u298F',\n\tlbrkslu: '\\u298D',\n\tLcaron: '\\u013D',\n\tlcaron: '\\u013E',\n\tLcedil: '\\u013B',\n\tlcedil: '\\u013C',\n\tlceil: '\\u2308',\n\tlcub: '\\u007B',\n\tLcy: '\\u041B',\n\tlcy: '\\u043B',\n\tldca: '\\u2936',\n\tldquo: '\\u201C',\n\tldquor: '\\u201E',\n\tldrdhar: '\\u2967',\n\tldrushar: '\\u294B',\n\tldsh: '\\u21B2',\n\tlE: '\\u2266',\n\tle: '\\u2264',\n\tLeftAngleBracket: '\\u27E8',\n\tLeftArrow: '\\u2190',\n\tLeftarrow: '\\u21D0',\n\tleftarrow: '\\u2190',\n\tLeftArrowBar: '\\u21E4',\n\tLeftArrowRightArrow: '\\u21C6',\n\tleftarrowtail: '\\u21A2',\n\tLeftCeiling: '\\u2308',\n\tLeftDoubleBracket: '\\u27E6',\n\tLeftDownTeeVector: '\\u2961',\n\tLeftDownVector: '\\u21C3',\n\tLeftDownVectorBar: '\\u2959',\n\tLeftFloor: '\\u230A',\n\tleftharpoondown: '\\u21BD',\n\tleftharpoonup: '\\u21BC',\n\tleftleftarrows: '\\u21C7',\n\tLeftRightArrow: '\\u2194',\n\tLeftrightarrow: '\\u21D4',\n\tleftrightarrow: '\\u2194',\n\tleftrightarrows: '\\u21C6',\n\tleftrightharpoons: '\\u21CB',\n\tleftrightsquigarrow: '\\u21AD',\n\tLeftRightVector: '\\u294E',\n\tLeftTee: '\\u22A3',\n\tLeftTeeArrow: '\\u21A4',\n\tLeftTeeVector: '\\u295A',\n\tleftthreetimes: '\\u22CB',\n\tLeftTriangle: '\\u22B2',\n\tLeftTriangleBar: '\\u29CF',\n\tLeftTriangleEqual: '\\u22B4',\n\tLeftUpDownVector: '\\u2951',\n\tLeftUpTeeVector: '\\u2960',\n\tLeftUpVector: '\\u21BF',\n\tLeftUpVectorBar: '\\u2958',\n\tLeftVector: '\\u21BC',\n\tLeftVectorBar: '\\u2952',\n\tlEg: '\\u2A8B',\n\tleg: '\\u22DA',\n\tleq: '\\u2264',\n\tleqq: '\\u2266',\n\tleqslant: '\\u2A7D',\n\tles: '\\u2A7D',\n\tlescc: '\\u2AA8',\n\tlesdot: '\\u2A7F',\n\tlesdoto: '\\u2A81',\n\tlesdotor: '\\u2A83',\n\tlesg: '\\u22DA\\uFE00',\n\tlesges: '\\u2A93',\n\tlessapprox: '\\u2A85',\n\tlessdot: '\\u22D6',\n\tlesseqgtr: '\\u22DA',\n\tlesseqqgtr: '\\u2A8B',\n\tLessEqualGreater: '\\u22DA',\n\tLessFullEqual: '\\u2266',\n\tLessGreater: '\\u2276',\n\tlessgtr: '\\u2276',\n\tLessLess: '\\u2AA1',\n\tlesssim: '\\u2272',\n\tLessSlantEqual: '\\u2A7D',\n\tLessTilde: '\\u2272',\n\tlfisht: '\\u297C',\n\tlfloor: '\\u230A',\n\tLfr: '\\uD835\\uDD0F',\n\tlfr: '\\uD835\\uDD29',\n\tlg: '\\u2276',\n\tlgE: '\\u2A91',\n\tlHar: '\\u2962',\n\tlhard: '\\u21BD',\n\tlharu: '\\u21BC',\n\tlharul: '\\u296A',\n\tlhblk: '\\u2584',\n\tLJcy: '\\u0409',\n\tljcy: '\\u0459',\n\tLl: '\\u22D8',\n\tll: '\\u226A',\n\tllarr: '\\u21C7',\n\tllcorner: '\\u231E',\n\tLleftarrow: '\\u21DA',\n\tllhard: '\\u296B',\n\tlltri: '\\u25FA',\n\tLmidot: '\\u013F',\n\tlmidot: '\\u0140',\n\tlmoust: '\\u23B0',\n\tlmoustache: '\\u23B0',\n\tlnap: '\\u2A89',\n\tlnapprox: '\\u2A89',\n\tlnE: '\\u2268',\n\tlne: '\\u2A87',\n\tlneq: '\\u2A87',\n\tlneqq: '\\u2268',\n\tlnsim: '\\u22E6',\n\tloang: '\\u27EC',\n\tloarr: '\\u21FD',\n\tlobrk: '\\u27E6',\n\tLongLeftArrow: '\\u27F5',\n\tLongleftarrow: '\\u27F8',\n\tlongleftarrow: '\\u27F5',\n\tLongLeftRightArrow: '\\u27F7',\n\tLongleftrightarrow: '\\u27FA',\n\tlongleftrightarrow: '\\u27F7',\n\tlongmapsto: '\\u27FC',\n\tLongRightArrow: '\\u27F6',\n\tLongrightarrow: '\\u27F9',\n\tlongrightarrow: '\\u27F6',\n\tlooparrowleft: '\\u21AB',\n\tlooparrowright: '\\u21AC',\n\tlopar: '\\u2985',\n\tLopf: '\\uD835\\uDD43',\n\tlopf: '\\uD835\\uDD5D',\n\tloplus: '\\u2A2D',\n\tlotimes: '\\u2A34',\n\tlowast: '\\u2217',\n\tlowbar: '\\u005F',\n\tLowerLeftArrow: '\\u2199',\n\tLowerRightArrow: '\\u2198',\n\tloz: '\\u25CA',\n\tlozenge: '\\u25CA',\n\tlozf: '\\u29EB',\n\tlpar: '\\u0028',\n\tlparlt: '\\u2993',\n\tlrarr: '\\u21C6',\n\tlrcorner: '\\u231F',\n\tlrhar: '\\u21CB',\n\tlrhard: '\\u296D',\n\tlrm: '\\u200E',\n\tlrtri: '\\u22BF',\n\tlsaquo: '\\u2039',\n\tLscr: '\\u2112',\n\tlscr: '\\uD835\\uDCC1',\n\tLsh: '\\u21B0',\n\tlsh: '\\u21B0',\n\tlsim: '\\u2272',\n\tlsime: '\\u2A8D',\n\tlsimg: '\\u2A8F',\n\tlsqb: '\\u005B',\n\tlsquo: '\\u2018',\n\tlsquor: '\\u201A',\n\tLstrok: '\\u0141',\n\tlstrok: '\\u0142',\n\tLt: '\\u226A',\n\tLT: '\\u003C',\n\tlt: '\\u003C',\n\tltcc: '\\u2AA6',\n\tltcir: '\\u2A79',\n\tltdot: '\\u22D6',\n\tlthree: '\\u22CB',\n\tltimes: '\\u22C9',\n\tltlarr: '\\u2976',\n\tltquest: '\\u2A7B',\n\tltri: '\\u25C3',\n\tltrie: '\\u22B4',\n\tltrif: '\\u25C2',\n\tltrPar: '\\u2996',\n\tlurdshar: '\\u294A',\n\tluruhar: '\\u2966',\n\tlvertneqq: '\\u2268\\uFE00',\n\tlvnE: '\\u2268\\uFE00',\n\tmacr: '\\u00AF',\n\tmale: '\\u2642',\n\tmalt: '\\u2720',\n\tmaltese: '\\u2720',\n\tMap: '\\u2905',\n\tmap: '\\u21A6',\n\tmapsto: '\\u21A6',\n\tmapstodown: '\\u21A7',\n\tmapstoleft: '\\u21A4',\n\tmapstoup: '\\u21A5',\n\tmarker: '\\u25AE',\n\tmcomma: '\\u2A29',\n\tMcy: '\\u041C',\n\tmcy: '\\u043C',\n\tmdash: '\\u2014',\n\tmDDot: '\\u223A',\n\tmeasuredangle: '\\u2221',\n\tMediumSpace: '\\u205F',\n\tMellintrf: '\\u2133',\n\tMfr: '\\uD835\\uDD10',\n\tmfr: '\\uD835\\uDD2A',\n\tmho: '\\u2127',\n\tmicro: '\\u00B5',\n\tmid: '\\u2223',\n\tmidast: '\\u002A',\n\tmidcir: '\\u2AF0',\n\tmiddot: '\\u00B7',\n\tminus: '\\u2212',\n\tminusb: '\\u229F',\n\tminusd: '\\u2238',\n\tminusdu: '\\u2A2A',\n\tMinusPlus: '\\u2213',\n\tmlcp: '\\u2ADB',\n\tmldr: '\\u2026',\n\tmnplus: '\\u2213',\n\tmodels: '\\u22A7',\n\tMopf: '\\uD835\\uDD44',\n\tmopf: '\\uD835\\uDD5E',\n\tmp: '\\u2213',\n\tMscr: '\\u2133',\n\tmscr: '\\uD835\\uDCC2',\n\tmstpos: '\\u223E',\n\tMu: '\\u039C',\n\tmu: '\\u03BC',\n\tmultimap: '\\u22B8',\n\tmumap: '\\u22B8',\n\tnabla: '\\u2207',\n\tNacute: '\\u0143',\n\tnacute: '\\u0144',\n\tnang: '\\u2220\\u20D2',\n\tnap: '\\u2249',\n\tnapE: '\\u2A70\\u0338',\n\tnapid: '\\u224B\\u0338',\n\tnapos: '\\u0149',\n\tnapprox: '\\u2249',\n\tnatur: '\\u266E',\n\tnatural: '\\u266E',\n\tnaturals: '\\u2115',\n\tnbsp: '\\u00A0',\n\tnbump: '\\u224E\\u0338',\n\tnbumpe: '\\u224F\\u0338',\n\tncap: '\\u2A43',\n\tNcaron: '\\u0147',\n\tncaron: '\\u0148',\n\tNcedil: '\\u0145',\n\tncedil: '\\u0146',\n\tncong: '\\u2247',\n\tncongdot: '\\u2A6D\\u0338',\n\tncup: '\\u2A42',\n\tNcy: '\\u041D',\n\tncy: '\\u043D',\n\tndash: '\\u2013',\n\tne: '\\u2260',\n\tnearhk: '\\u2924',\n\tneArr: '\\u21D7',\n\tnearr: '\\u2197',\n\tnearrow: '\\u2197',\n\tnedot: '\\u2250\\u0338',\n\tNegativeMediumSpace: '\\u200B',\n\tNegativeThickSpace: '\\u200B',\n\tNegativeThinSpace: '\\u200B',\n\tNegativeVeryThinSpace: '\\u200B',\n\tnequiv: '\\u2262',\n\tnesear: '\\u2928',\n\tnesim: '\\u2242\\u0338',\n\tNestedGreaterGreater: '\\u226B',\n\tNestedLessLess: '\\u226A',\n\tNewLine: '\\u000A',\n\tnexist: '\\u2204',\n\tnexists: '\\u2204',\n\tNfr: '\\uD835\\uDD11',\n\tnfr: '\\uD835\\uDD2B',\n\tngE: '\\u2267\\u0338',\n\tnge: '\\u2271',\n\tngeq: '\\u2271',\n\tngeqq: '\\u2267\\u0338',\n\tngeqslant: '\\u2A7E\\u0338',\n\tnges: '\\u2A7E\\u0338',\n\tnGg: '\\u22D9\\u0338',\n\tngsim: '\\u2275',\n\tnGt: '\\u226B\\u20D2',\n\tngt: '\\u226F',\n\tngtr: '\\u226F',\n\tnGtv: '\\u226B\\u0338',\n\tnhArr: '\\u21CE',\n\tnharr: '\\u21AE',\n\tnhpar: '\\u2AF2',\n\tni: '\\u220B',\n\tnis: '\\u22FC',\n\tnisd: '\\u22FA',\n\tniv: '\\u220B',\n\tNJcy: '\\u040A',\n\tnjcy: '\\u045A',\n\tnlArr: '\\u21CD',\n\tnlarr: '\\u219A',\n\tnldr: '\\u2025',\n\tnlE: '\\u2266\\u0338',\n\tnle: '\\u2270',\n\tnLeftarrow: '\\u21CD',\n\tnleftarrow: '\\u219A',\n\tnLeftrightarrow: '\\u21CE',\n\tnleftrightarrow: '\\u21AE',\n\tnleq: '\\u2270',\n\tnleqq: '\\u2266\\u0338',\n\tnleqslant: '\\u2A7D\\u0338',\n\tnles: '\\u2A7D\\u0338',\n\tnless: '\\u226E',\n\tnLl: '\\u22D8\\u0338',\n\tnlsim: '\\u2274',\n\tnLt: '\\u226A\\u20D2',\n\tnlt: '\\u226E',\n\tnltri: '\\u22EA',\n\tnltrie: '\\u22EC',\n\tnLtv: '\\u226A\\u0338',\n\tnmid: '\\u2224',\n\tNoBreak: '\\u2060',\n\tNonBreakingSpace: '\\u00A0',\n\tNopf: '\\u2115',\n\tnopf: '\\uD835\\uDD5F',\n\tNot: '\\u2AEC',\n\tnot: '\\u00AC',\n\tNotCongruent: '\\u2262',\n\tNotCupCap: '\\u226D',\n\tNotDoubleVerticalBar: '\\u2226',\n\tNotElement: '\\u2209',\n\tNotEqual: '\\u2260',\n\tNotEqualTilde: '\\u2242\\u0338',\n\tNotExists: '\\u2204',\n\tNotGreater: '\\u226F',\n\tNotGreaterEqual: '\\u2271',\n\tNotGreaterFullEqual: '\\u2267\\u0338',\n\tNotGreaterGreater: '\\u226B\\u0338',\n\tNotGreaterLess: '\\u2279',\n\tNotGreaterSlantEqual: '\\u2A7E\\u0338',\n\tNotGreaterTilde: '\\u2275',\n\tNotHumpDownHump: '\\u224E\\u0338',\n\tNotHumpEqual: '\\u224F\\u0338',\n\tnotin: '\\u2209',\n\tnotindot: '\\u22F5\\u0338',\n\tnotinE: '\\u22F9\\u0338',\n\tnotinva: '\\u2209',\n\tnotinvb: '\\u22F7',\n\tnotinvc: '\\u22F6',\n\tNotLeftTriangle: '\\u22EA',\n\tNotLeftTriangleBar: '\\u29CF\\u0338',\n\tNotLeftTriangleEqual: '\\u22EC',\n\tNotLess: '\\u226E',\n\tNotLessEqual: '\\u2270',\n\tNotLessGreater: '\\u2278',\n\tNotLessLess: '\\u226A\\u0338',\n\tNotLessSlantEqual: '\\u2A7D\\u0338',\n\tNotLessTilde: '\\u2274',\n\tNotNestedGreaterGreater: '\\u2AA2\\u0338',\n\tNotNestedLessLess: '\\u2AA1\\u0338',\n\tnotni: '\\u220C',\n\tnotniva: '\\u220C',\n\tnotnivb: '\\u22FE',\n\tnotnivc: '\\u22FD',\n\tNotPrecedes: '\\u2280',\n\tNotPrecedesEqual: '\\u2AAF\\u0338',\n\tNotPrecedesSlantEqual: '\\u22E0',\n\tNotReverseElement: '\\u220C',\n\tNotRightTriangle: '\\u22EB',\n\tNotRightTriangleBar: '\\u29D0\\u0338',\n\tNotRightTriangleEqual: '\\u22ED',\n\tNotSquareSubset: '\\u228F\\u0338',\n\tNotSquareSubsetEqual: '\\u22E2',\n\tNotSquareSuperset: '\\u2290\\u0338',\n\tNotSquareSupersetEqual: '\\u22E3',\n\tNotSubset: '\\u2282\\u20D2',\n\tNotSubsetEqual: '\\u2288',\n\tNotSucceeds: '\\u2281',\n\tNotSucceedsEqual: '\\u2AB0\\u0338',\n\tNotSucceedsSlantEqual: '\\u22E1',\n\tNotSucceedsTilde: '\\u227F\\u0338',\n\tNotSuperset: '\\u2283\\u20D2',\n\tNotSupersetEqual: '\\u2289',\n\tNotTilde: '\\u2241',\n\tNotTildeEqual: '\\u2244',\n\tNotTildeFullEqual: '\\u2247',\n\tNotTildeTilde: '\\u2249',\n\tNotVerticalBar: '\\u2224',\n\tnpar: '\\u2226',\n\tnparallel: '\\u2226',\n\tnparsl: '\\u2AFD\\u20E5',\n\tnpart: '\\u2202\\u0338',\n\tnpolint: '\\u2A14',\n\tnpr: '\\u2280',\n\tnprcue: '\\u22E0',\n\tnpre: '\\u2AAF\\u0338',\n\tnprec: '\\u2280',\n\tnpreceq: '\\u2AAF\\u0338',\n\tnrArr: '\\u21CF',\n\tnrarr: '\\u219B',\n\tnrarrc: '\\u2933\\u0338',\n\tnrarrw: '\\u219D\\u0338',\n\tnRightarrow: '\\u21CF',\n\tnrightarrow: '\\u219B',\n\tnrtri: '\\u22EB',\n\tnrtrie: '\\u22ED',\n\tnsc: '\\u2281',\n\tnsccue: '\\u22E1',\n\tnsce: '\\u2AB0\\u0338',\n\tNscr: '\\uD835\\uDCA9',\n\tnscr: '\\uD835\\uDCC3',\n\tnshortmid: '\\u2224',\n\tnshortparallel: '\\u2226',\n\tnsim: '\\u2241',\n\tnsime: '\\u2244',\n\tnsimeq: '\\u2244',\n\tnsmid: '\\u2224',\n\tnspar: '\\u2226',\n\tnsqsube: '\\u22E2',\n\tnsqsupe: '\\u22E3',\n\tnsub: '\\u2284',\n\tnsubE: '\\u2AC5\\u0338',\n\tnsube: '\\u2288',\n\tnsubset: '\\u2282\\u20D2',\n\tnsubseteq: '\\u2288',\n\tnsubseteqq: '\\u2AC5\\u0338',\n\tnsucc: '\\u2281',\n\tnsucceq: '\\u2AB0\\u0338',\n\tnsup: '\\u2285',\n\tnsupE: '\\u2AC6\\u0338',\n\tnsupe: '\\u2289',\n\tnsupset: '\\u2283\\u20D2',\n\tnsupseteq: '\\u2289',\n\tnsupseteqq: '\\u2AC6\\u0338',\n\tntgl: '\\u2279',\n\tNtilde: '\\u00D1',\n\tntilde: '\\u00F1',\n\tntlg: '\\u2278',\n\tntriangleleft: '\\u22EA',\n\tntrianglelefteq: '\\u22EC',\n\tntriangleright: '\\u22EB',\n\tntrianglerighteq: '\\u22ED',\n\tNu: '\\u039D',\n\tnu: '\\u03BD',\n\tnum: '\\u0023',\n\tnumero: '\\u2116',\n\tnumsp: '\\u2007',\n\tnvap: '\\u224D\\u20D2',\n\tnVDash: '\\u22AF',\n\tnVdash: '\\u22AE',\n\tnvDash: '\\u22AD',\n\tnvdash: '\\u22AC',\n\tnvge: '\\u2265\\u20D2',\n\tnvgt: '\\u003E\\u20D2',\n\tnvHarr: '\\u2904',\n\tnvinfin: '\\u29DE',\n\tnvlArr: '\\u2902',\n\tnvle: '\\u2264\\u20D2',\n\tnvlt: '\\u003C\\u20D2',\n\tnvltrie: '\\u22B4\\u20D2',\n\tnvrArr: '\\u2903',\n\tnvrtrie: '\\u22B5\\u20D2',\n\tnvsim: '\\u223C\\u20D2',\n\tnwarhk: '\\u2923',\n\tnwArr: '\\u21D6',\n\tnwarr: '\\u2196',\n\tnwarrow: '\\u2196',\n\tnwnear: '\\u2927',\n\tOacute: '\\u00D3',\n\toacute: '\\u00F3',\n\toast: '\\u229B',\n\tocir: '\\u229A',\n\tOcirc: '\\u00D4',\n\tocirc: '\\u00F4',\n\tOcy: '\\u041E',\n\tocy: '\\u043E',\n\todash: '\\u229D',\n\tOdblac: '\\u0150',\n\todblac: '\\u0151',\n\todiv: '\\u2A38',\n\todot: '\\u2299',\n\todsold: '\\u29BC',\n\tOElig: '\\u0152',\n\toelig: '\\u0153',\n\tofcir: '\\u29BF',\n\tOfr: '\\uD835\\uDD12',\n\tofr: '\\uD835\\uDD2C',\n\togon: '\\u02DB',\n\tOgrave: '\\u00D2',\n\tograve: '\\u00F2',\n\togt: '\\u29C1',\n\tohbar: '\\u29B5',\n\tohm: '\\u03A9',\n\toint: '\\u222E',\n\tolarr: '\\u21BA',\n\tolcir: '\\u29BE',\n\tolcross: '\\u29BB',\n\toline: '\\u203E',\n\tolt: '\\u29C0',\n\tOmacr: '\\u014C',\n\tomacr: '\\u014D',\n\tOmega: '\\u03A9',\n\tomega: '\\u03C9',\n\tOmicron: '\\u039F',\n\tomicron: '\\u03BF',\n\tomid: '\\u29B6',\n\tominus: '\\u2296',\n\tOopf: '\\uD835\\uDD46',\n\toopf: '\\uD835\\uDD60',\n\topar: '\\u29B7',\n\tOpenCurlyDoubleQuote: '\\u201C',\n\tOpenCurlyQuote: '\\u2018',\n\toperp: '\\u29B9',\n\toplus: '\\u2295',\n\tOr: '\\u2A54',\n\tor: '\\u2228',\n\torarr: '\\u21BB',\n\tord: '\\u2A5D',\n\torder: '\\u2134',\n\torderof: '\\u2134',\n\tordf: '\\u00AA',\n\tordm: '\\u00BA',\n\torigof: '\\u22B6',\n\toror: '\\u2A56',\n\torslope: '\\u2A57',\n\torv: '\\u2A5B',\n\toS: '\\u24C8',\n\tOscr: '\\uD835\\uDCAA',\n\toscr: '\\u2134',\n\tOslash: '\\u00D8',\n\toslash: '\\u00F8',\n\tosol: '\\u2298',\n\tOtilde: '\\u00D5',\n\totilde: '\\u00F5',\n\tOtimes: '\\u2A37',\n\totimes: '\\u2297',\n\totimesas: '\\u2A36',\n\tOuml: '\\u00D6',\n\touml: '\\u00F6',\n\tovbar: '\\u233D',\n\tOverBar: '\\u203E',\n\tOverBrace: '\\u23DE',\n\tOverBracket: '\\u23B4',\n\tOverParenthesis: '\\u23DC',\n\tpar: '\\u2225',\n\tpara: '\\u00B6',\n\tparallel: '\\u2225',\n\tparsim: '\\u2AF3',\n\tparsl: '\\u2AFD',\n\tpart: '\\u2202',\n\tPartialD: '\\u2202',\n\tPcy: '\\u041F',\n\tpcy: '\\u043F',\n\tpercnt: '\\u0025',\n\tperiod: '\\u002E',\n\tpermil: '\\u2030',\n\tperp: '\\u22A5',\n\tpertenk: '\\u2031',\n\tPfr: '\\uD835\\uDD13',\n\tpfr: '\\uD835\\uDD2D',\n\tPhi: '\\u03A6',\n\tphi: '\\u03C6',\n\tphiv: '\\u03D5',\n\tphmmat: '\\u2133',\n\tphone: '\\u260E',\n\tPi: '\\u03A0',\n\tpi: '\\u03C0',\n\tpitchfork: '\\u22D4',\n\tpiv: '\\u03D6',\n\tplanck: '\\u210F',\n\tplanckh: '\\u210E',\n\tplankv: '\\u210F',\n\tplus: '\\u002B',\n\tplusacir: '\\u2A23',\n\tplusb: '\\u229E',\n\tpluscir: '\\u2A22',\n\tplusdo: '\\u2214',\n\tplusdu: '\\u2A25',\n\tpluse: '\\u2A72',\n\tPlusMinus: '\\u00B1',\n\tplusmn: '\\u00B1',\n\tplussim: '\\u2A26',\n\tplustwo: '\\u2A27',\n\tpm: '\\u00B1',\n\tPoincareplane: '\\u210C',\n\tpointint: '\\u2A15',\n\tPopf: '\\u2119',\n\tpopf: '\\uD835\\uDD61',\n\tpound: '\\u00A3',\n\tPr: '\\u2ABB',\n\tpr: '\\u227A',\n\tprap: '\\u2AB7',\n\tprcue: '\\u227C',\n\tprE: '\\u2AB3',\n\tpre: '\\u2AAF',\n\tprec: '\\u227A',\n\tprecapprox: '\\u2AB7',\n\tpreccurlyeq: '\\u227C',\n\tPrecedes: '\\u227A',\n\tPrecedesEqual: '\\u2AAF',\n\tPrecedesSlantEqual: '\\u227C',\n\tPrecedesTilde: '\\u227E',\n\tpreceq: '\\u2AAF',\n\tprecnapprox: '\\u2AB9',\n\tprecneqq: '\\u2AB5',\n\tprecnsim: '\\u22E8',\n\tprecsim: '\\u227E',\n\tPrime: '\\u2033',\n\tprime: '\\u2032',\n\tprimes: '\\u2119',\n\tprnap: '\\u2AB9',\n\tprnE: '\\u2AB5',\n\tprnsim: '\\u22E8',\n\tprod: '\\u220F',\n\tProduct: '\\u220F',\n\tprofalar: '\\u232E',\n\tprofline: '\\u2312',\n\tprofsurf: '\\u2313',\n\tprop: '\\u221D',\n\tProportion: '\\u2237',\n\tProportional: '\\u221D',\n\tpropto: '\\u221D',\n\tprsim: '\\u227E',\n\tprurel: '\\u22B0',\n\tPscr: '\\uD835\\uDCAB',\n\tpscr: '\\uD835\\uDCC5',\n\tPsi: '\\u03A8',\n\tpsi: '\\u03C8',\n\tpuncsp: '\\u2008',\n\tQfr: '\\uD835\\uDD14',\n\tqfr: '\\uD835\\uDD2E',\n\tqint: '\\u2A0C',\n\tQopf: '\\u211A',\n\tqopf: '\\uD835\\uDD62',\n\tqprime: '\\u2057',\n\tQscr: '\\uD835\\uDCAC',\n\tqscr: '\\uD835\\uDCC6',\n\tquaternions: '\\u210D',\n\tquatint: '\\u2A16',\n\tquest: '\\u003F',\n\tquesteq: '\\u225F',\n\tQUOT: '\\u0022',\n\tquot: '\\u0022',\n\trAarr: '\\u21DB',\n\trace: '\\u223D\\u0331',\n\tRacute: '\\u0154',\n\tracute: '\\u0155',\n\tradic: '\\u221A',\n\traemptyv: '\\u29B3',\n\tRang: '\\u27EB',\n\trang: '\\u27E9',\n\trangd: '\\u2992',\n\trange: '\\u29A5',\n\trangle: '\\u27E9',\n\traquo: '\\u00BB',\n\tRarr: '\\u21A0',\n\trArr: '\\u21D2',\n\trarr: '\\u2192',\n\trarrap: '\\u2975',\n\trarrb: '\\u21E5',\n\trarrbfs: '\\u2920',\n\trarrc: '\\u2933',\n\trarrfs: '\\u291E',\n\trarrhk: '\\u21AA',\n\trarrlp: '\\u21AC',\n\trarrpl: '\\u2945',\n\trarrsim: '\\u2974',\n\tRarrtl: '\\u2916',\n\trarrtl: '\\u21A3',\n\trarrw: '\\u219D',\n\trAtail: '\\u291C',\n\tratail: '\\u291A',\n\tratio: '\\u2236',\n\trationals: '\\u211A',\n\tRBarr: '\\u2910',\n\trBarr: '\\u290F',\n\trbarr: '\\u290D',\n\trbbrk: '\\u2773',\n\trbrace: '\\u007D',\n\trbrack: '\\u005D',\n\trbrke: '\\u298C',\n\trbrksld: '\\u298E',\n\trbrkslu: '\\u2990',\n\tRcaron: '\\u0158',\n\trcaron: '\\u0159',\n\tRcedil: '\\u0156',\n\trcedil: '\\u0157',\n\trceil: '\\u2309',\n\trcub: '\\u007D',\n\tRcy: '\\u0420',\n\trcy: '\\u0440',\n\trdca: '\\u2937',\n\trdldhar: '\\u2969',\n\trdquo: '\\u201D',\n\trdquor: '\\u201D',\n\trdsh: '\\u21B3',\n\tRe: '\\u211C',\n\treal: '\\u211C',\n\trealine: '\\u211B',\n\trealpart: '\\u211C',\n\treals: '\\u211D',\n\trect: '\\u25AD',\n\tREG: '\\u00AE',\n\treg: '\\u00AE',\n\tReverseElement: '\\u220B',\n\tReverseEquilibrium: '\\u21CB',\n\tReverseUpEquilibrium: '\\u296F',\n\trfisht: '\\u297D',\n\trfloor: '\\u230B',\n\tRfr: '\\u211C',\n\trfr: '\\uD835\\uDD2F',\n\trHar: '\\u2964',\n\trhard: '\\u21C1',\n\trharu: '\\u21C0',\n\trharul: '\\u296C',\n\tRho: '\\u03A1',\n\trho: '\\u03C1',\n\trhov: '\\u03F1',\n\tRightAngleBracket: '\\u27E9',\n\tRightArrow: '\\u2192',\n\tRightarrow: '\\u21D2',\n\trightarrow: '\\u2192',\n\tRightArrowBar: '\\u21E5',\n\tRightArrowLeftArrow: '\\u21C4',\n\trightarrowtail: '\\u21A3',\n\tRightCeiling: '\\u2309',\n\tRightDoubleBracket: '\\u27E7',\n\tRightDownTeeVector: '\\u295D',\n\tRightDownVector: '\\u21C2',\n\tRightDownVectorBar: '\\u2955',\n\tRightFloor: '\\u230B',\n\trightharpoondown: '\\u21C1',\n\trightharpoonup: '\\u21C0',\n\trightleftarrows: '\\u21C4',\n\trightleftharpoons: '\\u21CC',\n\trightrightarrows: '\\u21C9',\n\trightsquigarrow: '\\u219D',\n\tRightTee: '\\u22A2',\n\tRightTeeArrow: '\\u21A6',\n\tRightTeeVector: '\\u295B',\n\trightthreetimes: '\\u22CC',\n\tRightTriangle: '\\u22B3',\n\tRightTriangleBar: '\\u29D0',\n\tRightTriangleEqual: '\\u22B5',\n\tRightUpDownVector: '\\u294F',\n\tRightUpTeeVector: '\\u295C',\n\tRightUpVector: '\\u21BE',\n\tRightUpVectorBar: '\\u2954',\n\tRightVector: '\\u21C0',\n\tRightVectorBar: '\\u2953',\n\tring: '\\u02DA',\n\trisingdotseq: '\\u2253',\n\trlarr: '\\u21C4',\n\trlhar: '\\u21CC',\n\trlm: '\\u200F',\n\trmoust: '\\u23B1',\n\trmoustache: '\\u23B1',\n\trnmid: '\\u2AEE',\n\troang: '\\u27ED',\n\troarr: '\\u21FE',\n\trobrk: '\\u27E7',\n\tropar: '\\u2986',\n\tRopf: '\\u211D',\n\tropf: '\\uD835\\uDD63',\n\troplus: '\\u2A2E',\n\trotimes: '\\u2A35',\n\tRoundImplies: '\\u2970',\n\trpar: '\\u0029',\n\trpargt: '\\u2994',\n\trppolint: '\\u2A12',\n\trrarr: '\\u21C9',\n\tRrightarrow: '\\u21DB',\n\trsaquo: '\\u203A',\n\tRscr: '\\u211B',\n\trscr: '\\uD835\\uDCC7',\n\tRsh: '\\u21B1',\n\trsh: '\\u21B1',\n\trsqb: '\\u005D',\n\trsquo: '\\u2019',\n\trsquor: '\\u2019',\n\trthree: '\\u22CC',\n\trtimes: '\\u22CA',\n\trtri: '\\u25B9',\n\trtrie: '\\u22B5',\n\trtrif: '\\u25B8',\n\trtriltri: '\\u29CE',\n\tRuleDelayed: '\\u29F4',\n\truluhar: '\\u2968',\n\trx: '\\u211E',\n\tSacute: '\\u015A',\n\tsacute: '\\u015B',\n\tsbquo: '\\u201A',\n\tSc: '\\u2ABC',\n\tsc: '\\u227B',\n\tscap: '\\u2AB8',\n\tScaron: '\\u0160',\n\tscaron: '\\u0161',\n\tsccue: '\\u227D',\n\tscE: '\\u2AB4',\n\tsce: '\\u2AB0',\n\tScedil: '\\u015E',\n\tscedil: '\\u015F',\n\tScirc: '\\u015C',\n\tscirc: '\\u015D',\n\tscnap: '\\u2ABA',\n\tscnE: '\\u2AB6',\n\tscnsim: '\\u22E9',\n\tscpolint: '\\u2A13',\n\tscsim: '\\u227F',\n\tScy: '\\u0421',\n\tscy: '\\u0441',\n\tsdot: '\\u22C5',\n\tsdotb: '\\u22A1',\n\tsdote: '\\u2A66',\n\tsearhk: '\\u2925',\n\tseArr: '\\u21D8',\n\tsearr: '\\u2198',\n\tsearrow: '\\u2198',\n\tsect: '\\u00A7',\n\tsemi: '\\u003B',\n\tseswar: '\\u2929',\n\tsetminus: '\\u2216',\n\tsetmn: '\\u2216',\n\tsext: '\\u2736',\n\tSfr: '\\uD835\\uDD16',\n\tsfr: '\\uD835\\uDD30',\n\tsfrown: '\\u2322',\n\tsharp: '\\u266F',\n\tSHCHcy: '\\u0429',\n\tshchcy: '\\u0449',\n\tSHcy: '\\u0428',\n\tshcy: '\\u0448',\n\tShortDownArrow: '\\u2193',\n\tShortLeftArrow: '\\u2190',\n\tshortmid: '\\u2223',\n\tshortparallel: '\\u2225',\n\tShortRightArrow: '\\u2192',\n\tShortUpArrow: '\\u2191',\n\tshy: '\\u00AD',\n\tSigma: '\\u03A3',\n\tsigma: '\\u03C3',\n\tsigmaf: '\\u03C2',\n\tsigmav: '\\u03C2',\n\tsim: '\\u223C',\n\tsimdot: '\\u2A6A',\n\tsime: '\\u2243',\n\tsimeq: '\\u2243',\n\tsimg: '\\u2A9E',\n\tsimgE: '\\u2AA0',\n\tsiml: '\\u2A9D',\n\tsimlE: '\\u2A9F',\n\tsimne: '\\u2246',\n\tsimplus: '\\u2A24',\n\tsimrarr: '\\u2972',\n\tslarr: '\\u2190',\n\tSmallCircle: '\\u2218',\n\tsmallsetminus: '\\u2216',\n\tsmashp: '\\u2A33',\n\tsmeparsl: '\\u29E4',\n\tsmid: '\\u2223',\n\tsmile: '\\u2323',\n\tsmt: '\\u2AAA',\n\tsmte: '\\u2AAC',\n\tsmtes: '\\u2AAC\\uFE00',\n\tSOFTcy: '\\u042C',\n\tsoftcy: '\\u044C',\n\tsol: '\\u002F',\n\tsolb: '\\u29C4',\n\tsolbar: '\\u233F',\n\tSopf: '\\uD835\\uDD4A',\n\tsopf: '\\uD835\\uDD64',\n\tspades: '\\u2660',\n\tspadesuit: '\\u2660',\n\tspar: '\\u2225',\n\tsqcap: '\\u2293',\n\tsqcaps: '\\u2293\\uFE00',\n\tsqcup: '\\u2294',\n\tsqcups: '\\u2294\\uFE00',\n\tSqrt: '\\u221A',\n\tsqsub: '\\u228F',\n\tsqsube: '\\u2291',\n\tsqsubset: '\\u228F',\n\tsqsubseteq: '\\u2291',\n\tsqsup: '\\u2290',\n\tsqsupe: '\\u2292',\n\tsqsupset: '\\u2290',\n\tsqsupseteq: '\\u2292',\n\tsqu: '\\u25A1',\n\tSquare: '\\u25A1',\n\tsquare: '\\u25A1',\n\tSquareIntersection: '\\u2293',\n\tSquareSubset: '\\u228F',\n\tSquareSubsetEqual: '\\u2291',\n\tSquareSuperset: '\\u2290',\n\tSquareSupersetEqual: '\\u2292',\n\tSquareUnion: '\\u2294',\n\tsquarf: '\\u25AA',\n\tsquf: '\\u25AA',\n\tsrarr: '\\u2192',\n\tSscr: '\\uD835\\uDCAE',\n\tsscr: '\\uD835\\uDCC8',\n\tssetmn: '\\u2216',\n\tssmile: '\\u2323',\n\tsstarf: '\\u22C6',\n\tStar: '\\u22C6',\n\tstar: '\\u2606',\n\tstarf: '\\u2605',\n\tstraightepsilon: '\\u03F5',\n\tstraightphi: '\\u03D5',\n\tstrns: '\\u00AF',\n\tSub: '\\u22D0',\n\tsub: '\\u2282',\n\tsubdot: '\\u2ABD',\n\tsubE: '\\u2AC5',\n\tsube: '\\u2286',\n\tsubedot: '\\u2AC3',\n\tsubmult: '\\u2AC1',\n\tsubnE: '\\u2ACB',\n\tsubne: '\\u228A',\n\tsubplus: '\\u2ABF',\n\tsubrarr: '\\u2979',\n\tSubset: '\\u22D0',\n\tsubset: '\\u2282',\n\tsubseteq: '\\u2286',\n\tsubseteqq: '\\u2AC5',\n\tSubsetEqual: '\\u2286',\n\tsubsetneq: '\\u228A',\n\tsubsetneqq: '\\u2ACB',\n\tsubsim: '\\u2AC7',\n\tsubsub: '\\u2AD5',\n\tsubsup: '\\u2AD3',\n\tsucc: '\\u227B',\n\tsuccapprox: '\\u2AB8',\n\tsucccurlyeq: '\\u227D',\n\tSucceeds: '\\u227B',\n\tSucceedsEqual: '\\u2AB0',\n\tSucceedsSlantEqual: '\\u227D',\n\tSucceedsTilde: '\\u227F',\n\tsucceq: '\\u2AB0',\n\tsuccnapprox: '\\u2ABA',\n\tsuccneqq: '\\u2AB6',\n\tsuccnsim: '\\u22E9',\n\tsuccsim: '\\u227F',\n\tSuchThat: '\\u220B',\n\tSum: '\\u2211',\n\tsum: '\\u2211',\n\tsung: '\\u266A',\n\tSup: '\\u22D1',\n\tsup: '\\u2283',\n\tsup1: '\\u00B9',\n\tsup2: '\\u00B2',\n\tsup3: '\\u00B3',\n\tsupdot: '\\u2ABE',\n\tsupdsub: '\\u2AD8',\n\tsupE: '\\u2AC6',\n\tsupe: '\\u2287',\n\tsupedot: '\\u2AC4',\n\tSuperset: '\\u2283',\n\tSupersetEqual: '\\u2287',\n\tsuphsol: '\\u27C9',\n\tsuphsub: '\\u2AD7',\n\tsuplarr: '\\u297B',\n\tsupmult: '\\u2AC2',\n\tsupnE: '\\u2ACC',\n\tsupne: '\\u228B',\n\tsupplus: '\\u2AC0',\n\tSupset: '\\u22D1',\n\tsupset: '\\u2283',\n\tsupseteq: '\\u2287',\n\tsupseteqq: '\\u2AC6',\n\tsupsetneq: '\\u228B',\n\tsupsetneqq: '\\u2ACC',\n\tsupsim: '\\u2AC8',\n\tsupsub: '\\u2AD4',\n\tsupsup: '\\u2AD6',\n\tswarhk: '\\u2926',\n\tswArr: '\\u21D9',\n\tswarr: '\\u2199',\n\tswarrow: '\\u2199',\n\tswnwar: '\\u292A',\n\tszlig: '\\u00DF',\n\tTab: '\\u0009',\n\ttarget: '\\u2316',\n\tTau: '\\u03A4',\n\ttau: '\\u03C4',\n\ttbrk: '\\u23B4',\n\tTcaron: '\\u0164',\n\ttcaron: '\\u0165',\n\tTcedil: '\\u0162',\n\ttcedil: '\\u0163',\n\tTcy: '\\u0422',\n\ttcy: '\\u0442',\n\ttdot: '\\u20DB',\n\ttelrec: '\\u2315',\n\tTfr: '\\uD835\\uDD17',\n\ttfr: '\\uD835\\uDD31',\n\tthere4: '\\u2234',\n\tTherefore: '\\u2234',\n\ttherefore: '\\u2234',\n\tTheta: '\\u0398',\n\ttheta: '\\u03B8',\n\tthetasym: '\\u03D1',\n\tthetav: '\\u03D1',\n\tthickapprox: '\\u2248',\n\tthicksim: '\\u223C',\n\tThickSpace: '\\u205F\\u200A',\n\tthinsp: '\\u2009',\n\tThinSpace: '\\u2009',\n\tthkap: '\\u2248',\n\tthksim: '\\u223C',\n\tTHORN: '\\u00DE',\n\tthorn: '\\u00FE',\n\tTilde: '\\u223C',\n\ttilde: '\\u02DC',\n\tTildeEqual: '\\u2243',\n\tTildeFullEqual: '\\u2245',\n\tTildeTilde: '\\u2248',\n\ttimes: '\\u00D7',\n\ttimesb: '\\u22A0',\n\ttimesbar: '\\u2A31',\n\ttimesd: '\\u2A30',\n\ttint: '\\u222D',\n\ttoea: '\\u2928',\n\ttop: '\\u22A4',\n\ttopbot: '\\u2336',\n\ttopcir: '\\u2AF1',\n\tTopf: '\\uD835\\uDD4B',\n\ttopf: '\\uD835\\uDD65',\n\ttopfork: '\\u2ADA',\n\ttosa: '\\u2929',\n\ttprime: '\\u2034',\n\tTRADE: '\\u2122',\n\ttrade: '\\u2122',\n\ttriangle: '\\u25B5',\n\ttriangledown: '\\u25BF',\n\ttriangleleft: '\\u25C3',\n\ttrianglelefteq: '\\u22B4',\n\ttriangleq: '\\u225C',\n\ttriangleright: '\\u25B9',\n\ttrianglerighteq: '\\u22B5',\n\ttridot: '\\u25EC',\n\ttrie: '\\u225C',\n\ttriminus: '\\u2A3A',\n\tTripleDot: '\\u20DB',\n\ttriplus: '\\u2A39',\n\ttrisb: '\\u29CD',\n\ttritime: '\\u2A3B',\n\ttrpezium: '\\u23E2',\n\tTscr: '\\uD835\\uDCAF',\n\ttscr: '\\uD835\\uDCC9',\n\tTScy: '\\u0426',\n\ttscy: '\\u0446',\n\tTSHcy: '\\u040B',\n\ttshcy: '\\u045B',\n\tTstrok: '\\u0166',\n\ttstrok: '\\u0167',\n\ttwixt: '\\u226C',\n\ttwoheadleftarrow: '\\u219E',\n\ttwoheadrightarrow: '\\u21A0',\n\tUacute: '\\u00DA',\n\tuacute: '\\u00FA',\n\tUarr: '\\u219F',\n\tuArr: '\\u21D1',\n\tuarr: '\\u2191',\n\tUarrocir: '\\u2949',\n\tUbrcy: '\\u040E',\n\tubrcy: '\\u045E',\n\tUbreve: '\\u016C',\n\tubreve: '\\u016D',\n\tUcirc: '\\u00DB',\n\tucirc: '\\u00FB',\n\tUcy: '\\u0423',\n\tucy: '\\u0443',\n\tudarr: '\\u21C5',\n\tUdblac: '\\u0170',\n\tudblac: '\\u0171',\n\tudhar: '\\u296E',\n\tufisht: '\\u297E',\n\tUfr: '\\uD835\\uDD18',\n\tufr: '\\uD835\\uDD32',\n\tUgrave: '\\u00D9',\n\tugrave: '\\u00F9',\n\tuHar: '\\u2963',\n\tuharl: '\\u21BF',\n\tuharr: '\\u21BE',\n\tuhblk: '\\u2580',\n\tulcorn: '\\u231C',\n\tulcorner: '\\u231C',\n\tulcrop: '\\u230F',\n\tultri: '\\u25F8',\n\tUmacr: '\\u016A',\n\tumacr: '\\u016B',\n\tuml: '\\u00A8',\n\tUnderBar: '\\u005F',\n\tUnderBrace: '\\u23DF',\n\tUnderBracket: '\\u23B5',\n\tUnderParenthesis: '\\u23DD',\n\tUnion: '\\u22C3',\n\tUnionPlus: '\\u228E',\n\tUogon: '\\u0172',\n\tuogon: '\\u0173',\n\tUopf: '\\uD835\\uDD4C',\n\tuopf: '\\uD835\\uDD66',\n\tUpArrow: '\\u2191',\n\tUparrow: '\\u21D1',\n\tuparrow: '\\u2191',\n\tUpArrowBar: '\\u2912',\n\tUpArrowDownArrow: '\\u21C5',\n\tUpDownArrow: '\\u2195',\n\tUpdownarrow: '\\u21D5',\n\tupdownarrow: '\\u2195',\n\tUpEquilibrium: '\\u296E',\n\tupharpoonleft: '\\u21BF',\n\tupharpoonright: '\\u21BE',\n\tuplus: '\\u228E',\n\tUpperLeftArrow: '\\u2196',\n\tUpperRightArrow: '\\u2197',\n\tUpsi: '\\u03D2',\n\tupsi: '\\u03C5',\n\tupsih: '\\u03D2',\n\tUpsilon: '\\u03A5',\n\tupsilon: '\\u03C5',\n\tUpTee: '\\u22A5',\n\tUpTeeArrow: '\\u21A5',\n\tupuparrows: '\\u21C8',\n\turcorn: '\\u231D',\n\turcorner: '\\u231D',\n\turcrop: '\\u230E',\n\tUring: '\\u016E',\n\turing: '\\u016F',\n\turtri: '\\u25F9',\n\tUscr: '\\uD835\\uDCB0',\n\tuscr: '\\uD835\\uDCCA',\n\tutdot: '\\u22F0',\n\tUtilde: '\\u0168',\n\tutilde: '\\u0169',\n\tutri: '\\u25B5',\n\tutrif: '\\u25B4',\n\tuuarr: '\\u21C8',\n\tUuml: '\\u00DC',\n\tuuml: '\\u00FC',\n\tuwangle: '\\u29A7',\n\tvangrt: '\\u299C',\n\tvarepsilon: '\\u03F5',\n\tvarkappa: '\\u03F0',\n\tvarnothing: '\\u2205',\n\tvarphi: '\\u03D5',\n\tvarpi: '\\u03D6',\n\tvarpropto: '\\u221D',\n\tvArr: '\\u21D5',\n\tvarr: '\\u2195',\n\tvarrho: '\\u03F1',\n\tvarsigma: '\\u03C2',\n\tvarsubsetneq: '\\u228A\\uFE00',\n\tvarsubsetneqq: '\\u2ACB\\uFE00',\n\tvarsupsetneq: '\\u228B\\uFE00',\n\tvarsupsetneqq: '\\u2ACC\\uFE00',\n\tvartheta: '\\u03D1',\n\tvartriangleleft: '\\u22B2',\n\tvartriangleright: '\\u22B3',\n\tVbar: '\\u2AEB',\n\tvBar: '\\u2AE8',\n\tvBarv: '\\u2AE9',\n\tVcy: '\\u0412',\n\tvcy: '\\u0432',\n\tVDash: '\\u22AB',\n\tVdash: '\\u22A9',\n\tvDash: '\\u22A8',\n\tvdash: '\\u22A2',\n\tVdashl: '\\u2AE6',\n\tVee: '\\u22C1',\n\tvee: '\\u2228',\n\tveebar: '\\u22BB',\n\tveeeq: '\\u225A',\n\tvellip: '\\u22EE',\n\tVerbar: '\\u2016',\n\tverbar: '\\u007C',\n\tVert: '\\u2016',\n\tvert: '\\u007C',\n\tVerticalBar: '\\u2223',\n\tVerticalLine: '\\u007C',\n\tVerticalSeparator: '\\u2758',\n\tVerticalTilde: '\\u2240',\n\tVeryThinSpace: '\\u200A',\n\tVfr: '\\uD835\\uDD19',\n\tvfr: '\\uD835\\uDD33',\n\tvltri: '\\u22B2',\n\tvnsub: '\\u2282\\u20D2',\n\tvnsup: '\\u2283\\u20D2',\n\tVopf: '\\uD835\\uDD4D',\n\tvopf: '\\uD835\\uDD67',\n\tvprop: '\\u221D',\n\tvrtri: '\\u22B3',\n\tVscr: '\\uD835\\uDCB1',\n\tvscr: '\\uD835\\uDCCB',\n\tvsubnE: '\\u2ACB\\uFE00',\n\tvsubne: '\\u228A\\uFE00',\n\tvsupnE: '\\u2ACC\\uFE00',\n\tvsupne: '\\u228B\\uFE00',\n\tVvdash: '\\u22AA',\n\tvzigzag: '\\u299A',\n\tWcirc: '\\u0174',\n\twcirc: '\\u0175',\n\twedbar: '\\u2A5F',\n\tWedge: '\\u22C0',\n\twedge: '\\u2227',\n\twedgeq: '\\u2259',\n\tweierp: '\\u2118',\n\tWfr: '\\uD835\\uDD1A',\n\twfr: '\\uD835\\uDD34',\n\tWopf: '\\uD835\\uDD4E',\n\twopf: '\\uD835\\uDD68',\n\twp: '\\u2118',\n\twr: '\\u2240',\n\twreath: '\\u2240',\n\tWscr: '\\uD835\\uDCB2',\n\twscr: '\\uD835\\uDCCC',\n\txcap: '\\u22C2',\n\txcirc: '\\u25EF',\n\txcup: '\\u22C3',\n\txdtri: '\\u25BD',\n\tXfr: '\\uD835\\uDD1B',\n\txfr: '\\uD835\\uDD35',\n\txhArr: '\\u27FA',\n\txharr: '\\u27F7',\n\tXi: '\\u039E',\n\txi: '\\u03BE',\n\txlArr: '\\u27F8',\n\txlarr: '\\u27F5',\n\txmap: '\\u27FC',\n\txnis: '\\u22FB',\n\txodot: '\\u2A00',\n\tXopf: '\\uD835\\uDD4F',\n\txopf: '\\uD835\\uDD69',\n\txoplus: '\\u2A01',\n\txotime: '\\u2A02',\n\txrArr: '\\u27F9',\n\txrarr: '\\u27F6',\n\tXscr: '\\uD835\\uDCB3',\n\txscr: '\\uD835\\uDCCD',\n\txsqcup: '\\u2A06',\n\txuplus: '\\u2A04',\n\txutri: '\\u25B3',\n\txvee: '\\u22C1',\n\txwedge: '\\u22C0',\n\tYacute: '\\u00DD',\n\tyacute: '\\u00FD',\n\tYAcy: '\\u042F',\n\tyacy: '\\u044F',\n\tYcirc: '\\u0176',\n\tycirc: '\\u0177',\n\tYcy: '\\u042B',\n\tycy: '\\u044B',\n\tyen: '\\u00A5',\n\tYfr: '\\uD835\\uDD1C',\n\tyfr: '\\uD835\\uDD36',\n\tYIcy: '\\u0407',\n\tyicy: '\\u0457',\n\tYopf: '\\uD835\\uDD50',\n\tyopf: '\\uD835\\uDD6A',\n\tYscr: '\\uD835\\uDCB4',\n\tyscr: '\\uD835\\uDCCE',\n\tYUcy: '\\u042E',\n\tyucy: '\\u044E',\n\tYuml: '\\u0178',\n\tyuml: '\\u00FF',\n\tZacute: '\\u0179',\n\tzacute: '\\u017A',\n\tZcaron: '\\u017D',\n\tzcaron: '\\u017E',\n\tZcy: '\\u0417',\n\tzcy: '\\u0437',\n\tZdot: '\\u017B',\n\tzdot: '\\u017C',\n\tzeetrf: '\\u2128',\n\tZeroWidthSpace: '\\u200B',\n\tZeta: '\\u0396',\n\tzeta: '\\u03B6',\n\tZfr: '\\u2128',\n\tzfr: '\\uD835\\uDD37',\n\tZHcy: '\\u0416',\n\tzhcy: '\\u0436',\n\tzigrarr: '\\u21DD',\n\tZopf: '\\u2124',\n\tzopf: '\\uD835\\uDD6B',\n\tZscr: '\\uD835\\uDCB5',\n\tzscr: '\\uD835\\uDCCF',\n\tzwj: '\\u200D',\n\tzwnj: '\\u200C',\n});\n\n/**\n * @deprecated use `HTML_ENTITIES` instead\n * @see HTML_ENTITIES\n */\nexports.entityMap = exports.HTML_ENTITIES;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,SAAS,8GAAyB,MAAM;AAE5C;;;;;;CAMC,GACD,QAAQ,YAAY,GAAG,OAAO;IAC7B,KAAK;IACL,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,MAAM;AACP;AAEA;;;;;;;;;;;;;CAaC,GACD,QAAQ,aAAa,GAAG,OAAO;IAC9B,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,QAAQ;IACR,MAAM;IACN,UAAU;IACV,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,UAAU;IACV,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,IAAI;IACJ,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,eAAe;IACf,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,UAAU;IACV,OAAO;IACP,UAAU;IACV,aAAa;IACb,WAAW;IACX,SAAS;IACT,WAAW;IACX,WAAW;IACX,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,MAAM;IACN,UAAU;IACV,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,MAAM;IACN,MAAM;IACN,MAAM;IACN,SAAS;IACT,KAAK;IACL,KAAK;IACL,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,WAAW;IACX,UAAU;IACV,SAAS;IACT,iBAAiB;IACjB,eAAe;IACf,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,cAAc;IACd,aAAa;IACb,eAAe;IACf,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;IACpB,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,UAAU;IACV,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,sBAAsB;IACtB,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;IACN,WAAW;IACX,WAAW;IACX,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,WAAW;IACX,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;IACZ,aAAa;IACb,aAAa;IACb,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,YAAY;IACZ,aAAa;IACb,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,SAAS;IACT,0BAA0B;IAC1B,uBAAuB;IACvB,iBAAiB;IACjB,OAAO;IACP,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,MAAM;IACN,SAAS;IACT,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,iBAAiB;IACjB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,iCAAiC;IACjC,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,KAAK;IACL,KAAK;IACL,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,SAAS;IACT,aAAa;IACb,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,gBAAgB;IAChB,iBAAiB;IACjB,OAAO;IACP,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,OAAO;IACP,UAAU;IACV,SAAS;IACT,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,SAAS;IACT,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,kBAAkB;IAClB,gBAAgB;IAChB,wBAAwB;IACxB,kBAAkB;IAClB,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,KAAK;IACL,eAAe;IACf,SAAS;IACT,OAAO;IACP,KAAK;IACL,QAAQ;IACR,eAAe;IACf,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,WAAW;IACX,gBAAgB;IAChB,uBAAuB;IACvB,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,sBAAsB;IACtB,eAAe;IACf,qBAAqB;IACrB,0BAA0B;IAC1B,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,mBAAmB;IACnB,WAAW;IACX,WAAW;IACX,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,oBAAoB;IACpB,iBAAiB;IACjB,oBAAoB;IACpB,SAAS;IACT,cAAc;IACd,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,OAAO;IACP,KAAK;IACL,KAAK;IACL,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,IAAI;IACJ,SAAS;IACT,UAAU;IACV,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,QAAQ;IACR,sBAAsB;IACtB,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,SAAS;IACT,OAAO;IACP,YAAY;IACZ,aAAa;IACb,OAAO;IACP,QAAQ;IACR,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,OAAO;IACP,SAAS;IACT,UAAU;IACV,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,aAAa;IACb,cAAc;IACd,cAAc;IACd,eAAe;IACf,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,OAAO;IACP,mBAAmB;IACnB,uBAAuB;IACvB,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,cAAc;IACd,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,cAAc;IACd,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,WAAW;IACX,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,cAAc;IACd,UAAU;IACV,UAAU;IACV,OAAO;IACP,QAAQ;IACR,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,cAAc;IACd,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,YAAY;IACZ,UAAU;IACV,UAAU;IACV,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,UAAU;IACV,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,UAAU;IACV,UAAU;IACV,UAAU;IACV,cAAc;IACd,UAAU;IACV,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,YAAY;IACZ,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,WAAW;IACX,cAAc;IACd,qBAAqB;IACrB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,WAAW;IACX,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,qBAAqB;IACrB,iBAAiB;IACjB,SAAS;IACT,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,iBAAiB;IACjB,YAAY;IACZ,eAAe;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,UAAU;IACV,KAAK;IACL,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,aAAa;IACb,SAAS;IACT,UAAU;IACV,SAAS;IACT,gBAAgB;IAChB,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,MAAM;IACN,UAAU;IACV,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,eAAe;IACf,eAAe;IACf,eAAe;IACf,oBAAoB;IACpB,oBAAoB;IACpB,oBAAoB;IACpB,YAAY;IACZ,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,iBAAiB;IACjB,KAAK;IACL,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,UAAU;IACV,OAAO;IACP,QAAQ;IACR,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,SAAS;IACT,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,SAAS;IACT,KAAK;IACL,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,eAAe;IACf,aAAa;IACb,WAAW;IACX,KAAK;IACL,KAAK;IACL,KAAK;IACL,OAAO;IACP,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,IAAI;IACJ,MAAM;IACN,MAAM;IACN,QAAQ;IACR,IAAI;IACJ,IAAI;IACJ,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,UAAU;IACV,MAAM;IACN,OAAO;IACP,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,KAAK;IACL,KAAK;IACL,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,qBAAqB;IACrB,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,sBAAsB;IACtB,gBAAgB;IAChB,SAAS;IACT,QAAQ;IACR,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,WAAW;IACX,MAAM;IACN,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,IAAI;IACJ,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,YAAY;IACZ,YAAY;IACZ,iBAAiB;IACjB,iBAAiB;IACjB,MAAM;IACN,OAAO;IACP,WAAW;IACX,MAAM;IACN,OAAO;IACP,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,kBAAkB;IAClB,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,cAAc;IACd,WAAW;IACX,sBAAsB;IACtB,YAAY;IACZ,UAAU;IACV,eAAe;IACf,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,sBAAsB;IACtB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,OAAO;IACP,UAAU;IACV,QAAQ;IACR,SAAS;IACT,SAAS;IACT,SAAS;IACT,iBAAiB;IACjB,oBAAoB;IACpB,sBAAsB;IACtB,SAAS;IACT,cAAc;IACd,gBAAgB;IAChB,aAAa;IACb,mBAAmB;IACnB,cAAc;IACd,yBAAyB;IACzB,mBAAmB;IACnB,OAAO;IACP,SAAS;IACT,SAAS;IACT,SAAS;IACT,aAAa;IACb,kBAAkB;IAClB,uBAAuB;IACvB,mBAAmB;IACnB,kBAAkB;IAClB,qBAAqB;IACrB,uBAAuB;IACvB,iBAAiB;IACjB,sBAAsB;IACtB,mBAAmB;IACnB,wBAAwB;IACxB,WAAW;IACX,gBAAgB;IAChB,aAAa;IACb,kBAAkB;IAClB,uBAAuB;IACvB,kBAAkB;IAClB,aAAa;IACb,kBAAkB;IAClB,UAAU;IACV,eAAe;IACf,mBAAmB;IACnB,eAAe;IACf,gBAAgB;IAChB,MAAM;IACN,WAAW;IACX,QAAQ;IACR,OAAO;IACP,SAAS;IACT,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,aAAa;IACb,OAAO;IACP,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,WAAW;IACX,gBAAgB;IAChB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,YAAY;IACZ,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,gBAAgB;IAChB,kBAAkB;IAClB,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,SAAS;IACT,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,OAAO;IACP,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,sBAAsB;IACtB,gBAAgB;IAChB,OAAO;IACP,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,KAAK;IACL,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,SAAS;IACT,KAAK;IACL,IAAI;IACJ,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,WAAW;IACX,aAAa;IACb,iBAAiB;IACjB,KAAK;IACL,MAAM;IACN,UAAU;IACV,QAAQ;IACR,OAAO;IACP,MAAM;IACN,UAAU;IACV,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,KAAK;IACL,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,MAAM;IACN,UAAU;IACV,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,QAAQ;IACR,SAAS;IACT,SAAS;IACT,IAAI;IACJ,eAAe;IACf,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,YAAY;IACZ,aAAa;IACb,UAAU;IACV,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,MAAM;IACN,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,MAAM;IACN,YAAY;IACZ,cAAc;IACd,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,aAAa;IACb,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,SAAS;IACT,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,WAAW;IACX,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,MAAM;IACN,SAAS;IACT,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,gBAAgB;IAChB,oBAAoB;IACpB,sBAAsB;IACtB,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,mBAAmB;IACnB,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,qBAAqB;IACrB,gBAAgB;IAChB,cAAc;IACd,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,oBAAoB;IACpB,YAAY;IACZ,kBAAkB;IAClB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,UAAU;IACV,eAAe;IACf,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,kBAAkB;IAClB,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,gBAAgB;IAChB,MAAM;IACN,cAAc;IACd,OAAO;IACP,OAAO;IACP,KAAK;IACL,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,SAAS;IACT,cAAc;IACd,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,aAAa;IACb,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,aAAa;IACb,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,KAAK;IACL,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,OAAO;IACP,MAAM;IACN,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,gBAAgB;IAChB,gBAAgB;IAChB,UAAU;IACV,eAAe;IACf,iBAAiB;IACjB,cAAc;IACd,KAAK;IACL,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,aAAa;IACb,eAAe;IACf,QAAQ;IACR,UAAU;IACV,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;IACR,WAAW;IACX,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,UAAU;IACV,YAAY;IACZ,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,oBAAoB;IACpB,cAAc;IACd,mBAAmB;IACnB,gBAAgB;IAChB,qBAAqB;IACrB,aAAa;IACb,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,iBAAiB;IACjB,aAAa;IACb,OAAO;IACP,KAAK;IACL,KAAK;IACL,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,WAAW;IACX,aAAa;IACb,WAAW;IACX,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,YAAY;IACZ,aAAa;IACb,UAAU;IACV,eAAe;IACf,oBAAoB;IACpB,eAAe;IACf,QAAQ;IACR,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,SAAS;IACT,MAAM;IACN,MAAM;IACN,SAAS;IACT,UAAU;IACV,eAAe;IACf,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;IACP,OAAO;IACP,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,WAAW;IACX,WAAW;IACX,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,SAAS;IACT,QAAQ;IACR,OAAO;IACP,KAAK;IACL,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,WAAW;IACX,WAAW;IACX,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;IACR,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,YAAY;IACZ,gBAAgB;IAChB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IACV,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,WAAW;IACX,eAAe;IACf,iBAAiB;IACjB,QAAQ;IACR,MAAM;IACN,UAAU;IACV,WAAW;IACX,SAAS;IACT,OAAO;IACP,SAAS;IACT,UAAU;IACV,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,kBAAkB;IAClB,mBAAmB;IACnB,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,UAAU;IACV,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,KAAK;IACL,UAAU;IACV,YAAY;IACZ,cAAc;IACd,kBAAkB;IAClB,OAAO;IACP,WAAW;IACX,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS;IACT,YAAY;IACZ,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,aAAa;IACb,eAAe;IACf,eAAe;IACf,gBAAgB;IAChB,OAAO;IACP,gBAAgB;IAChB,iBAAiB;IACjB,MAAM;IACN,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,UAAU;IACV,cAAc;IACd,eAAe;IACf,cAAc;IACd,eAAe;IACf,UAAU;IACV,iBAAiB;IACjB,kBAAkB;IAClB,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,KAAK;IACL,KAAK;IACL,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,aAAa;IACb,cAAc;IACd,mBAAmB;IACnB,eAAe;IACf,eAAe;IACf,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,QAAQ;IACR,gBAAgB;IAChB,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;AACP;AAEA;;;CAGC,GACD,QAAQ,SAAS,GAAG,QAAQ,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/sax.js"], "sourcesContent": ["var NAMESPACE = require(\"./conventions\").NAMESPACE;\n\n//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0;//tag name offerring\nvar S_ATTR = 1;//attr name offerring\nvar S_ATTR_SPACE=2;//attr name end and space offer\nvar S_EQ = 3;//=space?\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7;//closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n\tthis.message = message\n\tthis.locator = locator\n\tif(Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name\n\nfunction XMLReader(){\n\n}\n\nXMLReader.prototype = {\n\tparse:function(source,defaultNSMap,entityMap){\n\t\tvar domBuilder = this.domBuilder;\n\t\tdomBuilder.startDocument();\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\n\t\tparse(source,defaultNSMap,entityMap,\n\t\t\t\tdomBuilder,this.errorHandler);\n\t\tdomBuilder.endDocument();\n\t}\n}\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\n\tfunction fixedFromCharCode(code) {\n\t\t// String.prototype.fromCharCode does not supports\n\t\t// > 2 bytes unicode chars directly\n\t\tif (code > 0xffff) {\n\t\t\tcode -= 0x10000;\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\n\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\n\t\t} else {\n\t\t\treturn String.fromCharCode(code);\n\t\t}\n\t}\n\tfunction entityReplacer(a){\n\t\tvar k = a.slice(1,-1);\n\t\tif (Object.hasOwnProperty.call(entityMap, k)) {\n\t\t\treturn entityMap[k];\n\t\t}else if(k.charAt(0) === '#'){\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\n\t\t}else{\n\t\t\terrorHandler.error('entity not found:'+a);\n\t\t\treturn a;\n\t\t}\n\t}\n\tfunction appendText(end){//has some bugs\n\t\tif(end>start){\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\tlocator&&position(start);\n\t\t\tdomBuilder.characters(xt,0,end-start);\n\t\t\tstart = end\n\t\t}\n\t}\n\tfunction position(p,m){\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\n\t\t\tlineStart = m.index;\n\t\t\tlineEnd = lineStart + m[0].length;\n\t\t\tlocator.lineNumber++;\n\t\t\t//console.log('line++:',locator,startPos,endPos)\n\t\t}\n\t\tlocator.columnNumber = p-lineStart+1;\n\t}\n\tvar lineStart = 0;\n\tvar lineEnd = 0;\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\n\tvar locator = domBuilder.locator;\n\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\n\tvar closeMap = {};\n\tvar start = 0;\n\twhile(true){\n\t\ttry{\n\t\t\tvar tagStart = source.indexOf('<',start);\n\t\t\tif(tagStart<0){\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\n\t\t\t\t\tvar doc = domBuilder.doc;\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\n\t    \t\t\tdoc.appendChild(text);\n\t    \t\t\tdomBuilder.currentElement = text;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(tagStart>start){\n\t\t\t\tappendText(tagStart);\n\t\t\t}\n\t\t\tswitch(source.charAt(tagStart+1)){\n\t\t\tcase '/':\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\n\t\t\t\tvar tagName = source.substring(tagStart + 2, end).replace(/[ \\t\\n\\r]+$/g, '');\n\t\t\t\tvar config = parseStack.pop();\n\t\t\t\tif(end<0){\n\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\n\t        \t\tend = tagStart+1+tagName.length;\n\t        \t}else if(tagName.match(/\\s</)){\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\n\t        \t\tend = tagStart+1+tagName.length;\n\t\t\t\t}\n\t\t\t\tvar localNSMap = config.localNSMap;\n\t\t\t\tvar endMatch = config.tagName == tagName;\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\n\t\t        if(endIgnoreCaseMach){\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\n\t\t\t\t\tif(localNSMap){\n\t\t\t\t\t\tfor (var prefix in localNSMap) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(!endMatch){\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName ); // No known test case\n\t\t\t\t\t}\n\t\t        }else{\n\t\t        \tparseStack.push(config)\n\t\t        }\n\n\t\t\t\tend++;\n\t\t\t\tbreak;\n\t\t\t\t// end elment\n\t\t\tcase '?':// <?...?>\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\n\t\t\t\tbreak;\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tvar el = new ElementAttributes();\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\t\t\t\t//elStartEnd\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\n\t\t\t\tvar len = el.length;\n\n\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\n\t\t\t\t\tel.closed = true;\n\t\t\t\t\tif(!entityMap.nbsp){\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(locator && len){\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\n\t\t\t\t\t//try{//attribute position fixed\n\t\t\t\t\tfor(var i = 0;i<len;i++){\n\t\t\t\t\t\tvar a = el[i];\n\t\t\t\t\t\tposition(a.offset);\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator2\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator;\n\t\t\t\t}else{\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (NAMESPACE.isHTML(el.uri) && !el.closed) {\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\n\t\t\t\t} else {\n\t\t\t\t\tend++;\n\t\t\t\t}\n\t\t\t}\n\t\t}catch(e){\n\t\t\tif (e instanceof ParseError) {\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t\terrorHandler.error('element parse error: '+e)\n\t\t\tend = -1;\n\t\t}\n\t\tif(end>start){\n\t\t\tstart = end;\n\t\t}else{\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\n\t\t\tappendText(Math.max(tagStart,start)+1);\n\t\t}\n\t}\n}\nfunction copyLocator(f,t){\n\tt.lineNumber = f.lineNumber;\n\tt.columnNumber = f.columnNumber;\n\treturn t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\n\n\t/**\n\t * @param {string} qname\n\t * @param {string} value\n\t * @param {number} startIndex\n\t */\n\tfunction addAttribute(qname, value, startIndex) {\n\t\tif (el.attributeNames.hasOwnProperty(qname)) {\n\t\t\terrorHandler.fatalError('Attribute ' + qname + ' redefined')\n\t\t}\n\t\tel.addValue(\n\t\t\tqname,\n\t\t\t// @see https://www.w3.org/TR/xml/#AVNormalize\n\t\t\t// since the xmldom sax parser does not \"interpret\" DTD the following is not implemented:\n\t\t\t// - recursive replacement of (DTD) entity references\n\t\t\t// - trimming and collapsing multiple spaces into a single one for attributes that are not of type CDATA\n\t\t\tvalue.replace(/[\\t\\n\\r]/g, ' ').replace(/&#?\\w+;/g, entityReplacer),\n\t\t\tstartIndex\n\t\t)\n\t}\n\tvar attrName;\n\tvar value;\n\tvar p = ++start;\n\tvar s = S_TAG;//status\n\twhile(true){\n\t\tvar c = source.charAt(p);\n\t\tswitch(c){\n\t\tcase '=':\n\t\t\tif(s === S_ATTR){//attrName\n\t\t\t\tattrName = source.slice(start,p);\n\t\t\t\ts = S_EQ;\n\t\t\t}else if(s === S_ATTR_SPACE){\n\t\t\t\ts = S_EQ;\n\t\t\t}else{\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\n\t\t\t\tthrow new Error('attribute equal must after attrName'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '\\'':\n\t\tcase '\"':\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n\t\t\t\t){//equal\n\t\t\t\tif(s === S_ATTR){\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t}\n\t\t\t\tstart = p+1;\n\t\t\t\tp = source.indexOf(c,start)\n\t\t\t\tif(p>0){\n\t\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\t\taddAttribute(attrName, value, start-1);\n\t\t\t\t\ts = S_ATTR_END;\n\t\t\t\t}else{\n\t\t\t\t\t//fatalError: no end quot match\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\n\t\t\t\t}\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\taddAttribute(attrName, value, start);\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\n\t\t\t\tstart = p+1;\n\t\t\t\ts = S_ATTR_END\n\t\t\t}else{\n\t\t\t\t//fatalError: no equal before\n\t\t\t\tthrow new Error('attribute value must after \"=\"'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '/':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\ts =S_TAG_CLOSE;\n\t\t\t\tel.closed = true;\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\tcase S_ATTR:\n\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tel.closed = true;\n\t\t\t\tbreak;\n\t\t\t//case S_EQ:\n\t\t\tdefault:\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\") // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase ''://end document\n\t\t\terrorHandler.error('unexpected end of input');\n\t\t\tif(s == S_TAG){\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\t}\n\t\t\treturn p;\n\t\tcase '>':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\tbreak;//normal\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\n\t\t\tcase S_ATTR:\n\t\t\t\tvalue = source.slice(start,p);\n\t\t\t\tif(value.slice(-1) === '/'){\n\t\t\t\t\tel.closed  = true;\n\t\t\t\t\tvalue = value.slice(0,-1)\n\t\t\t\t}\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tif(s === S_ATTR_SPACE){\n\t\t\t\t\tvalue = attrName;\n\t\t\t\t}\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\t}else{\n\t\t\t\t\tif(!NAMESPACE.isHTML(currentNSMap['']) || !value.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(value, value, start)\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase S_EQ:\n\t\t\t\tthrow new Error('attribute value missed!!');\n\t\t\t}\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n\t\t\treturn p;\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\n\t\tcase '\\u0080':\n\t\t\tc = ' ';\n\t\tdefault:\n\t\t\tif(c<= ' '){//space\n\t\t\t\tswitch(s){\n\t\t\t\tcase S_TAG:\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR:\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t\ts = S_ATTR_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\t\t\tvar value = source.slice(start, p);\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\t//case S_TAG_SPACE:\n\t\t\t\t//case S_EQ:\n\t\t\t\t//case S_ATTR_SPACE:\n\t\t\t\t//\tvoid();break;\n\t\t\t\t//case S_TAG_CLOSE:\n\t\t\t\t\t//ignore warning\n\t\t\t\t}\n\t\t\t}else{//not space\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n\t\t\t\tswitch(s){\n\t\t\t\t//case S_TAG:void();break;\n\t\t\t\t//case S_ATTR:void();break;\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tvar tagName =  el.tagName;\n\t\t\t\t\tif (!NAMESPACE.isHTML(currentNSMap['']) || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(attrName, attrName, start);\n\t\t\t\t\tstart = p;\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\n\t\t\t\tcase S_TAG_SPACE:\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_EQ:\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_TAG_CLOSE:\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\n\t\t\t\t}\n\t\t\t}\n\t\t}//end outer switch\n\t\t//console.log('p++',p)\n\t\tp++;\n\t}\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el,domBuilder,currentNSMap){\n\tvar tagName = el.tagName;\n\tvar localNSMap = null;\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\tvar i = el.length;\n\twhile(i--){\n\t\tvar a = el[i];\n\t\tvar qName = a.qName;\n\t\tvar value = a.value;\n\t\tvar nsp = qName.indexOf(':');\n\t\tif(nsp>0){\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\n\t\t\tvar localName = qName.slice(nsp+1);\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\n\t\t}else{\n\t\t\tlocalName = qName;\n\t\t\tprefix = null\n\t\t\tnsPrefix = qName === 'xmlns' && ''\n\t\t}\n\t\t//can not set prefix,because prefix !== ''\n\t\ta.localName = localName ;\n\t\t//prefix == null for no ns prefix attribute\n\t\tif(nsPrefix !== false){//hack!!\n\t\t\tif(localNSMap == null){\n\t\t\t\tlocalNSMap = {}\n\t\t\t\t//console.log(currentNSMap,0)\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\n\t\t\t\t//console.log(currentNSMap,1)\n\t\t\t}\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n\t\t\ta.uri = NAMESPACE.XMLNS\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value)\n\t\t}\n\t}\n\tvar i = el.length;\n\twhile(i--){\n\t\ta = el[i];\n\t\tvar prefix = a.prefix;\n\t\tif(prefix){//no prefix attribute has no namespace\n\t\t\tif(prefix === 'xml'){\n\t\t\t\ta.uri = NAMESPACE.XML;\n\t\t\t}if(prefix !== 'xmlns'){\n\t\t\t\ta.uri = currentNSMap[prefix || '']\n\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n\t\t\t}\n\t\t}\n\t}\n\tvar nsp = tagName.indexOf(':');\n\tif(nsp>0){\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\n\t}else{\n\t\tprefix = null;//important!!\n\t\tlocalName = el.localName = tagName;\n\t}\n\t//no prefix element has default namespace\n\tvar ns = el.uri = currentNSMap[prefix || ''];\n\tdomBuilder.startElement(ns,localName,tagName,el);\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\n\t//localNSMap = null\n\tif(el.closed){\n\t\tdomBuilder.endElement(ns,localName,tagName);\n\t\tif(localNSMap){\n\t\t\tfor (prefix in localNSMap) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}else{\n\t\tel.currentNSMap = currentNSMap;\n\t\tel.localNSMap = localNSMap;\n\t\t//parseStack.push(el);\n\t\treturn true;\n\t}\n}\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\n\tif(/^(?:script|textarea)$/i.test(tagName)){\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\n\t\tif(/[&<]/.test(text)){\n\t\t\tif(/^script$/i.test(tagName)){\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\n\t\t\t\t\t//lexHandler.startCDATA();\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\t\t//lexHandler.endCDATA();\n\t\t\t\t\treturn elEndStart;\n\t\t\t\t//}\n\t\t\t}//}else{//text area\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\treturn elEndStart;\n\t\t\t//}\n\n\t\t}\n\t}\n\treturn elStartEnd+1;\n}\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\n\t//if(tagName in closeMap){\n\tvar pos = closeMap[tagName];\n\tif(pos == null){\n\t\t//console.log(tagName)\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\n\t\tif(pos<elStartEnd){//忘记闭合\n\t\t\tpos = source.lastIndexOf('</'+tagName)\n\t\t}\n\t\tcloseMap[tagName] =pos\n\t}\n\treturn pos<elStartEnd;\n\t//}\n}\n\nfunction _copy (source, target) {\n\tfor (var n in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, n)) {\n\t\t\ttarget[n] = source[n];\n\t\t}\n\t}\n}\n\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\n\tvar next= source.charAt(start+2)\n\tswitch(next){\n\tcase '-':\n\t\tif(source.charAt(start + 3) === '-'){\n\t\t\tvar end = source.indexOf('-->',start+4);\n\t\t\t//append comment source.substring(4,end)//<!--\n\t\t\tif(end>start){\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\n\t\t\t\treturn end+3;\n\t\t\t}else{\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}else{\n\t\t\t//error\n\t\t\treturn -1;\n\t\t}\n\tdefault:\n\t\tif(source.substr(start+3,6) == 'CDATA['){\n\t\t\tvar end = source.indexOf(']]>',start+9);\n\t\t\tdomBuilder.startCDATA();\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\n\t\t\tdomBuilder.endCDATA()\n\t\t\treturn end+3;\n\t\t}\n\t\t//<!DOCTYPE\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId)\n\t\tvar matchs = split(source,start);\n\t\tvar len = matchs.length;\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\n\t\t\tvar name = matchs[1][0];\n\t\t\tvar pubid = false;\n\t\t\tvar sysid = false;\n\t\t\tif(len>3){\n\t\t\t\tif(/^public$/i.test(matchs[2][0])){\n\t\t\t\t\tpubid = matchs[3][0];\n\t\t\t\t\tsysid = len>4 && matchs[4][0];\n\t\t\t\t}else if(/^system$/i.test(matchs[2][0])){\n\t\t\t\t\tsysid = matchs[3][0];\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar lastMatch = matchs[len-1]\n\t\t\tdomBuilder.startDTD(name, pubid, sysid);\n\t\t\tdomBuilder.endDTD();\n\n\t\t\treturn lastMatch.index+lastMatch[0].length\n\t\t}\n\t}\n\treturn -1;\n}\n\n\n\nfunction parseInstruction(source,start,domBuilder){\n\tvar end = source.indexOf('?>',start);\n\tif(end){\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n\t\tif(match){\n\t\t\tvar len = match[0].length;\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\n\t\t\treturn end+2;\n\t\t}else{//error\n\t\t\treturn -1;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction ElementAttributes(){\n\tthis.attributeNames = {}\n}\nElementAttributes.prototype = {\n\tsetTagName:function(tagName){\n\t\tif(!tagNamePattern.test(tagName)){\n\t\t\tthrow new Error('invalid tagName:'+tagName)\n\t\t}\n\t\tthis.tagName = tagName\n\t},\n\taddValue:function(qName, value, offset) {\n\t\tif(!tagNamePattern.test(qName)){\n\t\t\tthrow new Error('invalid attribute:'+qName)\n\t\t}\n\t\tthis.attributeNames[qName] = this.length;\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\n\t},\n\tlength:0,\n\tgetLocalName:function(i){return this[i].localName},\n\tgetLocator:function(i){return this[i].locator},\n\tgetQName:function(i){return this[i].qName},\n\tgetURI:function(i){return this[i].uri},\n\tgetValue:function(i){return this[i].value}\n//\t,getIndex:function(uri, localName)){\n//\t\tif(localName){\n//\n//\t\t}else{\n//\t\t\tvar qName = uri\n//\t\t}\n//\t},\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n//\tgetType:function(uri,localName){}\n//\tgetType:function(i){},\n}\n\n\n\nfunction split(source,start){\n\tvar match;\n\tvar buf = [];\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n\treg.lastIndex = start;\n\treg.exec(source);//skip <\n\twhile(match = reg.exec(source)){\n\t\tbuf.push(match);\n\t\tif(match[1])return buf;\n\t}\n}\n\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;\n"], "names": [], "mappings": "AAAA,IAAI,YAAY,8GAAyB,SAAS;AAElD,sQAAsQ;AACtQ,yGAAyG;AACzG,iDAAiD;AACjD,IAAI,gBAAgB,mJAAkJ,iBAAiB;;AACvL,IAAI,WAAW,IAAI,OAAO,eAAa,cAAc,MAAM,CAAC,KAAK,CAAC,GAAE,CAAC,KAAG;AACxE,IAAI,iBAAiB,IAAI,OAAO,MAAI,cAAc,MAAM,GAAC,SAAS,MAAM,GAAC,WAAS,cAAc,MAAM,GAAC,SAAS,MAAM,GAAC;AACvH,sEAAsE;AACtE,oaAAoa;AAEpa,0CAA0C;AAC1C,oDAAoD;AACpD,IAAI,QAAQ,GAAE,oBAAoB;AAClC,IAAI,SAAS,GAAE,qBAAqB;AACpC,IAAI,eAAa,GAAE,+BAA+B;AAClD,IAAI,OAAO,GAAE,SAAS;AACtB,IAAI,sBAAsB,GAAE,gCAAgC;AAC5D,IAAI,aAAa,GAAE,uCAAuC;AAC1D,IAAI,cAAc,GAAE,+CAA+C;AACnE,IAAI,cAAc,GAAE,iBAAiB;AAErC;;;;;;CAMC,GACD,SAAS,WAAW,OAAO,EAAE,OAAO;IACnC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,OAAO,GAAG;IACf,IAAG,MAAM,iBAAiB,EAAE,MAAM,iBAAiB,CAAC,IAAI,EAAE;AAC3D;AACA,WAAW,SAAS,GAAG,IAAI;AAC3B,WAAW,SAAS,CAAC,IAAI,GAAG,WAAW,IAAI;AAE3C,SAAS,aAET;AAEA,UAAU,SAAS,GAAG;IACrB,OAAM,SAAS,MAAM,EAAC,YAAY,EAAC,SAAS;QAC3C,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,WAAW,aAAa;QACxB,MAAM,cAAc,eAAe,CAAC;QACpC,MAAM,QAAO,cAAa,WACxB,YAAW,IAAI,CAAC,YAAY;QAC9B,WAAW,WAAW;IACvB;AACD;AACA,SAAS,MAAM,MAAM,EAAC,gBAAgB,EAAC,SAAS,EAAC,UAAU,EAAC,YAAY;IACvE,SAAS,kBAAkB,IAAI;QAC9B,kDAAkD;QAClD,mCAAmC;QACnC,IAAI,OAAO,QAAQ;YAClB,QAAQ;YACR,IAAI,aAAa,SAAS,CAAC,QAAQ,EAAE,GAClC,aAAa,SAAS,CAAC,OAAO,KAAK;YAEtC,OAAO,OAAO,YAAY,CAAC,YAAY;QACxC,OAAO;YACN,OAAO,OAAO,YAAY,CAAC;QAC5B;IACD;IACA,SAAS,eAAe,CAAC;QACxB,IAAI,IAAI,EAAE,KAAK,CAAC,GAAE,CAAC;QACnB,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,WAAW,IAAI;YAC7C,OAAO,SAAS,CAAC,EAAE;QACpB,OAAM,IAAG,EAAE,MAAM,CAAC,OAAO,KAAI;YAC5B,OAAO,kBAAkB,SAAS,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,KAAI;QAC3D,OAAK;YACJ,aAAa,KAAK,CAAC,sBAAoB;YACvC,OAAO;QACR;IACD;IACA,SAAS,WAAW,GAAG;QACtB,IAAG,MAAI,OAAM;YACZ,IAAI,KAAK,OAAO,SAAS,CAAC,OAAM,KAAK,OAAO,CAAC,YAAW;YACxD,WAAS,SAAS;YAClB,WAAW,UAAU,CAAC,IAAG,GAAE,MAAI;YAC/B,QAAQ;QACT;IACD;IACA,SAAS,SAAS,CAAC,EAAC,CAAC;QACpB,MAAM,KAAG,WAAW,CAAC,IAAI,YAAY,IAAI,CAAC,OAAO,EAAE;YAClD,YAAY,EAAE,KAAK;YACnB,UAAU,YAAY,CAAC,CAAC,EAAE,CAAC,MAAM;YACjC,QAAQ,UAAU;QAClB,gDAAgD;QACjD;QACA,QAAQ,YAAY,GAAG,IAAE,YAAU;IACpC;IACA,IAAI,YAAY;IAChB,IAAI,UAAU;IACd,IAAI,cAAc;IAClB,IAAI,UAAU,WAAW,OAAO;IAEhC,IAAI,aAAa;QAAC;YAAC,cAAa;QAAgB;KAAE;IAClD,IAAI,WAAW,CAAC;IAChB,IAAI,QAAQ;IACZ,MAAM,KAAK;QACV,IAAG;YACF,IAAI,WAAW,OAAO,OAAO,CAAC,KAAI;YAClC,IAAG,WAAS,GAAE;gBACb,IAAG,CAAC,OAAO,MAAM,CAAC,OAAO,KAAK,CAAC,UAAS;oBACvC,IAAI,MAAM,WAAW,GAAG;oBACrB,IAAI,OAAO,IAAI,cAAc,CAAC,OAAO,MAAM,CAAC;oBAC5C,IAAI,WAAW,CAAC;oBAChB,WAAW,cAAc,GAAG;gBAChC;gBACA;YACD;YACA,IAAG,WAAS,OAAM;gBACjB,WAAW;YACZ;YACA,OAAO,OAAO,MAAM,CAAC,WAAS;gBAC9B,KAAK;oBACJ,IAAI,MAAM,OAAO,OAAO,CAAC,KAAI,WAAS;oBACtC,IAAI,UAAU,OAAO,SAAS,CAAC,WAAW,GAAG,KAAK,OAAO,CAAC,gBAAgB;oBAC1E,IAAI,SAAS,WAAW,GAAG;oBAC3B,IAAG,MAAI,GAAE;wBAEF,UAAU,OAAO,SAAS,CAAC,WAAS,GAAG,OAAO,CAAC,WAAU;wBACzD,aAAa,KAAK,CAAC,mBAAiB,UAAQ,sBAAoB,OAAO,OAAO;wBAC9E,MAAM,WAAS,IAAE,QAAQ,MAAM;oBAChC,OAAM,IAAG,QAAQ,KAAK,CAAC,QAAO;wBAC7B,UAAU,QAAQ,OAAO,CAAC,WAAU;wBACpC,aAAa,KAAK,CAAC,mBAAiB,UAAQ;wBAC5C,MAAM,WAAS,IAAE,QAAQ,MAAM;oBACtC;oBACA,IAAI,aAAa,OAAO,UAAU;oBAClC,IAAI,WAAW,OAAO,OAAO,IAAI;oBACjC,IAAI,oBAAoB,YAAY,OAAO,OAAO,IAAE,OAAO,OAAO,CAAC,WAAW,MAAM,QAAQ,WAAW;oBACjG,IAAG,mBAAkB;wBACpB,WAAW,UAAU,CAAC,OAAO,GAAG,EAAC,OAAO,SAAS,EAAC;wBACxD,IAAG,YAAW;4BACb,IAAK,IAAI,UAAU,WAAY;gCAC9B,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,SAAS;oCAC7D,WAAW,gBAAgB,CAAC;gCAC7B;4BACD;wBACD;wBACA,IAAG,CAAC,UAAS;4BACH,aAAa,UAAU,CAAC,mBAAiB,UAAQ,6CAA2C,OAAO,OAAO,GAAI,qBAAqB;wBAC7I;oBACK,OAAK;wBACJ,WAAW,IAAI,CAAC;oBACjB;oBAEN;oBACA;gBACA,aAAa;gBACd,KAAK;oBACJ,WAAS,SAAS;oBAClB,MAAM,iBAAiB,QAAO,UAAS;oBACvC;gBACD,KAAK;oBACJ,WAAS,SAAS;oBAClB,MAAM,SAAS,QAAO,UAAS,YAAW;oBAC1C;gBACD;oBACC,WAAS,SAAS;oBAClB,IAAI,KAAK,IAAI;oBACb,IAAI,eAAe,UAAU,CAAC,WAAW,MAAM,GAAC,EAAE,CAAC,YAAY;oBAC/D,YAAY;oBACZ,IAAI,MAAM,sBAAsB,QAAO,UAAS,IAAG,cAAa,gBAAe;oBAC/E,IAAI,MAAM,GAAG,MAAM;oBAGnB,IAAG,CAAC,GAAG,MAAM,IAAI,cAAc,QAAO,KAAI,GAAG,OAAO,EAAC,WAAU;wBAC9D,GAAG,MAAM,GAAG;wBACZ,IAAG,CAAC,UAAU,IAAI,EAAC;4BAClB,aAAa,OAAO,CAAC;wBACtB;oBACD;oBACA,IAAG,WAAW,KAAI;wBACjB,IAAI,WAAW,YAAY,SAAQ,CAAC;wBACpC,gCAAgC;wBAChC,IAAI,IAAI,IAAI,GAAE,IAAE,KAAI,IAAI;4BACvB,IAAI,IAAI,EAAE,CAAC,EAAE;4BACb,SAAS,EAAE,MAAM;4BACjB,EAAE,OAAO,GAAG,YAAY,SAAQ,CAAC;wBAClC;wBACA,WAAW,OAAO,GAAG;wBACrB,IAAG,cAAc,IAAG,YAAW,eAAc;4BAC5C,WAAW,IAAI,CAAC;wBACjB;wBACA,WAAW,OAAO,GAAG;oBACtB,OAAK;wBACJ,IAAG,cAAc,IAAG,YAAW,eAAc;4BAC5C,WAAW,IAAI,CAAC;wBACjB;oBACD;oBAEA,IAAI,UAAU,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,MAAM,EAAE;wBAC3C,MAAM,wBAAwB,QAAO,KAAI,GAAG,OAAO,EAAC,gBAAe;oBACpE,OAAO;wBACN;oBACD;YACD;QACD,EAAC,OAAM,GAAE;YACR,IAAI,aAAa,YAAY;gBAC5B,MAAM;YACP;YACA,aAAa,KAAK,CAAC,0BAAwB;YAC3C,MAAM,CAAC;QACR;QACA,IAAG,MAAI,OAAM;YACZ,QAAQ;QACT,OAAK;YACJ,0BAA0B;YAC1B,WAAW,KAAK,GAAG,CAAC,UAAS,SAAO;QACrC;IACD;AACD;AACA,SAAS,YAAY,CAAC,EAAC,CAAC;IACvB,EAAE,UAAU,GAAG,EAAE,UAAU;IAC3B,EAAE,YAAY,GAAG,EAAE,YAAY;IAC/B,OAAO;AACR;AAEA;;;CAGC,GACD,SAAS,sBAAsB,MAAM,EAAC,KAAK,EAAC,EAAE,EAAC,YAAY,EAAC,cAAc,EAAC,YAAY;IAEtF;;;;EAIC,GACD,SAAS,aAAa,KAAK,EAAE,KAAK,EAAE,UAAU;QAC7C,IAAI,GAAG,cAAc,CAAC,cAAc,CAAC,QAAQ;YAC5C,aAAa,UAAU,CAAC,eAAe,QAAQ;QAChD;QACA,GAAG,QAAQ,CACV,OACA,8CAA8C;QAC9C,yFAAyF;QACzF,qDAAqD;QACrD,wGAAwG;QACxG,MAAM,OAAO,CAAC,aAAa,KAAK,OAAO,CAAC,YAAY,iBACpD;IAEF;IACA,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,EAAE;IACV,IAAI,IAAI,OAAM,QAAQ;IACtB,MAAM,KAAK;QACV,IAAI,IAAI,OAAO,MAAM,CAAC;QACtB,OAAO;YACP,KAAK;gBACJ,IAAG,MAAM,QAAO;oBACf,WAAW,OAAO,KAAK,CAAC,OAAM;oBAC9B,IAAI;gBACL,OAAM,IAAG,MAAM,cAAa;oBAC3B,IAAI;gBACL,OAAK;oBACJ,+DAA+D;oBAC/D,MAAM,IAAI,MAAM,wCAAwC,qBAAqB;gBAC9E;gBACA;YACD,KAAK;YACL,KAAK;gBACJ,IAAG,MAAM,QAAQ,MAAM,OAAO,sBAAsB;kBAClD;oBACD,IAAG,MAAM,QAAO;wBACf,aAAa,OAAO,CAAC;wBACrB,WAAW,OAAO,KAAK,CAAC,OAAM;oBAC/B;oBACA,QAAQ,IAAE;oBACV,IAAI,OAAO,OAAO,CAAC,GAAE;oBACrB,IAAG,IAAE,GAAE;wBACN,QAAQ,OAAO,KAAK,CAAC,OAAO;wBAC5B,aAAa,UAAU,OAAO,QAAM;wBACpC,IAAI;oBACL,OAAK;wBACJ,+BAA+B;wBAC/B,MAAM,IAAI,MAAM,8BAA4B,IAAE;oBAC/C;gBACD,OAAM,IAAG,KAAK,qBAAoB;oBACjC,QAAQ,OAAO,KAAK,CAAC,OAAO;oBAC5B,aAAa,UAAU,OAAO;oBAC9B,aAAa,OAAO,CAAC,gBAAc,WAAS,yBAAuB,IAAE;oBACrE,QAAQ,IAAE;oBACV,IAAI;gBACL,OAAK;oBACJ,6BAA6B;oBAC7B,MAAM,IAAI,MAAM,mCAAmC,qBAAqB;gBACzE;gBACA;YACD,KAAK;gBACJ,OAAO;oBACP,KAAK;wBACJ,GAAG,UAAU,CAAC,OAAO,KAAK,CAAC,OAAM;oBAClC,KAAK;oBACL,KAAK;oBACL,KAAK;wBACJ,IAAG;wBACH,GAAG,MAAM,GAAG;oBACb,KAAK;oBACL,KAAK;wBACJ;oBACA,KAAK;wBACJ,GAAG,MAAM,GAAG;wBACb;oBACD,YAAY;oBACZ;wBACC,MAAM,IAAI,MAAM,qCAAqC,qBAAqB;;gBAC3E;gBACA;YACD,KAAK;gBACJ,aAAa,KAAK,CAAC;gBACnB,IAAG,KAAK,OAAM;oBACb,GAAG,UAAU,CAAC,OAAO,KAAK,CAAC,OAAM;gBAClC;gBACA,OAAO;YACR,KAAK;gBACJ,OAAO;oBACP,KAAK;wBACJ,GAAG,UAAU,CAAC,OAAO,KAAK,CAAC,OAAM;oBAClC,KAAK;oBACL,KAAK;oBACL,KAAK;wBACJ,OAAM,QAAQ;oBACf,KAAK;oBACL,KAAK;wBACJ,QAAQ,OAAO,KAAK,CAAC,OAAM;wBAC3B,IAAG,MAAM,KAAK,CAAC,CAAC,OAAO,KAAI;4BAC1B,GAAG,MAAM,GAAI;4BACb,QAAQ,MAAM,KAAK,CAAC,GAAE,CAAC;wBACxB;oBACD,KAAK;wBACJ,IAAG,MAAM,cAAa;4BACrB,QAAQ;wBACT;wBACA,IAAG,KAAK,qBAAoB;4BAC3B,aAAa,OAAO,CAAC,gBAAc,QAAM;4BACzC,aAAa,UAAU,OAAO;wBAC/B,OAAK;4BACJ,IAAG,CAAC,UAAU,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,qCAAoC;gCAC1F,aAAa,OAAO,CAAC,gBAAc,QAAM,uBAAqB,QAAM;4BACrE;4BACA,aAAa,OAAO,OAAO;wBAC5B;wBACA;oBACD,KAAK;wBACJ,MAAM,IAAI,MAAM;gBACjB;gBACH,qEAAqE;gBAClE,OAAO;YACR,sCAAsC,GACtC,KAAK;gBACJ,IAAI;YACL;gBACC,IAAG,KAAI,KAAI;oBACV,OAAO;wBACP,KAAK;4BACJ,GAAG,UAAU,CAAC,OAAO,KAAK,CAAC,OAAM,KAAI,SAAS;4BAC9C,IAAI;4BACJ;wBACD,KAAK;4BACJ,WAAW,OAAO,KAAK,CAAC,OAAM;4BAC9B,IAAI;4BACJ;wBACD,KAAK;4BACJ,IAAI,QAAQ,OAAO,KAAK,CAAC,OAAO;4BAChC,aAAa,OAAO,CAAC,gBAAc,QAAM;4BACzC,aAAa,UAAU,OAAO;wBAC/B,KAAK;4BACJ,IAAI;4BACJ;oBAOD;gBACD,OAAK;oBACR,0CAA0C;oBAC1C,oDAAoD;oBAChD,OAAO;wBACP,0BAA0B;wBAC1B,2BAA2B;wBAC3B,wCAAwC;wBACxC,KAAK;4BACJ,IAAI,UAAW,GAAG,OAAO;4BACzB,IAAI,CAAC,UAAU,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,SAAS,KAAK,CAAC,qCAAqC;gCAC/F,aAAa,OAAO,CAAC,gBAAc,WAAS,uBAAqB,WAAS;4BAC3E;4BACA,aAAa,UAAU,UAAU;4BACjC,QAAQ;4BACR,IAAI;4BACJ;wBACD,KAAK;4BACJ,aAAa,OAAO,CAAC,iCAA+B,WAAS;wBAC9D,KAAK;4BACJ,IAAI;4BACJ,QAAQ;4BACR;wBACD,KAAK;4BACJ,IAAI;4BACJ,QAAQ;4BACR;wBACD,KAAK;4BACJ,MAAM,IAAI,MAAM;oBACjB;gBACD;QACD,EAAC,kBAAkB;QACnB,sBAAsB;QACtB;IACD;AACD;AACA;;CAEC,GACD,SAAS,cAAc,EAAE,EAAC,UAAU,EAAC,YAAY;IAChD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,aAAa;IACjB,kEAAkE;IAClE,IAAI,IAAI,GAAG,MAAM;IACjB,MAAM,IAAI;QACT,IAAI,IAAI,EAAE,CAAC,EAAE;QACb,IAAI,QAAQ,EAAE,KAAK;QACnB,IAAI,QAAQ,EAAE,KAAK;QACnB,IAAI,MAAM,MAAM,OAAO,CAAC;QACxB,IAAG,MAAI,GAAE;YACR,IAAI,SAAS,EAAE,MAAM,GAAG,MAAM,KAAK,CAAC,GAAE;YACtC,IAAI,YAAY,MAAM,KAAK,CAAC,MAAI;YAChC,IAAI,WAAW,WAAW,WAAW;QACtC,OAAK;YACJ,YAAY;YACZ,SAAS;YACT,WAAW,UAAU,WAAW;QACjC;QACA,0CAA0C;QAC1C,EAAE,SAAS,GAAG;QACd,2CAA2C;QAC3C,IAAG,aAAa,OAAM;YACrB,IAAG,cAAc,MAAK;gBACrB,aAAa,CAAC;gBACd,6BAA6B;gBAC7B,MAAM,cAAa,eAAa,CAAC;YACjC,6BAA6B;YAC9B;YACA,YAAY,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,GAAG;YAChD,EAAE,GAAG,GAAG,UAAU,KAAK;YACvB,WAAW,kBAAkB,CAAC,UAAU;QACzC;IACD;IACA,IAAI,IAAI,GAAG,MAAM;IACjB,MAAM,IAAI;QACT,IAAI,EAAE,CAAC,EAAE;QACT,IAAI,SAAS,EAAE,MAAM;QACrB,IAAG,QAAO;YACT,IAAG,WAAW,OAAM;gBACnB,EAAE,GAAG,GAAG,UAAU,GAAG;YACtB;YAAC,IAAG,WAAW,SAAQ;gBACtB,EAAE,GAAG,GAAG,YAAY,CAAC,UAAU,GAAG;YAElC,gFAAgF;YACjF;QACD;IACD;IACA,IAAI,MAAM,QAAQ,OAAO,CAAC;IAC1B,IAAG,MAAI,GAAE;QACR,SAAS,GAAG,MAAM,GAAG,QAAQ,KAAK,CAAC,GAAE;QACrC,YAAY,GAAG,SAAS,GAAG,QAAQ,KAAK,CAAC,MAAI;IAC9C,OAAK;QACJ,SAAS,MAAK,aAAa;QAC3B,YAAY,GAAG,SAAS,GAAG;IAC5B;IACA,yCAAyC;IACzC,IAAI,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,UAAU,GAAG;IAC5C,WAAW,YAAY,CAAC,IAAG,WAAU,SAAQ;IAC7C,2EAA2E;IAC3E,mBAAmB;IACnB,IAAG,GAAG,MAAM,EAAC;QACZ,WAAW,UAAU,CAAC,IAAG,WAAU;QACnC,IAAG,YAAW;YACb,IAAK,UAAU,WAAY;gBAC1B,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,SAAS;oBAC7D,WAAW,gBAAgB,CAAC;gBAC7B;YACD;QACD;IACD,OAAK;QACJ,GAAG,YAAY,GAAG;QAClB,GAAG,UAAU,GAAG;QAChB,sBAAsB;QACtB,OAAO;IACR;AACD;AACA,SAAS,wBAAwB,MAAM,EAAC,UAAU,EAAC,OAAO,EAAC,cAAc,EAAC,UAAU;IACnF,IAAG,yBAAyB,IAAI,CAAC,UAAS;QACzC,IAAI,aAAc,OAAO,OAAO,CAAC,OAAK,UAAQ,KAAI;QAClD,IAAI,OAAO,OAAO,SAAS,CAAC,aAAW,GAAE;QACzC,IAAG,OAAO,IAAI,CAAC,OAAM;YACpB,IAAG,YAAY,IAAI,CAAC,UAAS;gBAC5B,0BAA0B;gBACzB,0BAA0B;gBAC1B,WAAW,UAAU,CAAC,MAAK,GAAE,KAAK,MAAM;gBACxC,wBAAwB;gBACxB,OAAO;YACR,GAAG;YACJ,EAAC,mBAAmB;YACnB,OAAO,KAAK,OAAO,CAAC,YAAW;YAC/B,WAAW,UAAU,CAAC,MAAK,GAAE,KAAK,MAAM;YACxC,OAAO;QACR,GAAG;QAEJ;IACD;IACA,OAAO,aAAW;AACnB;AACA,SAAS,cAAc,MAAM,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ;IACxD,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,CAAC,QAAQ;IAC3B,IAAG,OAAO,MAAK;QACd,sBAAsB;QACtB,MAAO,OAAO,WAAW,CAAC,OAAK,UAAQ;QACvC,IAAG,MAAI,YAAW;YACjB,MAAM,OAAO,WAAW,CAAC,OAAK;QAC/B;QACA,QAAQ,CAAC,QAAQ,GAAE;IACpB;IACA,OAAO,MAAI;AACX,GAAG;AACJ;AAEA,SAAS,MAAO,MAAM,EAAE,MAAM;IAC7B,IAAK,IAAI,KAAK,OAAQ;QACrB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,IAAI;YACpD,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;QACtB;IACD;AACD;AAEA,SAAS,SAAS,MAAM,EAAC,KAAK,EAAC,UAAU,EAAC,YAAY;IACrD,IAAI,OAAM,OAAO,MAAM,CAAC,QAAM;IAC9B,OAAO;QACP,KAAK;YACJ,IAAG,OAAO,MAAM,CAAC,QAAQ,OAAO,KAAI;gBACnC,IAAI,MAAM,OAAO,OAAO,CAAC,OAAM,QAAM;gBACrC,8CAA8C;gBAC9C,IAAG,MAAI,OAAM;oBACZ,WAAW,OAAO,CAAC,QAAO,QAAM,GAAE,MAAI,QAAM;oBAC5C,OAAO,MAAI;gBACZ,OAAK;oBACJ,aAAa,KAAK,CAAC;oBACnB,OAAO,CAAC;gBACT;YACD,OAAK;gBACJ,OAAO;gBACP,OAAO,CAAC;YACT;QACD;YACC,IAAG,OAAO,MAAM,CAAC,QAAM,GAAE,MAAM,UAAS;gBACvC,IAAI,MAAM,OAAO,OAAO,CAAC,OAAM,QAAM;gBACrC,WAAW,UAAU;gBACrB,WAAW,UAAU,CAAC,QAAO,QAAM,GAAE,MAAI,QAAM;gBAC/C,WAAW,QAAQ;gBACnB,OAAO,MAAI;YACZ;YACA,WAAW;YACX,uFAAuF;YACvF,IAAI,SAAS,MAAM,QAAO;YAC1B,IAAI,MAAM,OAAO,MAAM;YACvB,IAAG,MAAI,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAE;gBAC1C,IAAI,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;gBACvB,IAAI,QAAQ;gBACZ,IAAI,QAAQ;gBACZ,IAAG,MAAI,GAAE;oBACR,IAAG,YAAY,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAE;wBACjC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE;wBACpB,QAAQ,MAAI,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE;oBAC9B,OAAM,IAAG,YAAY,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAE;wBACvC,QAAQ,MAAM,CAAC,EAAE,CAAC,EAAE;oBACrB;gBACD;gBACA,IAAI,YAAY,MAAM,CAAC,MAAI,EAAE;gBAC7B,WAAW,QAAQ,CAAC,MAAM,OAAO;gBACjC,WAAW,MAAM;gBAEjB,OAAO,UAAU,KAAK,GAAC,SAAS,CAAC,EAAE,CAAC,MAAM;YAC3C;IACD;IACA,OAAO,CAAC;AACT;AAIA,SAAS,iBAAiB,MAAM,EAAC,KAAK,EAAC,UAAU;IAChD,IAAI,MAAM,OAAO,OAAO,CAAC,MAAK;IAC9B,IAAG,KAAI;QACN,IAAI,QAAQ,OAAO,SAAS,CAAC,OAAM,KAAK,KAAK,CAAC;QAC9C,IAAG,OAAM;YACR,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;YACzB,WAAW,qBAAqB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;YACnD,OAAO,MAAI;QACZ,OAAK;YACJ,OAAO,CAAC;QACT;IACD;IACA,OAAO,CAAC;AACT;AAEA,SAAS;IACR,IAAI,CAAC,cAAc,GAAG,CAAC;AACxB;AACA,kBAAkB,SAAS,GAAG;IAC7B,YAAW,SAAS,OAAO;QAC1B,IAAG,CAAC,eAAe,IAAI,CAAC,UAAS;YAChC,MAAM,IAAI,MAAM,qBAAmB;QACpC;QACA,IAAI,CAAC,OAAO,GAAG;IAChB;IACA,UAAS,SAAS,KAAK,EAAE,KAAK,EAAE,MAAM;QACrC,IAAG,CAAC,eAAe,IAAI,CAAC,QAAO;YAC9B,MAAM,IAAI,MAAM,uBAAqB;QACtC;QACA,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YAAC,OAAM;YAAM,OAAM;YAAM,QAAO;QAAM;IAC7D;IACA,QAAO;IACP,cAAa,SAAS,CAAC;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS;IAAA;IACjD,YAAW,SAAS,CAAC;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO;IAAA;IAC7C,UAAS,SAAS,CAAC;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK;IAAA;IACzC,QAAO,SAAS,CAAC;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG;IAAA;IACrC,UAAS,SAAS,CAAC;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK;IAAA;AAW1C;AAIA,SAAS,MAAM,MAAM,EAAC,KAAK;IAC1B,IAAI;IACJ,IAAI,MAAM,EAAE;IACZ,IAAI,MAAM;IACV,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI,CAAC,SAAQ,QAAQ;IACzB,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC9B,IAAI,IAAI,CAAC;QACT,IAAG,KAAK,CAAC,EAAE,EAAC,OAAO;IACpB;AACD;AAEA,QAAQ,SAAS,GAAG;AACpB,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/dom-parser.js"], "sourcesContent": ["var conventions = require(\"./conventions\");\nvar dom = require('./dom')\nvar entities = require('./entities');\nvar sax = require('./sax');\n\nvar DOMImplementation = dom.DOMImplementation;\n\nvar NAMESPACE = conventions.NAMESPACE;\n\nvar ParseError = sax.ParseError;\nvar XMLReader = sax.XMLReader;\n\n/**\n * Normalizes line ending according to https://www.w3.org/TR/xml11/#sec-line-ends:\n *\n * > XML parsed entities are often stored in computer files which,\n * > for editing convenience, are organized into lines.\n * > These lines are typically separated by some combination\n * > of the characters CARRIAGE RETURN (#xD) and LINE FEED (#xA).\n * >\n * > To simplify the tasks of applications, the XML processor must behave\n * > as if it normalized all line breaks in external parsed entities (including the document entity)\n * > on input, before parsing, by translating all of the following to a single #xA character:\n * >\n * > 1. the two-character sequence #xD #xA\n * > 2. the two-character sequence #xD #x85\n * > 3. the single character #x85\n * > 4. the single character #x2028\n * > 5. any #xD character that is not immediately followed by #xA or #x85.\n *\n * @param {string} input\n * @returns {string}\n */\nfunction normalizeLineEndings(input) {\n\treturn input\n\t\t.replace(/\\r[\\n\\u0085]/g, '\\n')\n\t\t.replace(/[\\r\\u0085\\u2028]/g, '\\n')\n}\n\n/**\n * @typedef Locator\n * @property {number} [columnNumber]\n * @property {number} [lineNumber]\n */\n\n/**\n * @typedef DOMParserOptions\n * @property {DOMHandler} [domBuilder]\n * @property {Function} [errorHandler]\n * @property {(string) => string} [normalizeLineEndings] used to replace line endings before parsing\n * \t\t\t\t\t\tdefaults to `normalizeLineEndings`\n * @property {Locator} [locator]\n * @property {Record<string, string>} [xmlns]\n *\n * @see normalizeLineEndings\n */\n\n/**\n * The DOMParser interface provides the ability to parse XML or HTML source code\n * from a string into a DOM `Document`.\n *\n * _xmldom is different from the spec in that it allows an `options` parameter,\n * to override the default behavior._\n *\n * @param {DOMParserOptions} [options]\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-parsing-and-serialization\n */\nfunction DOMParser(options){\n\tthis.options = options ||{locator:{}};\n}\n\nDOMParser.prototype.parseFromString = function(source,mimeType){\n\tvar options = this.options;\n\tvar sax =  new XMLReader();\n\tvar domBuilder = options.domBuilder || new DOMHandler();//contentHandler and LexicalHandler\n\tvar errorHandler = options.errorHandler;\n\tvar locator = options.locator;\n\tvar defaultNSMap = options.xmlns||{};\n\tvar isHTML = /\\/x?html?$/.test(mimeType);//mimeType.toLowerCase().indexOf('html') > -1;\n  \tvar entityMap = isHTML ? entities.HTML_ENTITIES : entities.XML_ENTITIES;\n\tif(locator){\n\t\tdomBuilder.setDocumentLocator(locator)\n\t}\n\n\tsax.errorHandler = buildErrorHandler(errorHandler,domBuilder,locator);\n\tsax.domBuilder = options.domBuilder || domBuilder;\n\tif(isHTML){\n\t\tdefaultNSMap[''] = NAMESPACE.HTML;\n\t}\n\tdefaultNSMap.xml = defaultNSMap.xml || NAMESPACE.XML;\n\tvar normalize = options.normalizeLineEndings || normalizeLineEndings;\n\tif (source && typeof source === 'string') {\n\t\tsax.parse(\n\t\t\tnormalize(source),\n\t\t\tdefaultNSMap,\n\t\t\tentityMap\n\t\t)\n\t} else {\n\t\tsax.errorHandler.error('invalid doc source')\n\t}\n\treturn domBuilder.doc;\n}\nfunction buildErrorHandler(errorImpl,domBuilder,locator){\n\tif(!errorImpl){\n\t\tif(domBuilder instanceof DOMHandler){\n\t\t\treturn domBuilder;\n\t\t}\n\t\terrorImpl = domBuilder ;\n\t}\n\tvar errorHandler = {}\n\tvar isCallback = errorImpl instanceof Function;\n\tlocator = locator||{}\n\tfunction build(key){\n\t\tvar fn = errorImpl[key];\n\t\tif(!fn && isCallback){\n\t\t\tfn = errorImpl.length == 2?function(msg){errorImpl(key,msg)}:errorImpl;\n\t\t}\n\t\terrorHandler[key] = fn && function(msg){\n\t\t\tfn('[xmldom '+key+']\\t'+msg+_locator(locator));\n\t\t}||function(){};\n\t}\n\tbuild('warning');\n\tbuild('error');\n\tbuild('fatalError');\n\treturn errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n    this.cdata = false;\n}\nfunction position(locator,node){\n\tnode.lineNumber = locator.lineNumber;\n\tnode.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n\tstartDocument : function() {\n    \tthis.doc = new DOMImplementation().createDocument(null, null, null);\n    \tif (this.locator) {\n        \tthis.doc.documentURI = this.locator.systemId;\n    \t}\n\t},\n\tstartElement:function(namespaceURI, localName, qName, attrs) {\n\t\tvar doc = this.doc;\n\t    var el = doc.createElementNS(namespaceURI, qName||localName);\n\t    var len = attrs.length;\n\t    appendElement(this, el);\n\t    this.currentElement = el;\n\n\t\tthis.locator && position(this.locator,el)\n\t    for (var i = 0 ; i < len; i++) {\n\t        var namespaceURI = attrs.getURI(i);\n\t        var value = attrs.getValue(i);\n\t        var qName = attrs.getQName(i);\n\t\t\tvar attr = doc.createAttributeNS(namespaceURI, qName);\n\t\t\tthis.locator &&position(attrs.getLocator(i),attr);\n\t\t\tattr.value = attr.nodeValue = value;\n\t\t\tel.setAttributeNode(attr)\n\t    }\n\t},\n\tendElement:function(namespaceURI, localName, qName) {\n\t\tvar current = this.currentElement\n\t\tvar tagName = current.tagName;\n\t\tthis.currentElement = current.parentNode;\n\t},\n\tstartPrefixMapping:function(prefix, uri) {\n\t},\n\tendPrefixMapping:function(prefix) {\n\t},\n\tprocessingInstruction:function(target, data) {\n\t    var ins = this.doc.createProcessingInstruction(target, data);\n\t    this.locator && position(this.locator,ins)\n\t    appendElement(this, ins);\n\t},\n\tignorableWhitespace:function(ch, start, length) {\n\t},\n\tcharacters:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t\t//console.log(chars)\n\t\tif(chars){\n\t\t\tif (this.cdata) {\n\t\t\t\tvar charNode = this.doc.createCDATASection(chars);\n\t\t\t} else {\n\t\t\t\tvar charNode = this.doc.createTextNode(chars);\n\t\t\t}\n\t\t\tif(this.currentElement){\n\t\t\t\tthis.currentElement.appendChild(charNode);\n\t\t\t}else if(/^\\s*$/.test(chars)){\n\t\t\t\tthis.doc.appendChild(charNode);\n\t\t\t\t//process xml\n\t\t\t}\n\t\t\tthis.locator && position(this.locator,charNode)\n\t\t}\n\t},\n\tskippedEntity:function(name) {\n\t},\n\tendDocument:function() {\n\t\tthis.doc.normalize();\n\t},\n\tsetDocumentLocator:function (locator) {\n\t    if(this.locator = locator){// && !('lineNumber' in locator)){\n\t    \tlocator.lineNumber = 0;\n\t    }\n\t},\n\t//LexicalHandler\n\tcomment:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t    var comm = this.doc.createComment(chars);\n\t    this.locator && position(this.locator,comm)\n\t    appendElement(this, comm);\n\t},\n\n\tstartCDATA:function() {\n\t    //used in characters() methods\n\t    this.cdata = true;\n\t},\n\tendCDATA:function() {\n\t    this.cdata = false;\n\t},\n\n\tstartDTD:function(name, publicId, systemId) {\n\t\tvar impl = this.doc.implementation;\n\t    if (impl && impl.createDocumentType) {\n\t        var dt = impl.createDocumentType(name, publicId, systemId);\n\t        this.locator && position(this.locator,dt)\n\t        appendElement(this, dt);\n\t\t\t\t\tthis.doc.doctype = dt;\n\t    }\n\t},\n\t/**\n\t * @see org.xml.sax.ErrorHandler\n\t * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n\t */\n\twarning:function(error) {\n\t\tconsole.warn('[xmldom warning]\\t'+error,_locator(this.locator));\n\t},\n\terror:function(error) {\n\t\tconsole.error('[xmldom error]\\t'+error,_locator(this.locator));\n\t},\n\tfatalError:function(error) {\n\t\tthrow new ParseError(error, this.locator);\n\t}\n}\nfunction _locator(l){\n\tif(l){\n\t\treturn '\\n@'+(l.systemId ||'')+'#[line:'+l.lineNumber+',col:'+l.columnNumber+']'\n\t}\n}\nfunction _toString(chars,start,length){\n\tif(typeof chars == 'string'){\n\t\treturn chars.substr(start,length)\n\t}else{//java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n\t\tif(chars.length >= start+length || start){\n\t\t\treturn new java.lang.String(chars,start,length)+'';\n\t\t}\n\t\treturn chars;\n\t}\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g,function(key){\n\tDOMHandler.prototype[key] = function(){return null}\n})\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement (hander,node) {\n    if (!hander.currentElement) {\n        hander.doc.appendChild(node);\n    } else {\n        hander.currentElement.appendChild(node);\n    }\n}//appendChild and setAttributeNS are preformance key\n\nexports.__DOMHandler = DOMHandler;\nexports.normalizeLineEndings = normalizeLineEndings;\nexports.DOMParser = DOMParser;\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,oBAAoB,IAAI,iBAAiB;AAE7C,IAAI,YAAY,YAAY,SAAS;AAErC,IAAI,aAAa,IAAI,UAAU;AAC/B,IAAI,YAAY,IAAI,SAAS;AAE7B;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAAS,qBAAqB,KAAK;IAClC,OAAO,MACL,OAAO,CAAC,iBAAiB,MACzB,OAAO,CAAC,qBAAqB;AAChC;AAEA;;;;CAIC,GAED;;;;;;;;;;CAUC,GAED;;;;;;;;;;;;CAYC,GACD,SAAS,UAAU,OAAO;IACzB,IAAI,CAAC,OAAO,GAAG,WAAU;QAAC,SAAQ,CAAC;IAAC;AACrC;AAEA,UAAU,SAAS,CAAC,eAAe,GAAG,SAAS,MAAM,EAAC,QAAQ;IAC7D,IAAI,UAAU,IAAI,CAAC,OAAO;IAC1B,IAAI,MAAO,IAAI;IACf,IAAI,aAAa,QAAQ,UAAU,IAAI,IAAI,cAAa,mCAAmC;IAC3F,IAAI,eAAe,QAAQ,YAAY;IACvC,IAAI,UAAU,QAAQ,OAAO;IAC7B,IAAI,eAAe,QAAQ,KAAK,IAAE,CAAC;IACnC,IAAI,SAAS,aAAa,IAAI,CAAC,WAAU,8CAA8C;IACrF,IAAI,YAAY,SAAS,SAAS,aAAa,GAAG,SAAS,YAAY;IACzE,IAAG,SAAQ;QACV,WAAW,kBAAkB,CAAC;IAC/B;IAEA,IAAI,YAAY,GAAG,kBAAkB,cAAa,YAAW;IAC7D,IAAI,UAAU,GAAG,QAAQ,UAAU,IAAI;IACvC,IAAG,QAAO;QACT,YAAY,CAAC,GAAG,GAAG,UAAU,IAAI;IAClC;IACA,aAAa,GAAG,GAAG,aAAa,GAAG,IAAI,UAAU,GAAG;IACpD,IAAI,YAAY,QAAQ,oBAAoB,IAAI;IAChD,IAAI,UAAU,OAAO,WAAW,UAAU;QACzC,IAAI,KAAK,CACR,UAAU,SACV,cACA;IAEF,OAAO;QACN,IAAI,YAAY,CAAC,KAAK,CAAC;IACxB;IACA,OAAO,WAAW,GAAG;AACtB;AACA,SAAS,kBAAkB,SAAS,EAAC,UAAU,EAAC,OAAO;IACtD,IAAG,CAAC,WAAU;QACb,IAAG,sBAAsB,YAAW;YACnC,OAAO;QACR;QACA,YAAY;IACb;IACA,IAAI,eAAe,CAAC;IACpB,IAAI,aAAa,qBAAqB;IACtC,UAAU,WAAS,CAAC;IACpB,SAAS,MAAM,GAAG;QACjB,IAAI,KAAK,SAAS,CAAC,IAAI;QACvB,IAAG,CAAC,MAAM,YAAW;YACpB,KAAK,UAAU,MAAM,IAAI,IAAE,SAAS,GAAG;gBAAE,UAAU,KAAI;YAAI,IAAE;QAC9D;QACA,YAAY,CAAC,IAAI,GAAG,MAAM,SAAS,GAAG;YACrC,GAAG,aAAW,MAAI,QAAM,MAAI,SAAS;QACtC,KAAG,YAAW;IACf;IACA,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;AACR;AAEA,oCAAoC;AACpC;;;;;;;;CAQC,GACD,SAAS;IACL,IAAI,CAAC,KAAK,GAAG;AACjB;AACA,SAAS,SAAS,OAAO,EAAC,IAAI;IAC7B,KAAK,UAAU,GAAG,QAAQ,UAAU;IACpC,KAAK,YAAY,GAAG,QAAQ,YAAY;AACzC;AACA;;;CAGC,GACD,WAAW,SAAS,GAAG;IACtB,eAAgB;QACZ,IAAI,CAAC,GAAG,GAAG,IAAI,oBAAoB,cAAc,CAAC,MAAM,MAAM;QAC9D,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;QAChD;IACJ;IACA,cAAa,SAAS,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK;QAC1D,IAAI,MAAM,IAAI,CAAC,GAAG;QACf,IAAI,KAAK,IAAI,eAAe,CAAC,cAAc,SAAO;QAClD,IAAI,MAAM,MAAM,MAAM;QACtB,cAAc,IAAI,EAAE;QACpB,IAAI,CAAC,cAAc,GAAG;QAEzB,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,EAAC;QACnC,IAAK,IAAI,IAAI,GAAI,IAAI,KAAK,IAAK;YAC3B,IAAI,eAAe,MAAM,MAAM,CAAC;YAChC,IAAI,QAAQ,MAAM,QAAQ,CAAC;YAC3B,IAAI,QAAQ,MAAM,QAAQ,CAAC;YACjC,IAAI,OAAO,IAAI,iBAAiB,CAAC,cAAc;YAC/C,IAAI,CAAC,OAAO,IAAG,SAAS,MAAM,UAAU,CAAC,IAAG;YAC5C,KAAK,KAAK,GAAG,KAAK,SAAS,GAAG;YAC9B,GAAG,gBAAgB,CAAC;QAClB;IACJ;IACA,YAAW,SAAS,YAAY,EAAE,SAAS,EAAE,KAAK;QACjD,IAAI,UAAU,IAAI,CAAC,cAAc;QACjC,IAAI,UAAU,QAAQ,OAAO;QAC7B,IAAI,CAAC,cAAc,GAAG,QAAQ,UAAU;IACzC;IACA,oBAAmB,SAAS,MAAM,EAAE,GAAG,GACvC;IACA,kBAAiB,SAAS,MAAM,GAChC;IACA,uBAAsB,SAAS,MAAM,EAAE,IAAI;QACvC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,QAAQ;QACvD,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,EAAC;QACtC,cAAc,IAAI,EAAE;IACxB;IACA,qBAAoB,SAAS,EAAE,EAAE,KAAK,EAAE,MAAM,GAC9C;IACA,YAAW,SAAS,KAAK,EAAE,KAAK,EAAE,MAAM;QACvC,QAAQ,UAAU,KAAK,CAAC,IAAI,EAAC;QAC7B,oBAAoB;QACpB,IAAG,OAAM;YACR,IAAI,IAAI,CAAC,KAAK,EAAE;gBACf,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;YAC5C,OAAO;gBACN,IAAI,WAAW,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACxC;YACA,IAAG,IAAI,CAAC,cAAc,EAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;YACjC,OAAM,IAAG,QAAQ,IAAI,CAAC,QAAO;gBAC5B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;YACrB,aAAa;YACd;YACA,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,EAAC;QACvC;IACD;IACA,eAAc,SAAS,IAAI,GAC3B;IACA,aAAY;QACX,IAAI,CAAC,GAAG,CAAC,SAAS;IACnB;IACA,oBAAmB,SAAU,OAAO;QAChC,IAAG,IAAI,CAAC,OAAO,GAAG,SAAQ;YACzB,QAAQ,UAAU,GAAG;QACtB;IACJ;IACA,gBAAgB;IAChB,SAAQ,SAAS,KAAK,EAAE,KAAK,EAAE,MAAM;QACpC,QAAQ,UAAU,KAAK,CAAC,IAAI,EAAC;QAC1B,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;QAClC,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,EAAC;QACtC,cAAc,IAAI,EAAE;IACxB;IAEA,YAAW;QACP,8BAA8B;QAC9B,IAAI,CAAC,KAAK,GAAG;IACjB;IACA,UAAS;QACL,IAAI,CAAC,KAAK,GAAG;IACjB;IAEA,UAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,QAAQ;QACzC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc;QAC/B,IAAI,QAAQ,KAAK,kBAAkB,EAAE;YACjC,IAAI,KAAK,KAAK,kBAAkB,CAAC,MAAM,UAAU;YACjD,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,EAAC;YACtC,cAAc,IAAI,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG;QACnB;IACJ;IACA;;;EAGC,GACD,SAAQ,SAAS,KAAK;QACrB,QAAQ,IAAI,CAAC,uBAAqB,OAAM,SAAS,IAAI,CAAC,OAAO;IAC9D;IACA,OAAM,SAAS,KAAK;QACnB,QAAQ,KAAK,CAAC,qBAAmB,OAAM,SAAS,IAAI,CAAC,OAAO;IAC7D;IACA,YAAW,SAAS,KAAK;QACxB,MAAM,IAAI,WAAW,OAAO,IAAI,CAAC,OAAO;IACzC;AACD;AACA,SAAS,SAAS,CAAC;IAClB,IAAG,GAAE;QACJ,OAAO,QAAM,CAAC,EAAE,QAAQ,IAAG,EAAE,IAAE,YAAU,EAAE,UAAU,GAAC,UAAQ,EAAE,YAAY,GAAC;IAC9E;AACD;AACA,SAAS,UAAU,KAAK,EAAC,KAAK,EAAC,MAAM;IACpC,IAAG,OAAO,SAAS,UAAS;QAC3B,OAAO,MAAM,MAAM,CAAC,OAAM;IAC3B,OAAK;QACJ,IAAG,MAAM,MAAM,IAAI,QAAM,UAAU,OAAM;YACxC,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAM,OAAM,UAAQ;QACjD;QACA,OAAO;IACR;AACD;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,+JAA+J,OAAO,CAAC,QAAO,SAAS,GAAG;IACzL,WAAW,SAAS,CAAC,IAAI,GAAG;QAAW,OAAO;IAAI;AACnD;AAEA,4LAA4L,GAC5L,SAAS,cAAe,MAAM,EAAC,IAAI;IAC/B,IAAI,CAAC,OAAO,cAAc,EAAE;QACxB,OAAO,GAAG,CAAC,WAAW,CAAC;IAC3B,OAAO;QACH,OAAO,cAAc,CAAC,WAAW,CAAC;IACtC;AACJ,EAAC,oDAAoD;AAErD,QAAQ,YAAY,GAAG;AACvB,QAAQ,oBAAoB,GAAG;AAC/B,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/%40xmldom/xmldom/lib/index.js"], "sourcesContent": ["var dom = require('./dom')\nexports.DOMImplementation = dom.DOMImplementation\nexports.XMLSerializer = dom.XMLSerializer\nexports.DOMParser = require('./dom-parser').DOMParser\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,QAAQ,iBAAiB,GAAG,IAAI,iBAAiB;AACjD,QAAQ,aAAa,GAAG,IAAI,aAAa;AACzC,QAAQ,SAAS,GAAG,6GAAwB,SAAS", "ignoreList": [0], "debugId": null}}]}