'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { UserPreferences } from '@/lib/types';
import { AlertCircle, Check, X } from 'lucide-react';

interface SpellCheckSuggestion {
  word: string;
  suggestions: string[];
  position: { start: number; end: number };
}

interface SpellCheckerProps {
  text: string;
  onTextChange: (text: string) => void;
  preferences: UserPreferences;
  className?: string;
  placeholder?: string;
}

// Simple spell checker using browser's built-in spell check API
// In a real app, you might want to use a more sophisticated library
export default function SpellChecker({
  text,
  onTextChange,
  preferences,
  className = '',
  placeholder = 'Start writing...'
}: SpellCheckerProps) {
  const [misspelledWords, setMisspelledWords] = useState<SpellCheckSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<SpellCheckSuggestion | null>(null);
  const [suggestionPosition, setSuggestionPosition] = useState({ x: 0, y: 0 });
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionRef = useRef<HTMLDivElement>(null);

  // Basic word validation (you could enhance this with a proper dictionary)
  const isValidWord = useCallback((word: string): boolean => {
    // Remove punctuation and check if it's a valid word
    const cleanWord = word.replace(/[^\w]/g, '');
    if (cleanWord.length < 2) return true; // Skip very short words
    
    // Basic checks for common patterns
    const commonPatterns = [
      /^\d+$/, // Numbers
      /^[A-Z]+$/, // All caps (acronyms)
      /^[a-z]+ing$/, // -ing words
      /^[a-z]+ed$/, // -ed words
      /^[a-z]+ly$/, // -ly words
      /^[a-z]+tion$/, // -tion words
    ];
    
    return commonPatterns.some(pattern => pattern.test(cleanWord));
  }, []);

  // Get suggestions for a misspelled word
  const getSuggestions = useCallback((word: string): string[] => {
    // This is a simplified suggestion system
    // In a real app, you'd use a proper spell checking library
    const suggestions: string[] = [];
    
    // Common corrections
    const corrections: Record<string, string[]> = {
      'teh': ['the'],
      'adn': ['and'],
      'recieve': ['receive'],
      'seperate': ['separate'],
      'definately': ['definitely'],
      'occured': ['occurred'],
      'neccessary': ['necessary'],
      'accomodate': ['accommodate'],
      'beleive': ['believe'],
      'acheive': ['achieve']
    };
    
    const lowerWord = word.toLowerCase();
    if (corrections[lowerWord]) {
      suggestions.push(...corrections[lowerWord]);
    }
    
    // Add some basic suggestions based on common typos
    if (suggestions.length === 0) {
      // Remove double letters
      if (/(.)\1/.test(word)) {
        suggestions.push(word.replace(/(.)\1+/g, '$1'));
      }
      
      // Add common endings if missing
      if (word.endsWith('ing') && word.length > 6) {
        suggestions.push(word.slice(0, -3) + 'ed');
      }
    }
    
    return suggestions.slice(0, 5); // Limit to 5 suggestions
  }, []);

  // Check text for spelling errors
  const checkSpelling = useCallback(() => {
    if (!preferences.spellCheckEnabled || !text) {
      setMisspelledWords([]);
      return;
    }

    const words = text.split(/(\s+|[^\w\s])/);
    const errors: SpellCheckSuggestion[] = [];
    let position = 0;

    words.forEach(word => {
      if (/^\w+$/.test(word) && !isValidWord(word)) {
        const suggestions = getSuggestions(word);
        if (suggestions.length > 0) {
          errors.push({
            word,
            suggestions,
            position: { start: position, end: position + word.length }
          });
        }
      }
      position += word.length;
    });

    setMisspelledWords(errors);
  }, [text, preferences.spellCheckEnabled, isValidWord, getSuggestions]);

  // Debounced spell checking
  useEffect(() => {
    const timer = setTimeout(checkSpelling, 500);
    return () => clearTimeout(timer);
  }, [checkSpelling]);

  // Handle right-click on misspelled words
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (!preferences.spellCheckEnabled) return;

    e.preventDefault();
    
    const textarea = textareaRef.current;
    if (!textarea) return;

    const cursorPosition = textarea.selectionStart;
    const misspelled = misspelledWords.find(
      error => cursorPosition >= error.position.start && cursorPosition <= error.position.end
    );

    if (misspelled) {
      setSelectedSuggestion(misspelled);
      setSuggestionPosition({ x: e.clientX, y: e.clientY });
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [misspelledWords, preferences.spellCheckEnabled]);

  // Apply suggestion
  const applySuggestion = useCallback((suggestion: string) => {
    if (!selectedSuggestion) return;

    const { start, end } = selectedSuggestion.position;
    const newText = text.slice(0, start) + suggestion + text.slice(end);
    onTextChange(newText);
    setShowSuggestions(false);
    setSelectedSuggestion(null);
  }, [text, selectedSuggestion, onTextChange]);

  // Add word to personal dictionary (simplified)
  const addToDictionary = useCallback(() => {
    if (!selectedSuggestion) return;
    
    // In a real app, you'd save this to a personal dictionary
    console.log('Added to dictionary:', selectedSuggestion.word);
    setShowSuggestions(false);
    setSelectedSuggestion(null);
    
    // Remove from misspelled words
    setMisspelledWords(prev => 
      prev.filter(error => error.word !== selectedSuggestion.word)
    );
  }, [selectedSuggestion]);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionRef.current && !suggestionRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Highlight misspelled words in the text
  const getHighlightedText = () => {
    if (!preferences.spellCheckEnabled || misspelledWords.length === 0) {
      return text;
    }

    let highlightedText = text;
    let offset = 0;

    misspelledWords.forEach(error => {
      const start = error.position.start + offset;
      const end = error.position.end + offset;
      const before = highlightedText.slice(0, start);
      const word = highlightedText.slice(start, end);
      const after = highlightedText.slice(end);
      
      const highlighted = `<span class="spell-error" data-word="${word}">${word}</span>`;
      highlightedText = before + highlighted + after;
      offset += highlighted.length - word.length;
    });

    return highlightedText;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main textarea */}
      <textarea
        ref={textareaRef}
        value={text}
        onChange={(e) => onTextChange(e.target.value)}
        onContextMenu={handleContextMenu}
        placeholder={placeholder}
        className="w-full h-full bg-transparent border-none outline-none resize-none"
        style={{
          fontSize: `${preferences.fontSize}px`,
          fontFamily: preferences.fontFamily,
          spellCheck: preferences.spellCheckEnabled
        }}
        spellCheck={preferences.spellCheckEnabled}
      />

      {/* Spell check indicator */}
      {preferences.spellCheckEnabled && misspelledWords.length > 0 && (
        <div className="absolute top-2 right-2 flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
          <AlertCircle className="w-3 h-3" />
          <span>{misspelledWords.length} spelling {misspelledWords.length === 1 ? 'error' : 'errors'}</span>
        </div>
      )}

      {/* Suggestion popup */}
      {showSuggestions && selectedSuggestion && (
        <div
          ref={suggestionRef}
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg py-2 min-w-48"
          style={{
            left: suggestionPosition.x,
            top: suggestionPosition.y,
            transform: 'translate(-50%, -100%)'
          }}
        >
          <div className="px-3 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            Suggestions for "{selectedSuggestion.word}"
          </div>
          
          {selectedSuggestion.suggestions.length > 0 ? (
            <div className="py-1">
              {selectedSuggestion.suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => applySuggestion(suggestion)}
                  className="w-full px-3 py-1 text-left text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
              No suggestions available
            </div>
          )}
          
          <div className="border-t border-gray-200 dark:border-gray-700 py-1">
            <button
              onClick={addToDictionary}
              className="w-full px-3 py-1 text-left text-sm text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              Add to dictionary
            </button>
            
            <button
              onClick={() => setShowSuggestions(false)}
              className="w-full px-3 py-1 text-left text-sm text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              Ignore
            </button>
          </div>
        </div>
      )}

      {/* Custom styles for spell check highlighting */}
      <style jsx>{`
        .spell-error {
          text-decoration: underline;
          text-decoration-color: #ef4444;
          text-decoration-style: wavy;
          text-underline-offset: 2px;
        }
      `}</style>
    </div>
  );
}
