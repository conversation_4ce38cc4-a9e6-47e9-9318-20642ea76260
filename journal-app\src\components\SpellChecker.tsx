'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Check, X, Plus, BookOpen } from 'lucide-react';

interface SpellCheckSuggestion {
  word: string;
  suggestions: string[];
  position: { start: number; end: number };
}

interface SpellCheckerProps {
  text: string;
  onTextChange: (text: string) => void;
  enabled: boolean;
  className?: string;
}

export function SpellChecker({ text, onTextChange, enabled, className = '' }: SpellCheckerProps) {
  const [suggestions, setSuggestions] = useState<SpellCheckSuggestion[]>([]);
  const [customDictionary, setCustomDictionary] = useState<Set<string>>(new Set());
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [currentSuggestion, setCurrentSuggestion] = useState<SpellCheckSuggestion | null>(null);
  const [suggestionPosition, setSuggestionPosition] = useState({ x: 0, y: 0 });
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Load custom dictionary from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('journal_custom_dictionary');
        if (stored) {
          setCustomDictionary(new Set(JSON.parse(stored)));
        }
      } catch (error) {
        console.error('Error loading custom dictionary:', error);
      }
    }
  }, []);

  // Save custom dictionary to localStorage
  const saveCustomDictionary = (dictionary: Set<string>) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('journal_custom_dictionary', JSON.stringify([...dictionary]));
      } catch (error) {
        console.error('Error saving custom dictionary:', error);
      }
    }
  };

  // Basic spell check using browser API (if available) or simple word validation
  const checkSpelling = async (textToCheck: string): Promise<SpellCheckSuggestion[]> => {
    if (!enabled || !textToCheck.trim()) return [];

    const words = textToCheck.split(/\b/);
    const misspelledWords: SpellCheckSuggestion[] = [];
    let position = 0;

    for (const word of words) {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      
      if (cleanWord.length > 2 && !customDictionary.has(cleanWord)) {
        // Simple check for common misspellings or non-dictionary words
        if (isLikelyMisspelled(cleanWord)) {
          const suggestions = generateSuggestions(cleanWord);
          misspelledWords.push({
            word: cleanWord,
            suggestions,
            position: {
              start: position,
              end: position + word.length,
            },
          });
        }
      }
      
      position += word.length;
    }

    return misspelledWords;
  };

  // Simple heuristic to detect likely misspellings
  const isLikelyMisspelled = (word: string): boolean => {
    // Skip very short words, numbers, and common abbreviations
    if (word.length < 3 || /^\d+$/.test(word)) return false;
    
    // Skip words that are likely proper nouns (capitalized)
    if (/^[A-Z]/.test(word)) return false;
    
    // Simple patterns that might indicate misspellings
    const suspiciousPatterns = [
      /(.)\1{3,}/, // Repeated characters (more than 3)
      /[qwrtypsdghklzxcvbnm]{5,}/, // Too many consonants in a row
      /[aeiou]{4,}/, // Too many vowels in a row
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(word));
  };

  // Generate simple suggestions (this could be enhanced with a proper spell check library)
  const generateSuggestions = (word: string): string[] => {
    const suggestions: string[] = [];
    
    // Common corrections
    const commonCorrections: { [key: string]: string[] } = {
      'teh': ['the'],
      'adn': ['and'],
      'recieve': ['receive'],
      'seperate': ['separate'],
      'definately': ['definitely'],
      'occured': ['occurred'],
      'begining': ['beginning'],
      'writting': ['writing'],
      'comming': ['coming'],
      'runing': ['running'],
    };
    
    if (commonCorrections[word]) {
      suggestions.push(...commonCorrections[word]);
    }
    
    // Simple character substitutions
    const alphabet = 'abcdefghijklmnopqrstuvwxyz';
    for (let i = 0; i < word.length; i++) {
      for (const char of alphabet) {
        if (char !== word[i]) {
          const suggestion = word.substring(0, i) + char + word.substring(i + 1);
          if (suggestion.length > 2) {
            suggestions.push(suggestion);
          }
        }
      }
    }
    
    return suggestions.slice(0, 5); // Limit to 5 suggestions
  };

  // Check spelling when text changes
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      const newSuggestions = await checkSpelling(text);
      setSuggestions(newSuggestions);
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timeoutId);
  }, [text, enabled, customDictionary]);

  const handleWordClick = (event: React.MouseEvent, suggestion: SpellCheckSuggestion) => {
    event.preventDefault();
    setCurrentSuggestion(suggestion);
    setSuggestionPosition({ x: event.clientX, y: event.clientY });
    setShowSuggestions(true);
  };

  const applySuggestion = (newWord: string) => {
    if (!currentSuggestion) return;
    
    const { start, end } = currentSuggestion.position;
    const newText = text.substring(0, start) + newWord + text.substring(end);
    onTextChange(newText);
    setShowSuggestions(false);
    setCurrentSuggestion(null);
  };

  const addToCustomDictionary = (word: string) => {
    const newDictionary = new Set(customDictionary);
    newDictionary.add(word.toLowerCase());
    setCustomDictionary(newDictionary);
    saveCustomDictionary(newDictionary);
    setShowSuggestions(false);
    setCurrentSuggestion(null);
  };

  const ignoreSuggestion = () => {
    setShowSuggestions(false);
    setCurrentSuggestion(null);
  };

  // Render text with highlighted misspelled words
  const renderTextWithHighlights = () => {
    if (!enabled || suggestions.length === 0) {
      return text;
    }

    let lastIndex = 0;
    const elements: React.ReactNode[] = [];

    suggestions.forEach((suggestion, index) => {
      const { start, end } = suggestion.position;
      
      // Add text before the misspelled word
      if (start > lastIndex) {
        elements.push(text.substring(lastIndex, start));
      }
      
      // Add the misspelled word with highlighting
      elements.push(
        <span
          key={index}
          className="bg-red-100 dark:bg-red-900/30 border-b-2 border-red-500 cursor-pointer"
          onClick={(e) => handleWordClick(e, suggestion)}
          title={`Possible misspelling: ${suggestion.word}`}
        >
          {text.substring(start, end)}
        </span>
      );
      
      lastIndex = end;
    });
    
    // Add remaining text
    if (lastIndex < text.length) {
      elements.push(text.substring(lastIndex));
    }
    
    return elements;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Text display with spell check highlights */}
      {enabled ? (
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={text}
            onChange={(e) => onTextChange(e.target.value)}
            className="w-full h-full bg-transparent border-none outline-none resize-none text-transparent caret-black dark:caret-white relative z-10"
            spellCheck={false} // Disable browser spell check since we're doing our own
          />
          <div className="absolute inset-0 p-2 pointer-events-none whitespace-pre-wrap break-words z-0">
            {renderTextWithHighlights()}
          </div>
        </div>
      ) : (
        <textarea
          value={text}
          onChange={(e) => onTextChange(e.target.value)}
          className="w-full h-full bg-transparent border-none outline-none resize-none"
          spellCheck={enabled}
        />
      )}

      {/* Suggestions popup */}
      {showSuggestions && currentSuggestion && (
        <div
          ref={suggestionsRef}
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-2 min-w-48"
          style={{
            left: suggestionPosition.x,
            top: suggestionPosition.y + 10,
          }}
        >
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            Suggestions for "{currentSuggestion.word}":
          </div>
          
          <div className="space-y-1">
            {currentSuggestion.suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => applySuggestion(suggestion)}
                className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded flex items-center"
              >
                <Check className="w-3 h-3 mr-2 text-green-500" />
                {suggestion}
              </button>
            ))}
            
            <hr className="my-2 border-gray-200 dark:border-gray-600" />
            
            <button
              onClick={() => addToCustomDictionary(currentSuggestion.word)}
              className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded flex items-center text-blue-600 dark:text-blue-400"
            >
              <Plus className="w-3 h-3 mr-2" />
              Add to dictionary
            </button>
            
            <button
              onClick={ignoreSuggestion}
              className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded flex items-center text-gray-600 dark:text-gray-400"
            >
              <X className="w-3 h-3 mr-2" />
              Ignore
            </button>
          </div>
        </div>
      )}

      {/* Custom dictionary info */}
      {enabled && customDictionary.size > 0 && (
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center">
          <BookOpen className="w-3 h-3 mr-1" />
          Custom dictionary: {customDictionary.size} words
        </div>
      )}
    </div>
  );
}
