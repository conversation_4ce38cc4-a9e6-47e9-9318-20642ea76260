@import "tailwindcss";

/* Custom CSS Variables */
:root {
  --background: #ffffff;
  --foreground: #171717;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

/* Base Styles */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Inter, system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Smooth transitions for theme changes */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 9999px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Text selection */
::selection {
  background: #dbeafe;
}

.dark ::selection {
  background: #1e3a8a;
}

/* Focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.dark .focus-ring {
  box-shadow: 0 0 0 2px #60a5fa;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
