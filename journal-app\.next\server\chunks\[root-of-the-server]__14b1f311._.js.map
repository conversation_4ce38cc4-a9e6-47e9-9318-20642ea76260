{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport { JournalEntry, Tag, UserPreferences, JournalEntryRow, TagRow, UserPreferencesRow } from './types';\nimport { v4 as uuidv4 } from 'uuid';\n\nclass JournalDatabase {\n  private db: Database.Database;\n\n  constructor(dbPath: string = 'journal.db') {\n    this.db = new Database(dbPath);\n    this.initializeTables();\n  }\n\n  private initializeTables() {\n    // Create journal entries table\n    this.db.exec(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id TEXT PRIMARY KEY,\n        title TEXT NOT NULL,\n        content TEXT NOT NULL,\n        tags TEXT NOT NULL DEFAULT '[]',\n        created_at TEXT NOT NULL,\n        updated_at TEXT NOT NULL,\n        attachments TEXT,\n        word_count INTEGER DEFAULT 0\n      )\n    `);\n\n    // Create tags table\n    this.db.exec(`\n      CREATE TABLE IF NOT EXISTS tags (\n        id TEXT PRIMARY KEY,\n        name TEXT UNIQUE NOT NULL,\n        color TEXT,\n        created_at TEXT NOT NULL,\n        usage_count INTEGER DEFAULT 0\n      )\n    `);\n\n    // Create user preferences table\n    this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_preferences (\n        id TEXT PRIMARY KEY,\n        theme TEXT DEFAULT 'system',\n        font_size INTEGER DEFAULT 16,\n        font_family TEXT DEFAULT 'Inter',\n        spell_check_enabled INTEGER DEFAULT 1,\n        auto_save_interval INTEGER DEFAULT 2000,\n        last_sync_at TEXT\n      )\n    `);\n\n    // Create indexes for better performance\n    this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_entries_created_at ON journal_entries(created_at);\n      CREATE INDEX IF NOT EXISTS idx_entries_updated_at ON journal_entries(updated_at);\n      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);\n    `);\n\n    // Insert default preferences if none exist\n    const prefsCount = this.db.prepare('SELECT COUNT(*) as count FROM user_preferences').get() as { count: number };\n    if (prefsCount.count === 0) {\n      this.createDefaultPreferences();\n    }\n  }\n\n  private createDefaultPreferences() {\n    const defaultPrefs: UserPreferencesRow = {\n      id: uuidv4(),\n      theme: 'system',\n      font_size: 16,\n      font_family: 'Inter',\n      spell_check_enabled: 1,\n      auto_save_interval: 2000,\n      last_sync_at: null\n    };\n\n    this.db.prepare(`\n      INSERT INTO user_preferences (id, theme, font_size, font_family, spell_check_enabled, auto_save_interval, last_sync_at)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `).run(\n      defaultPrefs.id,\n      defaultPrefs.theme,\n      defaultPrefs.font_size,\n      defaultPrefs.font_family,\n      defaultPrefs.spell_check_enabled,\n      defaultPrefs.auto_save_interval,\n      defaultPrefs.last_sync_at\n    );\n  }\n\n  // Journal Entry operations\n  createEntry(entry: Omit<JournalEntry, 'id' | 'createdAt' | 'updatedAt'>): JournalEntry {\n    const id = uuidv4();\n    const now = new Date().toISOString();\n    const wordCount = this.countWords(entry.content);\n\n    const newEntry: JournalEntry = {\n      id,\n      ...entry,\n      createdAt: new Date(now),\n      updatedAt: new Date(now),\n      wordCount\n    };\n\n    this.db.prepare(`\n      INSERT INTO journal_entries (id, title, content, tags, created_at, updated_at, attachments, word_count)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `).run(\n      id,\n      entry.title,\n      entry.content,\n      JSON.stringify(entry.tags || []),\n      now,\n      now,\n      JSON.stringify(entry.attachments || []),\n      wordCount\n    );\n\n    // Update tag usage counts\n    this.updateTagUsage(entry.tags || []);\n\n    return newEntry;\n  }\n\n  updateEntry(id: string, updates: Partial<JournalEntry>): JournalEntry | null {\n    const existing = this.getEntry(id);\n    if (!existing) return null;\n\n    const now = new Date().toISOString();\n    const wordCount = updates.content ? this.countWords(updates.content) : existing.wordCount;\n\n    const updatedEntry = {\n      ...existing,\n      ...updates,\n      updatedAt: new Date(now),\n      wordCount\n    };\n\n    this.db.prepare(`\n      UPDATE journal_entries \n      SET title = ?, content = ?, tags = ?, updated_at = ?, attachments = ?, word_count = ?\n      WHERE id = ?\n    `).run(\n      updatedEntry.title,\n      updatedEntry.content,\n      JSON.stringify(updatedEntry.tags),\n      now,\n      JSON.stringify(updatedEntry.attachments || []),\n      wordCount,\n      id\n    );\n\n    // Update tag usage counts\n    if (updates.tags) {\n      this.updateTagUsage(updates.tags);\n    }\n\n    return updatedEntry;\n  }\n\n  getEntry(id: string): JournalEntry | null {\n    const row = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(id) as JournalEntryRow | undefined;\n    return row ? this.rowToEntry(row) : null;\n  }\n\n  getAllEntries(limit?: number, offset?: number): JournalEntry[] {\n    let query = 'SELECT * FROM journal_entries ORDER BY updated_at DESC';\n    if (limit) {\n      query += ` LIMIT ${limit}`;\n      if (offset) {\n        query += ` OFFSET ${offset}`;\n      }\n    }\n\n    const rows = this.db.prepare(query).all() as JournalEntryRow[];\n    return rows.map(row => this.rowToEntry(row));\n  }\n\n  deleteEntry(id: string): boolean {\n    const result = this.db.prepare('DELETE FROM journal_entries WHERE id = ?').run(id);\n    return result.changes > 0;\n  }\n\n  // Tag operations\n  createTag(name: string, color?: string): Tag {\n    const id = uuidv4();\n    const now = new Date().toISOString();\n\n    const newTag: Tag = {\n      id,\n      name,\n      color,\n      createdAt: new Date(now),\n      usageCount: 0\n    };\n\n    this.db.prepare(`\n      INSERT INTO tags (id, name, color, created_at, usage_count)\n      VALUES (?, ?, ?, ?, ?)\n    `).run(id, name, color, now, 0);\n\n    return newTag;\n  }\n\n  getAllTags(): Tag[] {\n    const rows = this.db.prepare('SELECT * FROM tags ORDER BY usage_count DESC, name ASC').all() as TagRow[];\n    return rows.map(row => this.rowToTag(row));\n  }\n\n  // User preferences operations\n  getPreferences(): UserPreferences {\n    const row = this.db.prepare('SELECT * FROM user_preferences LIMIT 1').get() as UserPreferencesRow;\n    return this.rowToPreferences(row);\n  }\n\n  updatePreferences(updates: Partial<UserPreferences>): UserPreferences {\n    const existing = this.getPreferences();\n    const updated = { ...existing, ...updates };\n\n    this.db.prepare(`\n      UPDATE user_preferences \n      SET theme = ?, font_size = ?, font_family = ?, spell_check_enabled = ?, auto_save_interval = ?\n      WHERE id = ?\n    `).run(\n      updated.theme,\n      updated.fontSize,\n      updated.fontFamily,\n      updated.spellCheckEnabled ? 1 : 0,\n      updated.autoSaveInterval,\n      existing.id\n    );\n\n    return updated;\n  }\n\n  // Search operations\n  searchEntries(query: string, tags?: string[]): JournalEntry[] {\n    let sql = `\n      SELECT * FROM journal_entries \n      WHERE (title LIKE ? OR content LIKE ?)\n    `;\n    const params: any[] = [`%${query}%`, `%${query}%`];\n\n    if (tags && tags.length > 0) {\n      const tagConditions = tags.map(() => 'tags LIKE ?').join(' AND ');\n      sql += ` AND (${tagConditions})`;\n      tags.forEach(tag => params.push(`%\"${tag}\"%`));\n    }\n\n    sql += ' ORDER BY updated_at DESC';\n\n    const rows = this.db.prepare(sql).all(...params) as JournalEntryRow[];\n    return rows.map(row => this.rowToEntry(row));\n  }\n\n  // Helper methods\n  private countWords(text: string): number {\n    return text.trim().split(/\\s+/).filter(word => word.length > 0).length;\n  }\n\n  private updateTagUsage(tags: string[]) {\n    tags.forEach(tagName => {\n      this.db.prepare(`\n        INSERT INTO tags (id, name, created_at, usage_count) \n        VALUES (?, ?, ?, 1)\n        ON CONFLICT(name) DO UPDATE SET usage_count = usage_count + 1\n      `).run(uuidv4(), tagName, new Date().toISOString());\n    });\n  }\n\n  private rowToEntry(row: JournalEntryRow): JournalEntry {\n    return {\n      id: row.id,\n      title: row.title,\n      content: row.content,\n      tags: JSON.parse(row.tags),\n      createdAt: new Date(row.created_at),\n      updatedAt: new Date(row.updated_at),\n      attachments: row.attachments ? JSON.parse(row.attachments) : undefined,\n      wordCount: row.word_count\n    };\n  }\n\n  private rowToTag(row: TagRow): Tag {\n    return {\n      id: row.id,\n      name: row.name,\n      color: row.color || undefined,\n      createdAt: new Date(row.created_at),\n      usageCount: row.usage_count\n    };\n  }\n\n  private rowToPreferences(row: UserPreferencesRow): UserPreferences {\n    return {\n      id: row.id,\n      theme: row.theme as 'light' | 'dark' | 'system',\n      fontSize: row.font_size,\n      fontFamily: row.font_family,\n      spellCheckEnabled: row.spell_check_enabled === 1,\n      autoSaveInterval: row.auto_save_interval,\n      lastSyncAt: row.last_sync_at ? new Date(row.last_sync_at) : undefined\n    };\n  }\n\n  close() {\n    this.db.close();\n  }\n}\n\nexport default JournalDatabase;\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM;IACI,GAAsB;IAE9B,YAAY,SAAiB,YAAY,CAAE;QACzC,IAAI,CAAC,EAAE,GAAG,IAAI,2HAAA,CAAA,UAAQ,CAAC;QACvB,IAAI,CAAC,gBAAgB;IACvB;IAEQ,mBAAmB;QACzB,+BAA+B;QAC/B,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;;;;;;;;;;;IAWd,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;;;;;;;;IAQd,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;;;;;;;;;;IAUd,CAAC;QAED,wCAAwC;QACxC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;;;;IAId,CAAC;QAED,2CAA2C;QAC3C,MAAM,aAAa,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,kDAAkD,GAAG;QACxF,IAAI,WAAW,KAAK,KAAK,GAAG;YAC1B,IAAI,CAAC,wBAAwB;QAC/B;IACF;IAEQ,2BAA2B;QACjC,MAAM,eAAmC;YACvC,IAAI,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;YACT,OAAO;YACP,WAAW;YACX,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,cAAc;QAChB;QAEA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;IAGjB,CAAC,EAAE,GAAG,CACJ,aAAa,EAAE,EACf,aAAa,KAAK,EAClB,aAAa,SAAS,EACtB,aAAa,WAAW,EACxB,aAAa,mBAAmB,EAChC,aAAa,kBAAkB,EAC/B,aAAa,YAAY;IAE7B;IAEA,2BAA2B;IAC3B,YAAY,KAA2D,EAAgB;QACrF,MAAM,KAAK,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QAChB,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO;QAE/C,MAAM,WAAyB;YAC7B;YACA,GAAG,KAAK;YACR,WAAW,IAAI,KAAK;YACpB,WAAW,IAAI,KAAK;YACpB;QACF;QAEA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;IAGjB,CAAC,EAAE,GAAG,CACJ,IACA,MAAM,KAAK,EACX,MAAM,OAAO,EACb,KAAK,SAAS,CAAC,MAAM,IAAI,IAAI,EAAE,GAC/B,KACA,KACA,KAAK,SAAS,CAAC,MAAM,WAAW,IAAI,EAAE,GACtC;QAGF,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,EAAE;QAEpC,OAAO;IACT;IAEA,YAAY,EAAU,EAAE,OAA8B,EAAuB;QAC3E,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,YAAY,QAAQ,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,OAAO,IAAI,SAAS,SAAS;QAEzF,MAAM,eAAe;YACnB,GAAG,QAAQ;YACX,GAAG,OAAO;YACV,WAAW,IAAI,KAAK;YACpB;QACF;QAEA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;;IAIjB,CAAC,EAAE,GAAG,CACJ,aAAa,KAAK,EAClB,aAAa,OAAO,EACpB,KAAK,SAAS,CAAC,aAAa,IAAI,GAChC,KACA,KAAK,SAAS,CAAC,aAAa,WAAW,IAAI,EAAE,GAC7C,WACA;QAGF,0BAA0B;QAC1B,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI;QAClC;QAEA,OAAO;IACT;IAEA,SAAS,EAAU,EAAuB;QACxC,MAAM,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,8CAA8C,GAAG,CAAC;QAC9E,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO;IACtC;IAEA,cAAc,KAAc,EAAE,MAAe,EAAkB;QAC7D,IAAI,QAAQ;QACZ,IAAI,OAAO;YACT,SAAS,CAAC,OAAO,EAAE,OAAO;YAC1B,IAAI,QAAQ;gBACV,SAAS,CAAC,QAAQ,EAAE,QAAQ;YAC9B;QACF;QAEA,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG;QACvC,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC;IACzC;IAEA,YAAY,EAAU,EAAW;QAC/B,MAAM,SAAS,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,4CAA4C,GAAG,CAAC;QAC/E,OAAO,OAAO,OAAO,GAAG;IAC1B;IAEA,iBAAiB;IACjB,UAAU,IAAY,EAAE,KAAc,EAAO;QAC3C,MAAM,KAAK,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;QAChB,MAAM,MAAM,IAAI,OAAO,WAAW;QAElC,MAAM,SAAc;YAClB;YACA;YACA;YACA,WAAW,IAAI,KAAK;YACpB,YAAY;QACd;QAEA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;IAGjB,CAAC,EAAE,GAAG,CAAC,IAAI,MAAM,OAAO,KAAK;QAE7B,OAAO;IACT;IAEA,aAAoB;QAClB,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,0DAA0D,GAAG;QAC1F,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,QAAQ,CAAC;IACvC;IAEA,8BAA8B;IAC9B,iBAAkC;QAChC,MAAM,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,0CAA0C,GAAG;QACzE,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B;IAEA,kBAAkB,OAAiC,EAAmB;QACpE,MAAM,WAAW,IAAI,CAAC,cAAc;QACpC,MAAM,UAAU;YAAE,GAAG,QAAQ;YAAE,GAAG,OAAO;QAAC;QAE1C,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;;IAIjB,CAAC,EAAE,GAAG,CACJ,QAAQ,KAAK,EACb,QAAQ,QAAQ,EAChB,QAAQ,UAAU,EAClB,QAAQ,iBAAiB,GAAG,IAAI,GAChC,QAAQ,gBAAgB,EACxB,SAAS,EAAE;QAGb,OAAO;IACT;IAEA,oBAAoB;IACpB,cAAc,KAAa,EAAE,IAAe,EAAkB;QAC5D,IAAI,MAAM,CAAC;;;IAGX,CAAC;QACD,MAAM,SAAgB;YAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SAAC;QAElD,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAM,eAAe,IAAI,CAAC;YACzD,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAChC,KAAK,OAAO,CAAC,CAAA,MAAO,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;QAC9C;QAEA,OAAO;QAEP,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI;QACzC,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC;IACzC;IAEA,iBAAiB;IACT,WAAW,IAAY,EAAU;QACvC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IACxE;IAEQ,eAAe,IAAc,EAAE;QACrC,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIjB,CAAC,EAAE,GAAG,CAAC,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD,KAAK,SAAS,IAAI,OAAO,WAAW;QAClD;IACF;IAEQ,WAAW,GAAoB,EAAgB;QACrD,OAAO;YACL,IAAI,IAAI,EAAE;YACV,OAAO,IAAI,KAAK;YAChB,SAAS,IAAI,OAAO;YACpB,MAAM,KAAK,KAAK,CAAC,IAAI,IAAI;YACzB,WAAW,IAAI,KAAK,IAAI,UAAU;YAClC,WAAW,IAAI,KAAK,IAAI,UAAU;YAClC,aAAa,IAAI,WAAW,GAAG,KAAK,KAAK,CAAC,IAAI,WAAW,IAAI;YAC7D,WAAW,IAAI,UAAU;QAC3B;IACF;IAEQ,SAAS,GAAW,EAAO;QACjC,OAAO;YACL,IAAI,IAAI,EAAE;YACV,MAAM,IAAI,IAAI;YACd,OAAO,IAAI,KAAK,IAAI;YACpB,WAAW,IAAI,KAAK,IAAI,UAAU;YAClC,YAAY,IAAI,WAAW;QAC7B;IACF;IAEQ,iBAAiB,GAAuB,EAAmB;QACjE,OAAO;YACL,IAAI,IAAI,EAAE;YACV,OAAO,IAAI,KAAK;YAChB,UAAU,IAAI,SAAS;YACvB,YAAY,IAAI,WAAW;YAC3B,mBAAmB,IAAI,mBAAmB,KAAK;YAC/C,kBAAkB,IAAI,kBAAkB;YACxC,YAAY,IAAI,YAAY,GAAG,IAAI,KAAK,IAAI,YAAY,IAAI;QAC9D;IACF;IAEA,QAAQ;QACN,IAAI,CAAC,EAAE,CAAC,KAAK;IACf;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/app/api/entries/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { JournalEntry, ApiResponse, PaginatedResponse } from '@/lib/types';\nimport JournalDatabase from '@/lib/database';\n\n// Initialize database (in production, this would be a singleton)\nlet db: JournalDatabase;\n\nfunction getDatabase() {\n  if (!db) {\n    db = new JournalDatabase();\n  }\n  return db;\n}\n\n// GET /api/entries - Get all entries with pagination\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const search = searchParams.get('search');\n    const tags = searchParams.get('tags')?.split(',').filter(Boolean);\n    \n    const database = getDatabase();\n    \n    let entries: JournalEntry[];\n    \n    if (search || tags) {\n      // Search entries\n      entries = database.searchEntries(search || '', tags);\n    } else {\n      // Get all entries with pagination\n      const offset = (page - 1) * limit;\n      entries = database.getAllEntries(limit, offset);\n    }\n    \n    // Get total count for pagination\n    const allEntries = database.getAllEntries();\n    const total = allEntries.length;\n    \n    const response: PaginatedResponse<JournalEntry> = {\n      data: entries,\n      total,\n      page,\n      limit,\n      hasMore: page * limit < total\n    };\n    \n    return NextResponse.json({\n      success: true,\n      data: response\n    } as ApiResponse<PaginatedResponse<JournalEntry>>);\n    \n  } catch (error) {\n    console.error('Error fetching entries:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to fetch entries'\n    } as ApiResponse<never>, { status: 500 });\n  }\n}\n\n// POST /api/entries - Create a new entry\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { title, content, tags = [] } = body;\n    \n    if (!title && !content) {\n      return NextResponse.json({\n        success: false,\n        error: 'Title or content is required'\n      } as ApiResponse<never>, { status: 400 });\n    }\n    \n    const database = getDatabase();\n    \n    const newEntry = database.createEntry({\n      title: title || 'Untitled',\n      content: content || '',\n      tags: Array.isArray(tags) ? tags : []\n    });\n    \n    return NextResponse.json({\n      success: true,\n      data: newEntry\n    } as ApiResponse<JournalEntry>, { status: 201 });\n    \n  } catch (error) {\n    console.error('Error creating entry:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to create entry'\n    } as ApiResponse<never>, { status: 500 });\n  }\n}\n\n// PUT /api/entries - Bulk update entries (for sync)\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { entries } = body;\n    \n    if (!Array.isArray(entries)) {\n      return NextResponse.json({\n        success: false,\n        error: 'Entries must be an array'\n      } as ApiResponse<never>, { status: 400 });\n    }\n    \n    const database = getDatabase();\n    const updatedEntries: JournalEntry[] = [];\n    \n    for (const entryData of entries) {\n      try {\n        if (entryData.id) {\n          // Update existing entry\n          const updated = database.updateEntry(entryData.id, entryData);\n          if (updated) {\n            updatedEntries.push(updated);\n          }\n        } else {\n          // Create new entry\n          const created = database.createEntry(entryData);\n          updatedEntries.push(created);\n        }\n      } catch (entryError) {\n        console.error('Error processing entry:', entryError);\n        // Continue with other entries\n      }\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: updatedEntries\n    } as ApiResponse<JournalEntry[]>);\n    \n  } catch (error) {\n    console.error('Error bulk updating entries:', error);\n    return NextResponse.json({\n      success: false,\n      error: 'Failed to update entries'\n    } as ApiResponse<never>, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAEA,iEAAiE;AACjE,IAAI;AAEJ,SAAS;IACP,IAAI,CAAC,IAAI;QACP,KAAK,IAAI,wHAAA,CAAA,UAAe;IAC1B;IACA,OAAO;AACT;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,OAAO,aAAa,GAAG,CAAC,SAAS,MAAM,KAAK,OAAO;QAEzD,MAAM,WAAW;QAEjB,IAAI;QAEJ,IAAI,UAAU,MAAM;YAClB,iBAAiB;YACjB,UAAU,SAAS,aAAa,CAAC,UAAU,IAAI;QACjD,OAAO;YACL,kCAAkC;YAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAC5B,UAAU,SAAS,aAAa,CAAC,OAAO;QAC1C;QAEA,iCAAiC;QACjC,MAAM,aAAa,SAAS,aAAa;QACzC,MAAM,QAAQ,WAAW,MAAM;QAE/B,MAAM,WAA4C;YAChD,MAAM;YACN;YACA;YACA;YACA,SAAS,OAAO,QAAQ;QAC1B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAyB;YAAE,QAAQ;QAAI;IACzC;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,GAAG;QAEtC,IAAI,CAAC,SAAS,CAAC,SAAS;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAyB;gBAAE,QAAQ;YAAI;QACzC;QAEA,MAAM,WAAW;QAEjB,MAAM,WAAW,SAAS,WAAW,CAAC;YACpC,OAAO,SAAS;YAChB,SAAS,WAAW;YACpB,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;QACvC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR,GAAgC;YAAE,QAAQ;QAAI;IAEhD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAyB;YAAE,QAAQ;QAAI;IACzC;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,GAAG;QAEpB,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAyB;gBAAE,QAAQ;YAAI;QACzC;QAEA,MAAM,WAAW;QACjB,MAAM,iBAAiC,EAAE;QAEzC,KAAK,MAAM,aAAa,QAAS;YAC/B,IAAI;gBACF,IAAI,UAAU,EAAE,EAAE;oBAChB,wBAAwB;oBACxB,MAAM,UAAU,SAAS,WAAW,CAAC,UAAU,EAAE,EAAE;oBACnD,IAAI,SAAS;wBACX,eAAe,IAAI,CAAC;oBACtB;gBACF,OAAO;oBACL,mBAAmB;oBACnB,MAAM,UAAU,SAAS,WAAW,CAAC;oBACrC,eAAe,IAAI,CAAC;gBACtB;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,8BAA8B;YAChC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAyB;YAAE,QAAQ;QAAI;IACzC;AACF", "debugId": null}}]}