{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/lib/localStorage.ts"], "sourcesContent": ["import { JournalEntry } from './database';\n\nconst STORAGE_KEYS = {\n  ENTRIES: 'journal_entries',\n  DRAFTS: 'journal_drafts',\n  SETTINGS: 'journal_settings',\n  TAGS: 'journal_tags',\n} as const;\n\nexport interface UserSettings {\n  theme: 'light' | 'dark' | 'system';\n  fontSize: number;\n  fontFamily: string;\n  autoSave: boolean;\n  autoSaveInterval: number; // in milliseconds\n  spellCheck: boolean;\n}\n\nexport interface Draft {\n  id: string;\n  title: string;\n  content: string;\n  tags: string[];\n  lastModified: string;\n}\n\nclass LocalStorageManager {\n  // Settings management\n  getSettings(): UserSettings {\n    if (typeof window === 'undefined') {\n      return this.getDefaultSettings();\n    }\n\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.SETTINGS);\n      if (stored) {\n        return { ...this.getDefaultSettings(), ...JSON.parse(stored) };\n      }\n    } catch (error) {\n      console.error('Error loading settings from localStorage:', error);\n    }\n    return this.getDefaultSettings();\n  }\n\n  saveSettings(settings: Partial<UserSettings>): void {\n    if (typeof window === 'undefined') return;\n\n    try {\n      const current = this.getSettings();\n      const updated = { ...current, ...settings };\n      localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(updated));\n    } catch (error) {\n      console.error('Error saving settings to localStorage:', error);\n    }\n  }\n\n  private getDefaultSettings(): UserSettings {\n    return {\n      theme: 'system',\n      fontSize: 16,\n      fontFamily: 'Inter, system-ui, sans-serif',\n      autoSave: true,\n      autoSaveInterval: 2000, // 2 seconds\n      spellCheck: true,\n    };\n  }\n\n  // Draft management (for unsaved entries)\n  saveDraft(draft: Draft): void {\n    if (typeof window === 'undefined') return;\n\n    try {\n      const drafts = this.getDrafts();\n      const existingIndex = drafts.findIndex(d => d.id === draft.id);\n\n      if (existingIndex >= 0) {\n        drafts[existingIndex] = draft;\n      } else {\n        drafts.push(draft);\n      }\n\n      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n    } catch (error) {\n      console.error('Error saving draft to localStorage:', error);\n    }\n  }\n\n  getDrafts(): Draft[] {\n    if (typeof window === 'undefined') return [];\n\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.DRAFTS);\n      return stored ? JSON.parse(stored) : [];\n    } catch (error) {\n      console.error('Error loading drafts from localStorage:', error);\n      return [];\n    }\n  }\n\n  getDraft(id: string): Draft | null {\n    const drafts = this.getDrafts();\n    return drafts.find(d => d.id === id) || null;\n  }\n\n  deleteDraft(id: string): void {\n    try {\n      const drafts = this.getDrafts().filter(d => d.id !== id);\n      localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(drafts));\n    } catch (error) {\n      console.error('Error deleting draft from localStorage:', error);\n    }\n  }\n\n  // Entry caching (for offline access)\n  cacheEntries(entries: JournalEntry[]): void {\n    try {\n      const cached = {\n        entries,\n        lastUpdated: new Date().toISOString(),\n      };\n      localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n    } catch (error) {\n      console.error('Error caching entries to localStorage:', error);\n    }\n  }\n\n  getCachedEntries(): { entries: JournalEntry[]; lastUpdated: string } | null {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.ENTRIES);\n      return stored ? JSON.parse(stored) : null;\n    } catch (error) {\n      console.error('Error loading cached entries from localStorage:', error);\n      return null;\n    }\n  }\n\n  cacheEntry(entry: JournalEntry): void {\n    try {\n      const cached = this.getCachedEntries();\n      if (cached) {\n        const existingIndex = cached.entries.findIndex(e => e.id === entry.id);\n        if (existingIndex >= 0) {\n          cached.entries[existingIndex] = entry;\n        } else {\n          cached.entries.unshift(entry);\n        }\n        cached.lastUpdated = new Date().toISOString();\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n      } else {\n        this.cacheEntries([entry]);\n      }\n    } catch (error) {\n      console.error('Error caching entry to localStorage:', error);\n    }\n  }\n\n  removeCachedEntry(id: string): void {\n    try {\n      const cached = this.getCachedEntries();\n      if (cached) {\n        cached.entries = cached.entries.filter(e => e.id !== id);\n        cached.lastUpdated = new Date().toISOString();\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(cached));\n      }\n    } catch (error) {\n      console.error('Error removing cached entry from localStorage:', error);\n    }\n  }\n\n  // Tag caching\n  cacheTags(tags: string[]): void {\n    try {\n      const cached = {\n        tags,\n        lastUpdated: new Date().toISOString(),\n      };\n      localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(cached));\n    } catch (error) {\n      console.error('Error caching tags to localStorage:', error);\n    }\n  }\n\n  getCachedTags(): string[] {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEYS.TAGS);\n      const cached = stored ? JSON.parse(stored) : null;\n      return cached ? cached.tags : [];\n    } catch (error) {\n      console.error('Error loading cached tags from localStorage:', error);\n      return [];\n    }\n  }\n\n  // Utility methods\n  clearAllData(): void {\n    try {\n      Object.values(STORAGE_KEYS).forEach(key => {\n        localStorage.removeItem(key);\n      });\n    } catch (error) {\n      console.error('Error clearing localStorage:', error);\n    }\n  }\n\n  getStorageUsage(): { used: number; available: number } {\n    try {\n      let used = 0;\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i);\n        if (key) {\n          used += localStorage.getItem(key)?.length || 0;\n        }\n      }\n\n      // Estimate available space (most browsers have ~5-10MB limit)\n      const available = 5 * 1024 * 1024 - used; // Assume 5MB limit\n      \n      return { used, available };\n    } catch (error) {\n      console.error('Error calculating storage usage:', error);\n      return { used: 0, available: 0 };\n    }\n  }\n\n  // Auto-save functionality\n  createAutoSaveTimer(callback: () => void, interval?: number): NodeJS.Timeout {\n    const settings = this.getSettings();\n    const saveInterval = interval || settings.autoSaveInterval;\n    \n    return setInterval(callback, saveInterval);\n  }\n\n  // Export/Import functionality\n  exportData(): string {\n    try {\n      const data = {\n        entries: this.getCachedEntries(),\n        drafts: this.getDrafts(),\n        settings: this.getSettings(),\n        tags: this.getCachedTags(),\n        exportDate: new Date().toISOString(),\n      };\n      return JSON.stringify(data, null, 2);\n    } catch (error) {\n      console.error('Error exporting data:', error);\n      throw new Error('Failed to export data');\n    }\n  }\n\n  importData(jsonData: string): void {\n    try {\n      const data = JSON.parse(jsonData);\n      \n      if (data.entries) {\n        localStorage.setItem(STORAGE_KEYS.ENTRIES, JSON.stringify(data.entries));\n      }\n      \n      if (data.drafts) {\n        localStorage.setItem(STORAGE_KEYS.DRAFTS, JSON.stringify(data.drafts));\n      }\n      \n      if (data.settings) {\n        localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));\n      }\n      \n      if (data.tags) {\n        localStorage.setItem(STORAGE_KEYS.TAGS, JSON.stringify(data.tags));\n      }\n    } catch (error) {\n      console.error('Error importing data:', error);\n      throw new Error('Failed to import data');\n    }\n  }\n}\n\n// Singleton instance\nlet storageInstance: LocalStorageManager | null = null;\n\nexport function getLocalStorage(): LocalStorageManager {\n  if (!storageInstance) {\n    storageInstance = new LocalStorageManager();\n  }\n  return storageInstance;\n}\n\nexport default LocalStorageManager;\n"], "names": [], "mappings": ";;;;AAEA,MAAM,eAAe;IACnB,SAAS;IACT,QAAQ;IACR,UAAU;IACV,MAAM;AACR;AAmBA,MAAM;IACJ,sBAAsB;IACtB,cAA4B;QAC1B,uCAAmC;;QAEnC;QAEA,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,QAAQ;YACzD,IAAI,QAAQ;gBACV,OAAO;oBAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE;oBAAE,GAAG,KAAK,KAAK,CAAC,OAAO;gBAAC;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D;QACA,OAAO,IAAI,CAAC,kBAAkB;IAChC;IAEA,aAAa,QAA+B,EAAQ;QAClD,uCAAmC;;QAAM;QAEzC,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,WAAW;YAChC,MAAM,UAAU;gBAAE,GAAG,OAAO;gBAAE,GAAG,QAAQ;YAAC;YAC1C,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC;QAC7D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEQ,qBAAmC;QACzC,OAAO;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,UAAU;YACV,kBAAkB;YAClB,YAAY;QACd;IACF;IAEA,yCAAyC;IACzC,UAAU,KAAY,EAAQ;QAC5B,uCAAmC;;QAAM;QAEzC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,SAAS;YAC7B,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;YAE7D,IAAI,iBAAiB,GAAG;gBACtB,MAAM,CAAC,cAAc,GAAG;YAC1B,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;YAEA,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,YAAqB;QACnB,uCAAmC;;QAAS;QAE5C,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,MAAM;YACvD,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO,EAAE;QACX;IACF;IAEA,SAAS,EAAU,EAAgB;QACjC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC1C;IAEA,YAAY,EAAU,EAAQ;QAC5B,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACrD,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA,qCAAqC;IACrC,aAAa,OAAuB,EAAQ;QAC1C,IAAI;YACF,MAAM,SAAS;gBACb;gBACA,aAAa,IAAI,OAAO,WAAW;YACrC;YACA,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,mBAA4E;QAC1E,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,OAAO;YACxD,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO;QACT;IACF;IAEA,WAAW,KAAmB,EAAQ;QACpC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB;YACpC,IAAI,QAAQ;gBACV,MAAM,gBAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;gBACrE,IAAI,iBAAiB,GAAG;oBACtB,OAAO,OAAO,CAAC,cAAc,GAAG;gBAClC,OAAO;oBACL,OAAO,OAAO,CAAC,OAAO,CAAC;gBACzB;gBACA,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;gBAC3C,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;YAC5D,OAAO;gBACL,IAAI,CAAC,YAAY,CAAC;oBAAC;iBAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,kBAAkB,EAAU,EAAQ;QAClC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB;YACpC,IAAI,QAAQ;gBACV,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACrD,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;gBAC3C,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;QAClE;IACF;IAEA,cAAc;IACd,UAAU,IAAc,EAAQ;QAC9B,IAAI;YACF,MAAM,SAAS;gBACb;gBACA,aAAa,IAAI,OAAO,WAAW;YACrC;YACA,aAAa,OAAO,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,gBAA0B;QACxB,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,IAAI;YACrD,MAAM,SAAS,SAAS,KAAK,KAAK,CAAC,UAAU;YAC7C,OAAO,SAAS,OAAO,IAAI,GAAG,EAAE;QAClC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO,EAAE;QACX;IACF;IAEA,kBAAkB;IAClB,eAAqB;QACnB,IAAI;YACF,OAAO,MAAM,CAAC,cAAc,OAAO,CAAC,CAAA;gBAClC,aAAa,UAAU,CAAC;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,kBAAuD;QACrD,IAAI;YACF,IAAI,OAAO;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,KAAK;oBACP,QAAQ,aAAa,OAAO,CAAC,MAAM,UAAU;gBAC/C;YACF;YAEA,8DAA8D;YAC9D,MAAM,YAAY,IAAI,OAAO,OAAO,MAAM,mBAAmB;YAE7D,OAAO;gBAAE;gBAAM;YAAU;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,MAAM;gBAAG,WAAW;YAAE;QACjC;IACF;IAEA,0BAA0B;IAC1B,oBAAoB,QAAoB,EAAE,QAAiB,EAAkB;QAC3E,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,eAAe,YAAY,SAAS,gBAAgB;QAE1D,OAAO,YAAY,UAAU;IAC/B;IAEA,8BAA8B;IAC9B,aAAqB;QACnB,IAAI;YACF,MAAM,OAAO;gBACX,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,QAAQ,IAAI,CAAC,SAAS;gBACtB,UAAU,IAAI,CAAC,WAAW;gBAC1B,MAAM,IAAI,CAAC,aAAa;gBACxB,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW,QAAgB,EAAQ;QACjC,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YAExB,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC,KAAK,OAAO;YACxE;YAEA,IAAI,KAAK,MAAM,EAAE;gBACf,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC,KAAK,MAAM;YACtE;YAEA,IAAI,KAAK,QAAQ,EAAE;gBACjB,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC,KAAK,QAAQ;YAC1E;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,aAAa,OAAO,CAAC,aAAa,IAAI,EAAE,KAAK,SAAS,CAAC,KAAK,IAAI;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA,qBAAqB;AACrB,IAAI,kBAA8C;AAE3C,SAAS;IACd,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,IAAI;IACxB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { getLocalStorage } from '@/lib/localStorage';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  resolvedTheme: 'light' | 'dark';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>('system');\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\n  const [mounted, setMounted] = useState(false);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const storage = getLocalStorage();\n    const settings = storage.getSettings();\n    setThemeState(settings.theme);\n    setMounted(true);\n  }, []);\n\n  // Update resolved theme when theme changes or system preference changes\n  useEffect(() => {\n    const updateResolvedTheme = () => {\n      if (theme === 'system') {\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        setResolvedTheme(systemTheme);\n      } else {\n        setResolvedTheme(theme);\n      }\n    };\n\n    updateResolvedTheme();\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateResolvedTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n    \n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(resolvedTheme);\n    \n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');\n    }\n  }, [resolvedTheme, mounted]);\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    \n    // Save to localStorage\n    const storage = getLocalStorage();\n    storage.saveSettings({ theme: newTheme });\n  };\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-white\">\n        {children}\n      </div>\n    );\n  }\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAaA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD;YAC9B,MAAM,WAAW,QAAQ,WAAW;YACpC,cAAc,SAAS,KAAK;YAC5B,WAAW;QACb;kCAAG,EAAE;IAEL,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;+DAAsB;oBAC1B,IAAI,UAAU,UAAU;wBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;wBACzF,iBAAiB;oBACnB,OAAO;wBACL,iBAAiB;oBACnB;gBACF;;YAEA;YAEA,kCAAkC;YAClC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;wDAAe;oBACnB,IAAI,UAAU,UAAU;wBACtB;oBACF;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;KAAM;IAEV,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,SAAS,eAAe;YAErC,gCAAgC;YAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAE/B,0BAA0B;YAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;YAEnB,8CAA8C;YAC9C,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,YAAY,CAAC,WAAW,kBAAkB,SAAS,YAAY;YAChF;QACF;kCAAG;QAAC;QAAe;KAAQ;IAE3B,MAAM,WAAW,CAAC;QAChB,cAAc;QAEd,uBAAuB;QACvB,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,kBAAe,AAAD;QAC9B,QAAQ,YAAY,CAAC;YAAE,OAAO;QAAS;IACzC;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAc;kBAC5D;;;;;;AAGP;IA/EgB;KAAA", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}