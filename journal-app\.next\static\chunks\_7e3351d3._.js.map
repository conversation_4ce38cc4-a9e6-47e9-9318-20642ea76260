{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/lib/localStorage.ts"], "sourcesContent": ["import { JournalEntry, Tag, UserPreferences } from './types';\n\n// Local storage keys\nconst STORAGE_KEYS = {\n  ENTRIES: 'journal_entries',\n  TAGS: 'journal_tags',\n  PREFERENCES: 'journal_preferences',\n  DRAFT: 'journal_draft',\n  PENDING_SYNC: 'journal_pending_sync',\n  LAST_SYNC: 'journal_last_sync'\n} as const;\n\n// Local storage utilities for offline functionality\nexport class LocalStorageManager {\n  \n  // Check if localStorage is available\n  private isLocalStorageAvailable(): boolean {\n    try {\n      const test = '__localStorage_test__';\n      localStorage.setItem(test, test);\n      localStorage.removeItem(test);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  // Generic storage methods\n  private setItem<T>(key: string, value: T): void {\n    if (!this.isLocalStorageAvailable()) return;\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value));\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error);\n    }\n  }\n\n  private getItem<T>(key: string): T | null {\n    if (!this.isLocalStorageAvailable()) return null;\n    \n    try {\n      const item = localStorage.getItem(key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Failed to read from localStorage:', error);\n      return null;\n    }\n  }\n\n  private removeItem(key: string): void {\n    if (!this.isLocalStorageAvailable()) return;\n    \n    try {\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Failed to remove from localStorage:', error);\n    }\n  }\n\n  // Journal entries management\n  saveEntries(entries: JournalEntry[]): void {\n    this.setItem(STORAGE_KEYS.ENTRIES, entries);\n  }\n\n  getEntries(): JournalEntry[] {\n    const entries = this.getItem<JournalEntry[]>(STORAGE_KEYS.ENTRIES);\n    return entries || [];\n  }\n\n  saveEntry(entry: JournalEntry): void {\n    const entries = this.getEntries();\n    const existingIndex = entries.findIndex(e => e.id === entry.id);\n    \n    if (existingIndex >= 0) {\n      entries[existingIndex] = entry;\n    } else {\n      entries.unshift(entry); // Add new entries at the beginning\n    }\n    \n    this.saveEntries(entries);\n    this.markForSync(entry.id);\n  }\n\n  deleteEntry(entryId: string): void {\n    const entries = this.getEntries();\n    const filteredEntries = entries.filter(e => e.id !== entryId);\n    this.saveEntries(filteredEntries);\n    this.markForSync(entryId, 'delete');\n  }\n\n  getEntry(entryId: string): JournalEntry | null {\n    const entries = this.getEntries();\n    return entries.find(e => e.id === entryId) || null;\n  }\n\n  // Tags management\n  saveTags(tags: Tag[]): void {\n    this.setItem(STORAGE_KEYS.TAGS, tags);\n  }\n\n  getTags(): Tag[] {\n    const tags = this.getItem<Tag[]>(STORAGE_KEYS.TAGS);\n    return tags || [];\n  }\n\n  saveTag(tag: Tag): void {\n    const tags = this.getTags();\n    const existingIndex = tags.findIndex(t => t.id === tag.id);\n    \n    if (existingIndex >= 0) {\n      tags[existingIndex] = tag;\n    } else {\n      tags.push(tag);\n    }\n    \n    this.saveTags(tags);\n  }\n\n  // User preferences management\n  savePreferences(preferences: UserPreferences): void {\n    this.setItem(STORAGE_KEYS.PREFERENCES, preferences);\n  }\n\n  getPreferences(): UserPreferences | null {\n    return this.getItem<UserPreferences>(STORAGE_KEYS.PREFERENCES);\n  }\n\n  // Draft management for auto-save\n  saveDraft(entryId: string, content: string): void {\n    const drafts = this.getDrafts();\n    drafts[entryId] = {\n      content,\n      timestamp: Date.now()\n    };\n    this.setItem(STORAGE_KEYS.DRAFT, drafts);\n  }\n\n  getDraft(entryId: string): { content: string; timestamp: number } | null {\n    const drafts = this.getDrafts();\n    return drafts[entryId] || null;\n  }\n\n  clearDraft(entryId: string): void {\n    const drafts = this.getDrafts();\n    delete drafts[entryId];\n    this.setItem(STORAGE_KEYS.DRAFT, drafts);\n  }\n\n  private getDrafts(): Record<string, { content: string; timestamp: number }> {\n    return this.getItem<Record<string, { content: string; timestamp: number }>>(STORAGE_KEYS.DRAFT) || {};\n  }\n\n  // Sync management\n  markForSync(entryId: string, action: 'create' | 'update' | 'delete' = 'update'): void {\n    const pendingSync = this.getPendingSync();\n    pendingSync[entryId] = {\n      action,\n      timestamp: Date.now()\n    };\n    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);\n  }\n\n  getPendingSync(): Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }> {\n    return this.getItem<Record<string, { action: 'create' | 'update' | 'delete'; timestamp: number }>>(STORAGE_KEYS.PENDING_SYNC) || {};\n  }\n\n  clearPendingSync(entryId: string): void {\n    const pendingSync = this.getPendingSync();\n    delete pendingSync[entryId];\n    this.setItem(STORAGE_KEYS.PENDING_SYNC, pendingSync);\n  }\n\n  setLastSyncTime(timestamp: number): void {\n    this.setItem(STORAGE_KEYS.LAST_SYNC, timestamp);\n  }\n\n  getLastSyncTime(): number | null {\n    return this.getItem<number>(STORAGE_KEYS.LAST_SYNC);\n  }\n\n  // Search functionality\n  searchEntries(query: string, tags?: string[]): JournalEntry[] {\n    const entries = this.getEntries();\n    const lowercaseQuery = query.toLowerCase();\n\n    return entries.filter(entry => {\n      // Text search\n      const titleMatch = entry.title.toLowerCase().includes(lowercaseQuery);\n      const contentMatch = entry.content.toLowerCase().includes(lowercaseQuery);\n      const textMatch = titleMatch || contentMatch;\n\n      // Tag filter\n      const tagMatch = !tags || tags.length === 0 || \n        tags.every(tag => entry.tags.includes(tag));\n\n      return textMatch && tagMatch;\n    }).sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());\n  }\n\n  // Utility methods\n  getStorageUsage(): { used: number; available: number } {\n    if (!this.isLocalStorageAvailable()) {\n      return { used: 0, available: 0 };\n    }\n\n    let used = 0;\n    for (const key in localStorage) {\n      if (localStorage.hasOwnProperty(key)) {\n        used += localStorage[key].length;\n      }\n    }\n\n    // Estimate available space (most browsers have ~5-10MB limit)\n    const estimated = 5 * 1024 * 1024; // 5MB\n    return {\n      used,\n      available: Math.max(0, estimated - used)\n    };\n  }\n\n  clearAllData(): void {\n    Object.values(STORAGE_KEYS).forEach(key => {\n      this.removeItem(key);\n    });\n  }\n\n  // Export/Import functionality\n  exportData(): {\n    entries: JournalEntry[];\n    tags: Tag[];\n    preferences: UserPreferences | null;\n    exportDate: string;\n  } {\n    return {\n      entries: this.getEntries(),\n      tags: this.getTags(),\n      preferences: this.getPreferences(),\n      exportDate: new Date().toISOString()\n    };\n  }\n\n  importData(data: {\n    entries?: JournalEntry[];\n    tags?: Tag[];\n    preferences?: UserPreferences;\n  }): void {\n    if (data.entries) {\n      this.saveEntries(data.entries);\n    }\n    if (data.tags) {\n      this.saveTags(data.tags);\n    }\n    if (data.preferences) {\n      this.savePreferences(data.preferences);\n    }\n  }\n\n  // Auto-cleanup old drafts (older than 7 days)\n  cleanupOldDrafts(): void {\n    const drafts = this.getDrafts();\n    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);\n    \n    let hasChanges = false;\n    Object.keys(drafts).forEach(entryId => {\n      if (drafts[entryId].timestamp < sevenDaysAgo) {\n        delete drafts[entryId];\n        hasChanges = true;\n      }\n    });\n\n    if (hasChanges) {\n      this.setItem(STORAGE_KEYS.DRAFT, drafts);\n    }\n  }\n}\n\n// Create a singleton instance\nexport const localStorageManager = new LocalStorageManager();\n\n// Auto-cleanup on initialization\nlocalStorageManager.cleanupOldDrafts();\n"], "names": [], "mappings": ";;;;AAEA,qBAAqB;AACrB,MAAM,eAAe;IACnB,SAAS;IACT,MAAM;IACN,aAAa;IACb,OAAO;IACP,cAAc;IACd,WAAW;AACb;AAGO,MAAM;IAEX,qCAAqC;IAC7B,0BAAmC;QACzC,IAAI;YACF,MAAM,OAAO;YACb,aAAa,OAAO,CAAC,MAAM;YAC3B,aAAa,UAAU,CAAC;YACxB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,0BAA0B;IAClB,QAAW,GAAW,EAAE,KAAQ,EAAQ;QAC9C,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;QAErC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEQ,QAAW,GAAW,EAAY;QACxC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,OAAO;QAE5C,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEQ,WAAW,GAAW,EAAQ;QACpC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;QAErC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,6BAA6B;IAC7B,YAAY,OAAuB,EAAQ;QACzC,IAAI,CAAC,OAAO,CAAC,aAAa,OAAO,EAAE;IACrC;IAEA,aAA6B;QAC3B,MAAM,UAAU,IAAI,CAAC,OAAO,CAAiB,aAAa,OAAO;QACjE,OAAO,WAAW,EAAE;IACtB;IAEA,UAAU,KAAmB,EAAQ;QACnC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAE9D,IAAI,iBAAiB,GAAG;YACtB,OAAO,CAAC,cAAc,GAAG;QAC3B,OAAO;YACL,QAAQ,OAAO,CAAC,QAAQ,mCAAmC;QAC7D;QAEA,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;IAC3B;IAEA,YAAY,OAAe,EAAQ;QACjC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,WAAW,CAAC,SAAS;IAC5B;IAEA,SAAS,OAAe,EAAuB;QAC7C,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,OAAO,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY;IAChD;IAEA,kBAAkB;IAClB,SAAS,IAAW,EAAQ;QAC1B,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,EAAE;IAClC;IAEA,UAAiB;QACf,MAAM,OAAO,IAAI,CAAC,OAAO,CAAQ,aAAa,IAAI;QAClD,OAAO,QAAQ,EAAE;IACnB;IAEA,QAAQ,GAAQ,EAAQ;QACtB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,MAAM,gBAAgB,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,EAAE;QAEzD,IAAI,iBAAiB,GAAG;YACtB,IAAI,CAAC,cAAc,GAAG;QACxB,OAAO;YACL,KAAK,IAAI,CAAC;QACZ;QAEA,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,8BAA8B;IAC9B,gBAAgB,WAA4B,EAAQ;QAClD,IAAI,CAAC,OAAO,CAAC,aAAa,WAAW,EAAE;IACzC;IAEA,iBAAyC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAkB,aAAa,WAAW;IAC/D;IAEA,iCAAiC;IACjC,UAAU,OAAe,EAAE,OAAe,EAAQ;QAChD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,CAAC,QAAQ,GAAG;YAChB;YACA,WAAW,KAAK,GAAG;QACrB;QACA,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;IACnC;IAEA,SAAS,OAAe,EAAiD;QACvE,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,MAAM,CAAC,QAAQ,IAAI;IAC5B;IAEA,WAAW,OAAe,EAAQ;QAChC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,MAAM,CAAC,QAAQ;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;IACnC;IAEQ,YAAoE;QAC1E,OAAO,IAAI,CAAC,OAAO,CAAyD,aAAa,KAAK,KAAK,CAAC;IACtG;IAEA,kBAAkB;IAClB,YAAY,OAAe,EAAE,SAAyC,QAAQ,EAAQ;QACpF,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,WAAW,CAAC,QAAQ,GAAG;YACrB;YACA,WAAW,KAAK,GAAG;QACrB;QACA,IAAI,CAAC,OAAO,CAAC,aAAa,YAAY,EAAE;IAC1C;IAEA,iBAAgG;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAgF,aAAa,YAAY,KAAK,CAAC;IACpI;IAEA,iBAAiB,OAAe,EAAQ;QACtC,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,WAAW,CAAC,QAAQ;QAC3B,IAAI,CAAC,OAAO,CAAC,aAAa,YAAY,EAAE;IAC1C;IAEA,gBAAgB,SAAiB,EAAQ;QACvC,IAAI,CAAC,OAAO,CAAC,aAAa,SAAS,EAAE;IACvC;IAEA,kBAAiC;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAS,aAAa,SAAS;IACpD;IAEA,uBAAuB;IACvB,cAAc,KAAa,EAAE,IAAe,EAAkB;QAC5D,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,iBAAiB,MAAM,WAAW;QAExC,OAAO,QAAQ,MAAM,CAAC,CAAA;YACpB,cAAc;YACd,MAAM,aAAa,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;YACtD,MAAM,eAAe,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;YAC1D,MAAM,YAAY,cAAc;YAEhC,aAAa;YACb,MAAM,WAAW,CAAC,QAAQ,KAAK,MAAM,KAAK,KACxC,KAAK,KAAK,CAAC,CAAA,MAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;YAExC,OAAO,aAAa;QACtB,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;IAC/D;IAEA,kBAAkB;IAClB,kBAAuD;QACrD,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;YACnC,OAAO;gBAAE,MAAM;gBAAG,WAAW;YAAE;QACjC;QAEA,IAAI,OAAO;QACX,IAAK,MAAM,OAAO,aAAc;YAC9B,IAAI,aAAa,cAAc,CAAC,MAAM;gBACpC,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;YAClC;QACF;QAEA,8DAA8D;QAC9D,MAAM,YAAY,IAAI,OAAO,MAAM,MAAM;QACzC,OAAO;YACL;YACA,WAAW,KAAK,GAAG,CAAC,GAAG,YAAY;QACrC;IACF;IAEA,eAAqB;QACnB,OAAO,MAAM,CAAC,cAAc,OAAO,CAAC,CAAA;YAClC,IAAI,CAAC,UAAU,CAAC;QAClB;IACF;IAEA,8BAA8B;IAC9B,aAKE;QACA,OAAO;YACL,SAAS,IAAI,CAAC,UAAU;YACxB,MAAM,IAAI,CAAC,OAAO;YAClB,aAAa,IAAI,CAAC,cAAc;YAChC,YAAY,IAAI,OAAO,WAAW;QACpC;IACF;IAEA,WAAW,IAIV,EAAQ;QACP,IAAI,KAAK,OAAO,EAAE;YAChB,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,IAAI,EAAE;YACb,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;QACzB;QACA,IAAI,KAAK,WAAW,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC,KAAK,WAAW;QACvC;IACF;IAEA,8CAA8C;IAC9C,mBAAyB;QACvB,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,eAAe,KAAK,GAAG,KAAM,IAAI,KAAK,KAAK,KAAK;QAEtD,IAAI,aAAa;QACjB,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;YAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,GAAG,cAAc;gBAC5C,OAAO,MAAM,CAAC,QAAQ;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,YAAY;YACd,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,EAAE;QACnC;IACF;AACF;AAGO,MAAM,sBAAsB,IAAI;AAEvC,iCAAiC;AACjC,oBAAoB,gBAAgB", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/ThemeProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { UserPreferences } from '@/lib/types';\nimport { localStorageManager } from '@/lib/localStorage';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  actualTheme: 'light' | 'dark';\n  setTheme: (theme: Theme) => void;\n  preferences: UserPreferences | null;\n  updatePreferences: (updates: Partial<UserPreferences>) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  defaultTheme?: Theme;\n}\n\nexport function ThemeProvider({ children, defaultTheme = 'system' }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<Theme>(defaultTheme);\n  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');\n  const [preferences, setPreferences] = useState<UserPreferences | null>(null);\n  const [mounted, setMounted] = useState(false);\n\n  // Load preferences from localStorage on mount\n  useEffect(() => {\n    const savedPreferences = localStorageManager.getPreferences();\n    if (savedPreferences) {\n      setPreferences(savedPreferences);\n      setThemeState(savedPreferences.theme);\n    } else {\n      // Create default preferences\n      const defaultPreferences: UserPreferences = {\n        id: 'default',\n        theme: 'system',\n        fontSize: 16,\n        fontFamily: 'Inter',\n        spellCheckEnabled: true,\n        autoSaveInterval: 2000\n      };\n      setPreferences(defaultPreferences);\n      localStorageManager.savePreferences(defaultPreferences);\n    }\n    setMounted(true);\n  }, []);\n\n  // Update actual theme based on theme setting and system preference\n  useEffect(() => {\n    if (!mounted) return;\n\n    const updateActualTheme = () => {\n      if (theme === 'system') {\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        setActualTheme(systemTheme);\n      } else {\n        setActualTheme(theme);\n      }\n    };\n\n    updateActualTheme();\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateActualTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme, mounted]);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = window.document.documentElement;\n    \n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(actualTheme);\n    \n    // Update meta theme-color for mobile browsers\n    const metaThemeColor = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaThemeColor) {\n      metaThemeColor.setAttribute('content', actualTheme === 'dark' ? '#1f2937' : '#ffffff');\n    }\n  }, [actualTheme, mounted]);\n\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    \n    // Update preferences\n    if (preferences) {\n      const updatedPreferences = { ...preferences, theme: newTheme };\n      setPreferences(updatedPreferences);\n      localStorageManager.savePreferences(updatedPreferences);\n    }\n  };\n\n  const updatePreferences = (updates: Partial<UserPreferences>) => {\n    if (!preferences) return;\n    \n    const updatedPreferences = { ...preferences, ...updates };\n    setPreferences(updatedPreferences);\n    localStorageManager.savePreferences(updatedPreferences);\n    \n    // If theme was updated, update theme state\n    if (updates.theme) {\n      setThemeState(updates.theme);\n    }\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    actualTheme,\n    setTheme,\n    preferences,\n    updatePreferences\n  };\n\n  // Don't render until mounted to avoid hydration mismatch\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\n// Theme toggle component\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nexport function ThemeToggle({ className = '' }: ThemeToggleProps) {\n  const { theme, setTheme } = useTheme();\n\n  const themes: { value: Theme; label: string; icon: string }[] = [\n    { value: 'light', label: 'Light', icon: '☀️' },\n    { value: 'dark', label: 'Dark', icon: '🌙' },\n    { value: 'system', label: 'System', icon: '💻' }\n  ];\n\n  return (\n    <div className={`flex items-center space-x-1 ${className}`}>\n      {themes.map(({ value, label, icon }) => (\n        <button\n          key={value}\n          onClick={() => setTheme(value)}\n          className={`\n            flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors\n            ${theme === value\n              ? 'bg-blue-600 text-white'\n              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n            }\n          `}\n          title={`Switch to ${label.toLowerCase()} theme`}\n        >\n          <span>{icon}</span>\n          <span className=\"hidden sm:inline\">{label}</span>\n        </button>\n      ))}\n    </div>\n  );\n}\n\n// Hook to get system theme preference\nexport function useSystemTheme() {\n  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const updateSystemTheme = () => {\n      setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n    };\n\n    updateSystemTheme();\n    mediaQuery.addEventListener('change', updateSystemTheme);\n    \n    return () => mediaQuery.removeEventListener('change', updateSystemTheme);\n  }, []);\n\n  return systemTheme;\n}\n\n// Hook to detect if user prefers reduced motion\nexport function usePrefersReducedMotion() {\n  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    \n    const updatePreference = () => {\n      setPrefersReducedMotion(mediaQuery.matches);\n    };\n\n    updatePreference();\n    mediaQuery.addEventListener('change', updatePreference);\n    \n    return () => mediaQuery.removeEventListener('change', updatePreference);\n  }, []);\n\n  return prefersReducedMotion;\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAO1D,SAAS,cAAc,EAAE,QAAQ,EAAE,eAAe,QAAQ,EAAsB;;IACrF,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,mBAAmB,6HAAA,CAAA,sBAAmB,CAAC,cAAc;YAC3D,IAAI,kBAAkB;gBACpB,eAAe;gBACf,cAAc,iBAAiB,KAAK;YACtC,OAAO;gBACL,6BAA6B;gBAC7B,MAAM,qBAAsC;oBAC1C,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,mBAAmB;oBACnB,kBAAkB;gBACpB;gBACA,eAAe;gBACf,6HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACtC;YACA,WAAW;QACb;kCAAG,EAAE;IAEL,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM;6DAAoB;oBACxB,IAAI,UAAU,UAAU;wBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;wBACzF,eAAe;oBACjB,OAAO;wBACL,eAAe;oBACjB;gBACF;;YAEA;YAEA,kCAAkC;YAClC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;wDAAe;oBACnB,IAAI,UAAU,UAAU;wBACtB;oBACF;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;QAAO;KAAQ;IAEnB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;YAE5C,gCAAgC;YAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAE/B,0BAA0B;YAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;YAEnB,8CAA8C;YAC9C,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,eAAe,YAAY,CAAC,WAAW,gBAAgB,SAAS,YAAY;YAC9E;QACF;kCAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,WAAW,CAAC;QAChB,cAAc;QAEd,qBAAqB;QACrB,IAAI,aAAa;YACf,MAAM,qBAAqB;gBAAE,GAAG,WAAW;gBAAE,OAAO;YAAS;YAC7D,eAAe;YACf,6HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QACtC;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,aAAa;QAElB,MAAM,qBAAqB;YAAE,GAAG,WAAW;YAAE,GAAG,OAAO;QAAC;QACxD,eAAe;QACf,6HAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;QAEpC,2CAA2C;QAC3C,IAAI,QAAQ,KAAK,EAAE;YACjB,cAAc,QAAQ,KAAK;QAC7B;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;IACF;IAEA,yDAAyD;IACzD,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GApHgB;KAAA;AAsHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAaT,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;;IAC9D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,MAAM,SAA0D;QAC9D;YAAE,OAAO;YAAS,OAAO;YAAS,MAAM;QAAK;QAC7C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM;QAAK;QAC3C;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM;QAAK;KAChD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;kBACvD,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,iBACjC,6LAAC;gBAEC,SAAS,IAAM,SAAS;gBACxB,WAAW,CAAC;;YAEV,EAAE,UAAU,QACR,2BACA,4EACH;UACH,CAAC;gBACD,OAAO,CAAC,UAAU,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC;;kCAE/C,6LAAC;kCAAM;;;;;;kCACP,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;eAZ/B;;;;;;;;;;AAiBf;IA9BgB;;QACc;;;MADd;AAiCT,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;8DAAoB;oBACxB,eAAe,WAAW,OAAO,GAAG,SAAS;gBAC/C;;YAEA;YACA,WAAW,gBAAgB,CAAC,UAAU;YAEtC;4CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;mCAAG,EAAE;IAEL,OAAO;AACT;IAjBgB;AAoBT,SAAS;;IACd,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;sEAAmB;oBACvB,wBAAwB,WAAW,OAAO;gBAC5C;;YAEA;YACA,WAAW,gBAAgB,CAAC,UAAU;YAEtC;qDAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;4CAAG,EAAE;IAEL,OAAO;AACT;IAjBgB", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}