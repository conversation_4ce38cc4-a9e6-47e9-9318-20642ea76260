import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, JournalEntry } from '@/lib/database';

// GET /api/entries - Get all entries with optional search and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || [];
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const db = getDatabase();
    
    let entries: JournalEntry[];
    
    if (query || tags.length > 0) {
      entries = db.searchEntries(query, tags);
    } else {
      entries = db.getAllEntries(limit, offset);
    }

    return NextResponse.json({ entries });
  } catch (error) {
    console.error('Error fetching entries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch entries' },
      { status: 500 }
    );
  }
}

// POST /api/entries - Create a new entry
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, content, tags } = body;

    if (!title && !content) {
      return NextResponse.json(
        { error: 'Title or content is required' },
        { status: 400 }
      );
    }

    const db = getDatabase();
    const entryId = db.createEntry({
      title: title || 'Untitled Entry',
      content: content || '',
      tags: tags || [],
      word_count: 0, // Will be calculated in the database
    });

    const entry = db.getEntry(entryId);
    
    return NextResponse.json({ entry }, { status: 201 });
  } catch (error) {
    console.error('Error creating entry:', error);
    return NextResponse.json(
      { error: 'Failed to create entry' },
      { status: 500 }
    );
  }
}
