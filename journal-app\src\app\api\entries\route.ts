import { NextRequest, NextResponse } from 'next/server';
import { JournalEntry, ApiResponse, PaginatedResponse } from '@/lib/types';
import JournalDatabase from '@/lib/database';

// Initialize database (in production, this would be a singleton)
let db: JournalDatabase;

function getDatabase() {
  if (!db) {
    db = new JournalDatabase();
  }
  return db;
}

// GET /api/entries - Get all entries with pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const tags = searchParams.get('tags')?.split(',').filter(Boolean);
    
    const database = getDatabase();
    
    let entries: JournalEntry[];
    
    if (search || tags) {
      // Search entries
      entries = database.searchEntries(search || '', tags);
    } else {
      // Get all entries with pagination
      const offset = (page - 1) * limit;
      entries = database.getAllEntries(limit, offset);
    }
    
    // Get total count for pagination
    const allEntries = database.getAllEntries();
    const total = allEntries.length;
    
    const response: PaginatedResponse<JournalEntry> = {
      data: entries,
      total,
      page,
      limit,
      hasMore: page * limit < total
    };
    
    return NextResponse.json({
      success: true,
      data: response
    } as ApiResponse<PaginatedResponse<JournalEntry>>);
    
  } catch (error) {
    console.error('Error fetching entries:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch entries'
    } as ApiResponse<never>, { status: 500 });
  }
}

// POST /api/entries - Create a new entry
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, content, tags = [] } = body;
    
    if (!title && !content) {
      return NextResponse.json({
        success: false,
        error: 'Title or content is required'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    
    const newEntry = database.createEntry({
      title: title || 'Untitled',
      content: content || '',
      tags: Array.isArray(tags) ? tags : []
    });
    
    return NextResponse.json({
      success: true,
      data: newEntry
    } as ApiResponse<JournalEntry>, { status: 201 });
    
  } catch (error) {
    console.error('Error creating entry:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create entry'
    } as ApiResponse<never>, { status: 500 });
  }
}

// PUT /api/entries - Bulk update entries (for sync)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { entries } = body;
    
    if (!Array.isArray(entries)) {
      return NextResponse.json({
        success: false,
        error: 'Entries must be an array'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    const updatedEntries: JournalEntry[] = [];
    
    for (const entryData of entries) {
      try {
        if (entryData.id) {
          // Update existing entry
          const updated = database.updateEntry(entryData.id, entryData);
          if (updated) {
            updatedEntries.push(updated);
          }
        } else {
          // Create new entry
          const created = database.createEntry(entryData);
          updatedEntries.push(created);
        }
      } catch (entryError) {
        console.error('Error processing entry:', entryError);
        // Continue with other entries
      }
    }
    
    return NextResponse.json({
      success: true,
      data: updatedEntries
    } as ApiResponse<JournalEntry[]>);
    
  } catch (error) {
    console.error('Error bulk updating entries:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update entries'
    } as ApiResponse<never>, { status: 500 });
  }
}
