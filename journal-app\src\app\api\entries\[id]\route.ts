import { NextRequest, NextResponse } from 'next/server';
import { JournalEntry, ApiResponse } from '@/lib/types';
import JournalDatabase from '@/lib/database';

// Initialize database (in production, this would be a singleton)
let db: JournalDatabase;

function getDatabase() {
  if (!db) {
    db = new JournalDatabase();
  }
  return db;
}

// GET /api/entries/[id] - Get a specific entry
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Entry ID is required'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    const entry = database.getEntry(id);
    
    if (!entry) {
      return NextResponse.json({
        success: false,
        error: 'Entry not found'
      } as ApiResponse<never>, { status: 404 });
    }
    
    return NextResponse.json({
      success: true,
      data: entry
    } as ApiResponse<JournalEntry>);
    
  } catch (error) {
    console.error('Error fetching entry:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch entry'
    } as ApiResponse<never>, { status: 500 });
  }
}

// PUT /api/entries/[id] - Update a specific entry
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Entry ID is required'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    const existingEntry = database.getEntry(id);
    
    if (!existingEntry) {
      return NextResponse.json({
        success: false,
        error: 'Entry not found'
      } as ApiResponse<never>, { status: 404 });
    }
    
    const updatedEntry = database.updateEntry(id, body);
    
    if (!updatedEntry) {
      return NextResponse.json({
        success: false,
        error: 'Failed to update entry'
      } as ApiResponse<never>, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      data: updatedEntry
    } as ApiResponse<JournalEntry>);
    
  } catch (error) {
    console.error('Error updating entry:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update entry'
    } as ApiResponse<never>, { status: 500 });
  }
}

// DELETE /api/entries/[id] - Delete a specific entry
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Entry ID is required'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    const existingEntry = database.getEntry(id);
    
    if (!existingEntry) {
      return NextResponse.json({
        success: false,
        error: 'Entry not found'
      } as ApiResponse<never>, { status: 404 });
    }
    
    const deleted = database.deleteEntry(id);
    
    if (!deleted) {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete entry'
      } as ApiResponse<never>, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      data: { id, deleted: true }
    } as ApiResponse<{ id: string; deleted: boolean }>);
    
  } catch (error) {
    console.error('Error deleting entry:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete entry'
    } as ApiResponse<never>, { status: 500 });
  }
}
