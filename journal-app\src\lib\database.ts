import Database from 'better-sqlite3';
import { join } from 'path';

export interface JournalEntry {
  id: string;
  title: string;
  content: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  word_count: number;
}

export interface Tag {
  id: string;
  name: string;
  color?: string;
  created_at: string;
}

class JournalDatabase {
  private db: Database.Database | null = null;

  constructor() {
    this.initDatabase();
  }

  private initDatabase() {
    try {
      // For development, use a local SQLite file
      const dbPath = join(process.cwd(), 'journal.db');
      this.db = new Database(dbPath);
      
      // Enable WAL mode for better performance
      this.db.pragma('journal_mode = WAL');
      
      this.createTables();
    } catch (error) {
      console.error('Failed to initialize database:', error);
    }
  }

  private createTables() {
    if (!this.db) return;

    // Create entries table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS entries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        word_count INTEGER DEFAULT 0
      )
    `);

    // Create tags table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL
      )
    `);

    // Create entry_tags junction table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS entry_tags (
        entry_id TEXT,
        tag_id TEXT,
        PRIMARY KEY (entry_id, tag_id),
        FOREIGN KEY (entry_id) REFERENCES entries(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_entries_created_at ON entries(created_at);
      CREATE INDEX IF NOT EXISTS idx_entries_updated_at ON entries(updated_at);
      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
    `);
  }

  // Entry operations
  createEntry(entry: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'>): string {
    if (!this.db) throw new Error('Database not initialized');

    const id = crypto.randomUUID();
    const now = new Date().toISOString();
    const wordCount = this.countWords(entry.content);

    const stmt = this.db.prepare(`
      INSERT INTO entries (id, title, content, created_at, updated_at, word_count)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    stmt.run(id, entry.title, entry.content, now, now, wordCount);

    // Add tags
    if (entry.tags.length > 0) {
      this.addTagsToEntry(id, entry.tags);
    }

    return id;
  }

  updateEntry(id: string, updates: Partial<Pick<JournalEntry, 'title' | 'content' | 'tags'>>): void {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.title !== undefined) {
      fields.push('title = ?');
      values.push(updates.title);
    }

    if (updates.content !== undefined) {
      fields.push('content = ?', 'word_count = ?');
      values.push(updates.content, this.countWords(updates.content));
    }

    if (fields.length > 0) {
      fields.push('updated_at = ?');
      values.push(now, id);

      const stmt = this.db.prepare(`
        UPDATE entries SET ${fields.join(', ')} WHERE id = ?
      `);
      stmt.run(...values);
    }

    // Update tags if provided
    if (updates.tags !== undefined) {
      this.updateEntryTags(id, updates.tags);
    }
  }

  getEntry(id: string): JournalEntry | null {
    if (!this.db) return null;

    const stmt = this.db.prepare(`
      SELECT * FROM entries WHERE id = ?
    `);
    const entry = stmt.get(id) as any;

    if (!entry) return null;

    const tags = this.getEntryTags(id);
    return { ...entry, tags };
  }

  getAllEntries(limit = 50, offset = 0): JournalEntry[] {
    if (!this.db) return [];

    const stmt = this.db.prepare(`
      SELECT * FROM entries 
      ORDER BY updated_at DESC 
      LIMIT ? OFFSET ?
    `);
    const entries = stmt.all(limit, offset) as any[];

    return entries.map(entry => ({
      ...entry,
      tags: this.getEntryTags(entry.id)
    }));
  }

  searchEntries(query: string, tags: string[] = []): JournalEntry[] {
    if (!this.db) return [];

    let sql = `
      SELECT DISTINCT e.* FROM entries e
    `;
    const params: any[] = [];

    if (tags.length > 0) {
      sql += `
        JOIN entry_tags et ON e.id = et.entry_id
        JOIN tags t ON et.tag_id = t.id
      `;
    }

    const conditions: string[] = [];

    if (query.trim()) {
      conditions.push('(e.title LIKE ? OR e.content LIKE ?)');
      const searchTerm = `%${query}%`;
      params.push(searchTerm, searchTerm);
    }

    if (tags.length > 0) {
      conditions.push(`t.name IN (${tags.map(() => '?').join(', ')})`);
      params.push(...tags);
    }

    if (conditions.length > 0) {
      sql += ` WHERE ${conditions.join(' AND ')}`;
    }

    sql += ' ORDER BY e.updated_at DESC';

    const stmt = this.db.prepare(sql);
    const entries = stmt.all(...params) as any[];

    return entries.map(entry => ({
      ...entry,
      tags: this.getEntryTags(entry.id)
    }));
  }

  deleteEntry(id: string): void {
    if (!this.db) return;

    const stmt = this.db.prepare('DELETE FROM entries WHERE id = ?');
    stmt.run(id);
  }

  // Tag operations
  createTag(name: string, color?: string): string {
    if (!this.db) throw new Error('Database not initialized');

    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = this.db.prepare(`
      INSERT OR IGNORE INTO tags (id, name, color, created_at)
      VALUES (?, ?, ?, ?)
    `);

    stmt.run(id, name, color, now);
    return id;
  }

  getAllTags(): Tag[] {
    if (!this.db) return [];

    const stmt = this.db.prepare('SELECT * FROM tags ORDER BY name');
    return stmt.all() as Tag[];
  }

  private getEntryTags(entryId: string): string[] {
    if (!this.db) return [];

    const stmt = this.db.prepare(`
      SELECT t.name FROM tags t
      JOIN entry_tags et ON t.id = et.tag_id
      WHERE et.entry_id = ?
    `);
    const tags = stmt.all(entryId) as { name: string }[];
    return tags.map(t => t.name);
  }

  private addTagsToEntry(entryId: string, tagNames: string[]): void {
    if (!this.db) return;

    for (const tagName of tagNames) {
      // Create tag if it doesn't exist
      this.createTag(tagName);

      // Get tag ID
      const tagStmt = this.db.prepare('SELECT id FROM tags WHERE name = ?');
      const tag = tagStmt.get(tagName) as { id: string } | undefined;

      if (tag) {
        // Link tag to entry
        const linkStmt = this.db.prepare(`
          INSERT OR IGNORE INTO entry_tags (entry_id, tag_id)
          VALUES (?, ?)
        `);
        linkStmt.run(entryId, tag.id);
      }
    }
  }

  private updateEntryTags(entryId: string, tagNames: string[]): void {
    if (!this.db) return;

    // Remove all existing tags for this entry
    const deleteStmt = this.db.prepare('DELETE FROM entry_tags WHERE entry_id = ?');
    deleteStmt.run(entryId);

    // Add new tags
    if (tagNames.length > 0) {
      this.addTagsToEntry(entryId, tagNames);
    }
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Singleton instance
let dbInstance: JournalDatabase | null = null;

export function getDatabase(): JournalDatabase {
  if (!dbInstance) {
    dbInstance = new JournalDatabase();
  }
  return dbInstance;
}

export default JournalDatabase;
