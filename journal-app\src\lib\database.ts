import Database from 'better-sqlite3';
import { JournalEntry, Tag, UserPreferences, JournalEntryRow, TagRow, UserPreferencesRow } from './types';
import { v4 as uuidv4 } from 'uuid';

class JournalDatabase {
  private db: Database.Database;

  constructor(dbPath: string = 'journal.db') {
    this.db = new Database(dbPath);
    this.initializeTables();
  }

  private initializeTables() {
    // Create journal entries table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        tags TEXT NOT NULL DEFAULT '[]',
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        attachments TEXT,
        word_count INTEGER DEFAULT 0
      )
    `);

    // Create tags table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL,
        usage_count INTEGER DEFAULT 0
      )
    `);

    // Create user preferences table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id TEXT PRIMARY KEY,
        theme TEXT DEFAULT 'system',
        font_size INTEGER DEFAULT 16,
        font_family TEXT DEFAULT 'Inter',
        spell_check_enabled INTEGER DEFAULT 1,
        auto_save_interval INTEGER DEFAULT 2000,
        last_sync_at TEXT
      )
    `);

    // Create indexes for better performance
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_entries_created_at ON journal_entries(created_at);
      CREATE INDEX IF NOT EXISTS idx_entries_updated_at ON journal_entries(updated_at);
      CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);
    `);

    // Insert default preferences if none exist
    const prefsCount = this.db.prepare('SELECT COUNT(*) as count FROM user_preferences').get() as { count: number };
    if (prefsCount.count === 0) {
      this.createDefaultPreferences();
    }
  }

  private createDefaultPreferences() {
    const defaultPrefs: UserPreferencesRow = {
      id: uuidv4(),
      theme: 'system',
      font_size: 16,
      font_family: 'Inter',
      spell_check_enabled: 1,
      auto_save_interval: 2000,
      last_sync_at: null
    };

    this.db.prepare(`
      INSERT INTO user_preferences (id, theme, font_size, font_family, spell_check_enabled, auto_save_interval, last_sync_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(
      defaultPrefs.id,
      defaultPrefs.theme,
      defaultPrefs.font_size,
      defaultPrefs.font_family,
      defaultPrefs.spell_check_enabled,
      defaultPrefs.auto_save_interval,
      defaultPrefs.last_sync_at
    );
  }

  // Journal Entry operations
  createEntry(entry: Omit<JournalEntry, 'id' | 'createdAt' | 'updatedAt'>): JournalEntry {
    const id = uuidv4();
    const now = new Date().toISOString();
    const wordCount = this.countWords(entry.content);

    const newEntry: JournalEntry = {
      id,
      ...entry,
      createdAt: new Date(now),
      updatedAt: new Date(now),
      wordCount
    };

    this.db.prepare(`
      INSERT INTO journal_entries (id, title, content, tags, created_at, updated_at, attachments, word_count)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      entry.title,
      entry.content,
      JSON.stringify(entry.tags || []),
      now,
      now,
      JSON.stringify(entry.attachments || []),
      wordCount
    );

    // Update tag usage counts
    this.updateTagUsage(entry.tags || []);

    return newEntry;
  }

  updateEntry(id: string, updates: Partial<JournalEntry>): JournalEntry | null {
    const existing = this.getEntry(id);
    if (!existing) return null;

    const now = new Date().toISOString();
    const wordCount = updates.content ? this.countWords(updates.content) : existing.wordCount;

    const updatedEntry = {
      ...existing,
      ...updates,
      updatedAt: new Date(now),
      wordCount
    };

    this.db.prepare(`
      UPDATE journal_entries 
      SET title = ?, content = ?, tags = ?, updated_at = ?, attachments = ?, word_count = ?
      WHERE id = ?
    `).run(
      updatedEntry.title,
      updatedEntry.content,
      JSON.stringify(updatedEntry.tags),
      now,
      JSON.stringify(updatedEntry.attachments || []),
      wordCount,
      id
    );

    // Update tag usage counts
    if (updates.tags) {
      this.updateTagUsage(updates.tags);
    }

    return updatedEntry;
  }

  getEntry(id: string): JournalEntry | null {
    const row = this.db.prepare('SELECT * FROM journal_entries WHERE id = ?').get(id) as JournalEntryRow | undefined;
    return row ? this.rowToEntry(row) : null;
  }

  getAllEntries(limit?: number, offset?: number): JournalEntry[] {
    let query = 'SELECT * FROM journal_entries ORDER BY updated_at DESC';
    if (limit) {
      query += ` LIMIT ${limit}`;
      if (offset) {
        query += ` OFFSET ${offset}`;
      }
    }

    const rows = this.db.prepare(query).all() as JournalEntryRow[];
    return rows.map(row => this.rowToEntry(row));
  }

  deleteEntry(id: string): boolean {
    const result = this.db.prepare('DELETE FROM journal_entries WHERE id = ?').run(id);
    return result.changes > 0;
  }

  // Tag operations
  createTag(name: string, color?: string): Tag {
    const id = uuidv4();
    const now = new Date().toISOString();

    const newTag: Tag = {
      id,
      name,
      color,
      createdAt: new Date(now),
      usageCount: 0
    };

    this.db.prepare(`
      INSERT INTO tags (id, name, color, created_at, usage_count)
      VALUES (?, ?, ?, ?, ?)
    `).run(id, name, color, now, 0);

    return newTag;
  }

  getAllTags(): Tag[] {
    const rows = this.db.prepare('SELECT * FROM tags ORDER BY usage_count DESC, name ASC').all() as TagRow[];
    return rows.map(row => this.rowToTag(row));
  }

  // User preferences operations
  getPreferences(): UserPreferences {
    const row = this.db.prepare('SELECT * FROM user_preferences LIMIT 1').get() as UserPreferencesRow;
    return this.rowToPreferences(row);
  }

  updatePreferences(updates: Partial<UserPreferences>): UserPreferences {
    const existing = this.getPreferences();
    const updated = { ...existing, ...updates };

    this.db.prepare(`
      UPDATE user_preferences 
      SET theme = ?, font_size = ?, font_family = ?, spell_check_enabled = ?, auto_save_interval = ?
      WHERE id = ?
    `).run(
      updated.theme,
      updated.fontSize,
      updated.fontFamily,
      updated.spellCheckEnabled ? 1 : 0,
      updated.autoSaveInterval,
      existing.id
    );

    return updated;
  }

  // Search operations
  searchEntries(query: string, tags?: string[]): JournalEntry[] {
    let sql = `
      SELECT * FROM journal_entries 
      WHERE (title LIKE ? OR content LIKE ?)
    `;
    const params: any[] = [`%${query}%`, `%${query}%`];

    if (tags && tags.length > 0) {
      const tagConditions = tags.map(() => 'tags LIKE ?').join(' AND ');
      sql += ` AND (${tagConditions})`;
      tags.forEach(tag => params.push(`%"${tag}"%`));
    }

    sql += ' ORDER BY updated_at DESC';

    const rows = this.db.prepare(sql).all(...params) as JournalEntryRow[];
    return rows.map(row => this.rowToEntry(row));
  }

  // Helper methods
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private updateTagUsage(tags: string[]) {
    tags.forEach(tagName => {
      this.db.prepare(`
        INSERT INTO tags (id, name, created_at, usage_count) 
        VALUES (?, ?, ?, 1)
        ON CONFLICT(name) DO UPDATE SET usage_count = usage_count + 1
      `).run(uuidv4(), tagName, new Date().toISOString());
    });
  }

  private rowToEntry(row: JournalEntryRow): JournalEntry {
    return {
      id: row.id,
      title: row.title,
      content: row.content,
      tags: JSON.parse(row.tags),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      attachments: row.attachments ? JSON.parse(row.attachments) : undefined,
      wordCount: row.word_count
    };
  }

  private rowToTag(row: TagRow): Tag {
    return {
      id: row.id,
      name: row.name,
      color: row.color || undefined,
      createdAt: new Date(row.created_at),
      usageCount: row.usage_count
    };
  }

  private rowToPreferences(row: UserPreferencesRow): UserPreferences {
    return {
      id: row.id,
      theme: row.theme as 'light' | 'dark' | 'system',
      fontSize: row.font_size,
      fontFamily: row.font_family,
      spellCheckEnabled: row.spell_check_enabled === 1,
      autoSaveInterval: row.auto_save_interval,
      lastSyncAt: row.last_sync_at ? new Date(row.last_sync_at) : undefined
    };
  }

  close() {
    this.db.close();
  }
}

export default JournalDatabase;
