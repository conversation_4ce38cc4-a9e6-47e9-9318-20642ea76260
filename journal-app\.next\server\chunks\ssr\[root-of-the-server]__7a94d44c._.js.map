{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { getLocalStorage, Draft } from '@/lib/localStorage';\nimport { Save, Clock, AlertCircle } from 'lucide-react';\n\ninterface TextEditorProps {\n  entryId?: string;\n  initialTitle?: string;\n  initialContent?: string;\n  initialTags?: string[];\n  onSave?: (data: { title: string; content: string; tags: string[] }) => void;\n  onAutoSave?: (data: { title: string; content: string; tags: string[] }) => void;\n  className?: string;\n}\n\nexport function TextEditor({\n  entryId,\n  initialTitle = '',\n  initialContent = '',\n  initialTags = [],\n  onSave,\n  onAutoSave,\n  className = '',\n}: TextEditorProps) {\n  const [title, setTitle] = useState(initialTitle);\n  const [content, setContent] = useState(initialContent);\n  const [tags, setTags] = useState<string[]>(initialTags);\n  const [tagInput, setTagInput] = useState('');\n  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'unsaved' | 'error'>('saved');\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  \n  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();\n  const contentRef = useRef<HTMLTextAreaElement>(null);\n  const storage = getLocalStorage();\n  const settings = storage.getSettings();\n\n  // Calculate word and character counts\n  useEffect(() => {\n    const words = content.trim().split(/\\s+/).filter(word => word.length > 0).length;\n    setWordCount(content.trim() === '' ? 0 : words);\n    setCharCount(content.length);\n  }, [content]);\n\n  // Auto-save functionality\n  const performAutoSave = useCallback(() => {\n    if (!settings.autoSave) return;\n\n    const draftId = entryId || `draft_${Date.now()}`;\n    const draft: Draft = {\n      id: draftId,\n      title,\n      content,\n      tags,\n      lastModified: new Date().toISOString(),\n    };\n\n    try {\n      storage.saveDraft(draft);\n      setSaveStatus('saved');\n      \n      if (onAutoSave) {\n        onAutoSave({ title, content, tags });\n      }\n    } catch (error) {\n      console.error('Auto-save failed:', error);\n      setSaveStatus('error');\n    }\n  }, [title, content, tags, entryId, settings.autoSave, storage, onAutoSave]);\n\n  // Debounced auto-save\n  useEffect(() => {\n    if (title || content) {\n      setSaveStatus('unsaved');\n      \n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n\n      autoSaveTimeoutRef.current = setTimeout(() => {\n        setSaveStatus('saving');\n        performAutoSave();\n      }, settings.autoSaveInterval);\n    }\n\n    return () => {\n      if (autoSaveTimeoutRef.current) {\n        clearTimeout(autoSaveTimeoutRef.current);\n      }\n    };\n  }, [title, content, tags, performAutoSave, settings.autoSaveInterval]);\n\n  // Manual save\n  const handleManualSave = useCallback(() => {\n    if (onSave) {\n      onSave({ title, content, tags });\n      setSaveStatus('saved');\n    } else {\n      performAutoSave();\n    }\n  }, [title, content, tags, onSave, performAutoSave]);\n\n  // Tag management\n  const addTag = useCallback((tagName: string) => {\n    const trimmedTag = tagName.trim().toLowerCase();\n    if (trimmedTag && !tags.includes(trimmedTag)) {\n      setTags(prev => [...prev, trimmedTag]);\n    }\n  }, [tags]);\n\n  const removeTag = useCallback((tagToRemove: string) => {\n    setTags(prev => prev.filter(tag => tag !== tagToRemove));\n  }, []);\n\n  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter' || e.key === ',') {\n      e.preventDefault();\n      if (tagInput.trim()) {\n        addTag(tagInput);\n        setTagInput('');\n      }\n    } else if (e.key === 'Backspace' && !tagInput && tags.length > 0) {\n      removeTag(tags[tags.length - 1]);\n    }\n  };\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        handleManualSave();\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleManualSave]);\n\n  // Auto-resize textarea\n  const adjustTextareaHeight = useCallback(() => {\n    const textarea = contentRef.current;\n    if (textarea) {\n      textarea.style.height = 'auto';\n      textarea.style.height = `${textarea.scrollHeight}px`;\n    }\n  }, []);\n\n  useEffect(() => {\n    adjustTextareaHeight();\n  }, [content, adjustTextareaHeight]);\n\n  // Save status indicator\n  const SaveStatusIndicator = () => {\n    switch (saveStatus) {\n      case 'saving':\n        return (\n          <div className=\"flex items-center text-blue-600 dark:text-blue-400\">\n            <Clock className=\"w-4 h-4 mr-1 animate-spin\" />\n            <span className=\"text-sm\">Saving...</span>\n          </div>\n        );\n      case 'saved':\n        return (\n          <div className=\"flex items-center text-green-600 dark:text-green-400\">\n            <Save className=\"w-4 h-4 mr-1\" />\n            <span className=\"text-sm\">Saved</span>\n          </div>\n        );\n      case 'error':\n        return (\n          <div className=\"flex items-center text-red-600 dark:text-red-400\">\n            <AlertCircle className=\"w-4 h-4 mr-1\" />\n            <span className=\"text-sm\">Save failed</span>\n          </div>\n        );\n      default:\n        return (\n          <div className=\"flex items-center text-gray-500 dark:text-gray-400\">\n            <span className=\"text-sm\">Unsaved changes</span>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={`flex flex-col h-full ${className}`}>\n      {/* Header with title and save status */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n        <input\n          type=\"text\"\n          value={title}\n          onChange={(e) => setTitle(e.target.value)}\n          placeholder=\"Enter title...\"\n          className=\"flex-1 text-2xl font-bold bg-transparent border-none outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400\"\n          style={{ fontSize: `${settings.fontSize + 8}px` }}\n        />\n        <div className=\"flex items-center space-x-4\">\n          <SaveStatusIndicator />\n          <button\n            onClick={handleManualSave}\n            className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n          >\n            Save\n          </button>\n        </div>\n      </div>\n\n      {/* Tags section */}\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"flex flex-wrap items-center gap-2\">\n          {tags.map((tag) => (\n            <span\n              key={tag}\n              className=\"inline-flex items-center px-2 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full\"\n            >\n              #{tag}\n              <button\n                onClick={() => removeTag(tag)}\n                className=\"ml-1 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100\"\n              >\n                ×\n              </button>\n            </span>\n          ))}\n          <input\n            type=\"text\"\n            value={tagInput}\n            onChange={(e) => setTagInput(e.target.value)}\n            onKeyDown={handleTagInputKeyDown}\n            placeholder=\"Add tags...\"\n            className=\"px-2 py-1 text-sm bg-transparent border border-gray-300 dark:border-gray-600 rounded outline-none focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400\"\n          />\n        </div>\n      </div>\n\n      {/* Content editor */}\n      <div className=\"flex-1 p-4\">\n        <textarea\n          ref={contentRef}\n          value={content}\n          onChange={(e) => setContent(e.target.value)}\n          placeholder=\"Start writing your journal entry...\"\n          className=\"w-full h-full min-h-[400px] bg-transparent border-none outline-none resize-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 leading-relaxed\"\n          style={{ \n            fontSize: `${settings.fontSize}px`,\n            fontFamily: settings.fontFamily,\n          }}\n          spellCheck={settings.spellCheck}\n        />\n      </div>\n\n      {/* Footer with stats */}\n      <div className=\"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400\">\n        <div className=\"flex items-center space-x-4\">\n          <span>{wordCount} words</span>\n          <span>{charCount} characters</span>\n        </div>\n        <div className=\"text-xs\">\n          Press Ctrl+S (Cmd+S) to save manually\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAgBO,SAAS,WAAW,EACzB,OAAO,EACP,eAAe,EAAE,EACjB,iBAAiB,EAAE,EACnB,cAAc,EAAE,EAChB,MAAM,EACN,UAAU,EACV,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAChC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAC/C,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;IAC9B,MAAM,WAAW,QAAQ,WAAW;IAEpC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;QAChF,aAAa,QAAQ,IAAI,OAAO,KAAK,IAAI;QACzC,aAAa,QAAQ,MAAM;IAC7B,GAAG;QAAC;KAAQ;IAEZ,0BAA0B;IAC1B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,SAAS,QAAQ,EAAE;QAExB,MAAM,UAAU,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;QAChD,MAAM,QAAe;YACnB,IAAI;YACJ;YACA;YACA;YACA,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,IAAI;YACF,QAAQ,SAAS,CAAC;YAClB,cAAc;YAEd,IAAI,YAAY;gBACd,WAAW;oBAAE;oBAAO;oBAAS;gBAAK;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,cAAc;QAChB;IACF,GAAG;QAAC;QAAO;QAAS;QAAM;QAAS,SAAS,QAAQ;QAAE;QAAS;KAAW;IAE1E,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,SAAS;YACpB,cAAc;YAEd,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;YAEA,mBAAmB,OAAO,GAAG,WAAW;gBACtC,cAAc;gBACd;YACF,GAAG,SAAS,gBAAgB;QAC9B;QAEA,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,aAAa,mBAAmB,OAAO;YACzC;QACF;IACF,GAAG;QAAC;QAAO;QAAS;QAAM;QAAiB,SAAS,gBAAgB;KAAC;IAErE,cAAc;IACd,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,QAAQ;YACV,OAAO;gBAAE;gBAAO;gBAAS;YAAK;YAC9B,cAAc;QAChB,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAO;QAAS;QAAM;QAAQ;KAAgB;IAElD,iBAAiB;IACjB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,aAAa,QAAQ,IAAI,GAAG,WAAW;QAC7C,IAAI,cAAc,CAAC,KAAK,QAAQ,CAAC,aAAa;YAC5C,QAAQ,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QACvC;IACF,GAAG;QAAC;KAAK;IAET,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,QAAQ,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ;IAC7C,GAAG,EAAE;IAEL,MAAM,wBAAwB,CAAC;QAC7B,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;YACtC,EAAE,cAAc;YAChB,IAAI,SAAS,IAAI,IAAI;gBACnB,OAAO;gBACP,YAAY;YACd;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,YAAY,KAAK,MAAM,GAAG,GAAG;YAChE,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACjC;IACF;IAEA,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;KAAiB;IAErB,uBAAuB;IACvB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM,WAAW,WAAW,OAAO;QACnC,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC,MAAM,GAAG;YACxB,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,YAAY,CAAC,EAAE,CAAC;QACtD;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAS;KAAqB;IAElC,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;YAGhC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;YAGhC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;YAGhC;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;QAGlC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,aAAY;wBACZ,WAAU;wBACV,OAAO;4BAAE,UAAU,GAAG,SAAS,QAAQ,GAAG,EAAE,EAAE,CAAC;wBAAC;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;;;;0CACD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gCAEC,WAAU;;oCACX;oCACG;kDACF,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDACX;;;;;;;+BAPI;;;;;sCAYT,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4BAC3C,WAAW;4BACX,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,KAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oBAC1C,aAAY;oBACZ,WAAU;oBACV,OAAO;wBACL,UAAU,GAAG,SAAS,QAAQ,CAAC,EAAE,CAAC;wBAClC,YAAY,SAAS,UAAU;oBACjC;oBACA,YAAY,SAAS,UAAU;;;;;;;;;;;0BAKnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAM;oCAAU;;;;;;;0CACjB,8OAAC;;oCAAM;oCAAU;;;;;;;;;;;;;kCAEnB,8OAAC;wBAAI,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/TagManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Search, Tag, X, Plus } from 'lucide-react';\nimport { getLocalStorage } from '@/lib/localStorage';\n\ninterface TagManagerProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  onSearch?: (query: string, tags: string[]) => void;\n  className?: string;\n}\n\nexport function TagManager({ \n  selectedTags, \n  onTagsChange, \n  onSearch,\n  className = '' \n}: TagManagerProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [tagInput, setTagInput] = useState('');\n  const [availableTags, setAvailableTags] = useState<string[]>([]);\n  const [filteredTags, setFilteredTags] = useState<string[]>([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [isCreatingTag, setIsCreatingTag] = useState(false);\n  \n  const inputRef = useRef<HTMLInputElement>(null);\n  const suggestionsRef = useRef<HTMLDivElement>(null);\n  const storage = getLocalStorage();\n\n  // Load available tags from localStorage\n  useEffect(() => {\n    const cachedTags = storage.getCachedTags();\n    setAvailableTags(cachedTags);\n  }, [storage]);\n\n  // Filter tags based on input\n  useEffect(() => {\n    if (tagInput.trim()) {\n      const filtered = availableTags.filter(tag => \n        tag.toLowerCase().includes(tagInput.toLowerCase()) &&\n        !selectedTags.includes(tag)\n      );\n      setFilteredTags(filtered);\n      setShowSuggestions(true);\n    } else {\n      setFilteredTags([]);\n      setShowSuggestions(false);\n    }\n  }, [tagInput, availableTags, selectedTags]);\n\n  // Handle search\n  useEffect(() => {\n    if (onSearch) {\n      const timeoutId = setTimeout(() => {\n        onSearch(searchQuery, selectedTags);\n      }, 300); // Debounce search\n\n      return () => clearTimeout(timeoutId);\n    }\n  }, [searchQuery, selectedTags, onSearch]);\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        suggestionsRef.current &&\n        !suggestionsRef.current.contains(event.target as Node) &&\n        inputRef.current &&\n        !inputRef.current.contains(event.target as Node)\n      ) {\n        setShowSuggestions(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const addTag = (tagName: string) => {\n    const trimmedTag = tagName.trim().toLowerCase();\n    if (trimmedTag && !selectedTags.includes(trimmedTag)) {\n      const newTags = [...selectedTags, trimmedTag];\n      onTagsChange(newTags);\n      \n      // Add to available tags if it's new\n      if (!availableTags.includes(trimmedTag)) {\n        const updatedTags = [...availableTags, trimmedTag];\n        setAvailableTags(updatedTags);\n        storage.cacheTags(updatedTags);\n      }\n    }\n    setTagInput('');\n    setShowSuggestions(false);\n    setIsCreatingTag(false);\n  };\n\n  const removeTag = (tagToRemove: string) => {\n    const newTags = selectedTags.filter(tag => tag !== tagToRemove);\n    onTagsChange(newTags);\n  };\n\n  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (tagInput.trim()) {\n        if (filteredTags.length > 0) {\n          addTag(filteredTags[0]);\n        } else {\n          addTag(tagInput);\n        }\n      }\n    } else if (e.key === 'Escape') {\n      setShowSuggestions(false);\n      setIsCreatingTag(false);\n    } else if (e.key === 'Backspace' && !tagInput && selectedTags.length > 0) {\n      removeTag(selectedTags[selectedTags.length - 1]);\n    } else if (e.key === 'ArrowDown' && filteredTags.length > 0) {\n      e.preventDefault();\n      // Focus first suggestion (could be enhanced with keyboard navigation)\n    }\n  };\n\n  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (onSearch) {\n        onSearch(searchQuery, selectedTags);\n      }\n    }\n  };\n\n  const clearAllTags = () => {\n    onTagsChange([]);\n  };\n\n  const clearSearch = () => {\n    setSearchQuery('');\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Search Bar */}\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n          <input\n            type=\"text\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            onKeyDown={handleSearchKeyDown}\n            placeholder=\"Search entries...\"\n            className=\"w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400\"\n          />\n          {searchQuery && (\n            <button\n              onClick={clearSearch}\n              className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Selected Tags */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Active Filters\n            </h3>\n            <button\n              onClick={clearAllTags}\n              className=\"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300\"\n            >\n              Clear all\n            </button>\n          </div>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tag) => (\n              <span\n                key={tag}\n                className=\"inline-flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full\"\n              >\n                <Tag className=\"w-3 h-3 mr-1\" />\n                {tag}\n                <button\n                  onClick={() => removeTag(tag)}\n                  className=\"ml-2 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Tag Input */}\n      <div className=\"space-y-2\">\n        <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          Add Tags\n        </h3>\n        <div className=\"relative\">\n          <div className=\"relative\">\n            <Tag className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={tagInput}\n              onChange={(e) => setTagInput(e.target.value)}\n              onKeyDown={handleTagInputKeyDown}\n              onFocus={() => setShowSuggestions(true)}\n              placeholder=\"Type to add or search tags...\"\n              className=\"w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400\"\n            />\n            {tagInput && (\n              <button\n                onClick={() => addTag(tagInput)}\n                className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 dark:hover:text-blue-300\"\n              >\n                <Plus className=\"w-4 h-4\" />\n              </button>\n            )}\n          </div>\n\n          {/* Tag Suggestions */}\n          {showSuggestions && (filteredTags.length > 0 || tagInput.trim()) && (\n            <div\n              ref={suggestionsRef}\n              className=\"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto\"\n            >\n              {filteredTags.map((tag) => (\n                <button\n                  key={tag}\n                  onClick={() => addTag(tag)}\n                  className=\"w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 flex items-center\"\n                >\n                  <Tag className=\"w-3 h-3 mr-2 text-gray-400\" />\n                  {tag}\n                </button>\n              ))}\n              \n              {tagInput.trim() && !availableTags.includes(tagInput.toLowerCase()) && (\n                <button\n                  onClick={() => addTag(tagInput)}\n                  className=\"w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100 flex items-center border-t border-gray-200 dark:border-gray-600\"\n                >\n                  <Plus className=\"w-3 h-3 mr-2 text-green-500\" />\n                  Create \"{tagInput.toLowerCase()}\"\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Available Tags */}\n      {availableTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Available Tags\n          </h3>\n          <div className=\"flex flex-wrap gap-2 max-h-32 overflow-y-auto\">\n            {availableTags\n              .filter(tag => !selectedTags.includes(tag))\n              .slice(0, 20) // Limit display for performance\n              .map((tag) => (\n                <button\n                  key={tag}\n                  onClick={() => addTag(tag)}\n                  className=\"inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <Tag className=\"w-3 h-3 mr-1\" />\n                  {tag}\n                </button>\n              ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAaO,SAAS,WAAW,EACzB,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;IAE9B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,QAAQ,aAAa;QACxC,iBAAiB;IACnB,GAAG;QAAC;KAAQ;IAEZ,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,IAAI,IAAI;YACnB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,MACpC,IAAI,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,OAC/C,CAAC,aAAa,QAAQ,CAAC;YAEzB,gBAAgB;YAChB,mBAAmB;QACrB,OAAO;YACL,gBAAgB,EAAE;YAClB,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAU;QAAe;KAAa;IAE1C,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,YAAY,WAAW;gBAC3B,SAAS,aAAa;YACxB,GAAG,MAAM,kBAAkB;YAE3B,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAa;QAAc;KAAS;IAExC,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,eAAe,OAAO,IACtB,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC7C,SAAS,OAAO,IAChB,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACvC;gBACA,mBAAmB;YACrB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,SAAS,CAAC;QACd,MAAM,aAAa,QAAQ,IAAI,GAAG,WAAW;QAC7C,IAAI,cAAc,CAAC,aAAa,QAAQ,CAAC,aAAa;YACpD,MAAM,UAAU;mBAAI;gBAAc;aAAW;YAC7C,aAAa;YAEb,oCAAoC;YACpC,IAAI,CAAC,cAAc,QAAQ,CAAC,aAAa;gBACvC,MAAM,cAAc;uBAAI;oBAAe;iBAAW;gBAClD,iBAAiB;gBACjB,QAAQ,SAAS,CAAC;YACpB;QACF;QACA,YAAY;QACZ,mBAAmB;QACnB,iBAAiB;IACnB;IAEA,MAAM,YAAY,CAAC;QACjB,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,MAAO,QAAQ;QACnD,aAAa;IACf;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,SAAS,IAAI,IAAI;gBACnB,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,OAAO,YAAY,CAAC,EAAE;gBACxB,OAAO;oBACL,OAAO;gBACT;YACF;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,mBAAmB;YACnB,iBAAiB;QACnB,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,CAAC,YAAY,aAAa,MAAM,GAAG,GAAG;YACxE,UAAU,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QACjD,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,aAAa,MAAM,GAAG,GAAG;YAC3D,EAAE,cAAc;QAChB,sEAAsE;QACxE;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,UAAU;gBACZ,SAAS,aAAa;YACxB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,EAAE;IACjB;IAEA,MAAM,cAAc;QAClB,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAW;4BACX,aAAY;4BACZ,WAAU;;;;;;wBAEX,6BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAOpB,aAAa,MAAM,GAAG,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACd;kDACD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAW;wCACX,SAAS,IAAM,mBAAmB;wCAClC,aAAY;wCACZ,WAAU;;;;;;oCAEX,0BACC,8OAAC;wCACC,SAAS,IAAM,OAAO;wCACtB,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAMrB,mBAAmB,CAAC,aAAa,MAAM,GAAG,KAAK,SAAS,IAAI,EAAE,mBAC7D,8OAAC;gCACC,KAAK;gCACL,WAAU;;oCAET,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;4CAEC,SAAS,IAAM,OAAO;4CACtB,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CALI;;;;;oCASR,SAAS,IAAI,MAAM,CAAC,cAAc,QAAQ,CAAC,SAAS,WAAW,qBAC9D,8OAAC;wCACC,SAAS,IAAM,OAAO;wCACtB,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAgC;4CACvC,SAAS,WAAW;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAS3C,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAI,WAAU;kCACZ,cACE,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,QAAQ,CAAC,MACrC,KAAK,CAAC,GAAG,IAAI,gCAAgC;yBAC7C,GAAG,CAAC,CAAC,oBACJ,8OAAC;gCAEC,SAAS,IAAM,OAAO;gCACtB,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACd;;+BALI;;;;;;;;;;;;;;;;;;;;;;AAavB", "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, File, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';\nimport mammoth from 'mammoth';\n\ninterface UploadedFile {\n  id: string;\n  name: string;\n  size: number;\n  type: string;\n  content: string;\n  title: string;\n  status: 'processing' | 'success' | 'error';\n  error?: string;\n}\n\ninterface FileUploadProps {\n  onFilesProcessed: (files: UploadedFile[]) => void;\n  onImport: (file: UploadedFile) => void;\n  className?: string;\n}\n\nexport function FileUpload({ onFilesProcessed, onImport, className = '' }: FileUploadProps) {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const processTextFile = async (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        const content = e.target?.result as string;\n        resolve(content);\n      };\n      reader.onerror = () => reject(new Error('Failed to read file'));\n      reader.readAsText(file);\n    });\n  };\n\n  const processDocxFile = async (file: File): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = async (e) => {\n        try {\n          const arrayBuffer = e.target?.result as ArrayBuffer;\n          const result = await mammoth.extractRawText({ arrayBuffer });\n          resolve(result.value);\n        } catch (error) {\n          reject(new Error('Failed to process DOCX file'));\n        }\n      };\n      reader.onerror = () => reject(new Error('Failed to read file'));\n      reader.readAsArrayBuffer(file);\n    });\n  };\n\n  const extractTitle = (content: string, filename: string): string => {\n    // Try to extract title from first line\n    const lines = content.trim().split('\\n');\n    const firstLine = lines[0]?.trim();\n    \n    if (firstLine && firstLine.length > 0 && firstLine.length < 100) {\n      // Remove common title markers\n      const cleanTitle = firstLine\n        .replace(/^#+\\s*/, '') // Remove markdown headers\n        .replace(/^Title:\\s*/i, '') // Remove \"Title:\" prefix\n        .replace(/^Subject:\\s*/i, '') // Remove \"Subject:\" prefix\n        .trim();\n      \n      if (cleanTitle.length > 0) {\n        return cleanTitle;\n      }\n    }\n    \n    // Fallback to filename without extension\n    return filename.replace(/\\.[^/.]+$/, '');\n  };\n\n  const processFile = async (file: File): Promise<UploadedFile> => {\n    const id = crypto.randomUUID();\n    const uploadedFile: UploadedFile = {\n      id,\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      content: '',\n      title: '',\n      status: 'processing',\n    };\n\n    try {\n      let content: string;\n\n      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {\n        content = await processTextFile(file);\n      } else if (\n        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n        file.name.endsWith('.docx')\n      ) {\n        content = await processDocxFile(file);\n      } else {\n        throw new Error('Unsupported file type. Please upload .txt or .docx files.');\n      }\n\n      const title = extractTitle(content, file.name);\n\n      return {\n        ...uploadedFile,\n        content: content.trim(),\n        title,\n        status: 'success',\n      };\n    } catch (error) {\n      return {\n        ...uploadedFile,\n        status: 'error',\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n      };\n    }\n  };\n\n  const onDrop = useCallback(async (acceptedFiles: File[]) => {\n    setIsProcessing(true);\n    \n    const processedFiles: UploadedFile[] = [];\n    \n    for (const file of acceptedFiles) {\n      const processed = await processFile(file);\n      processedFiles.push(processed);\n      \n      // Update state incrementally\n      setUploadedFiles(prev => [...prev, processed]);\n    }\n    \n    setIsProcessing(false);\n    onFilesProcessed(processedFiles);\n  }, [onFilesProcessed]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'text/plain': ['.txt'],\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],\n    },\n    multiple: true,\n  });\n\n  const removeFile = (id: string) => {\n    setUploadedFiles(prev => prev.filter(file => file.id !== id));\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getFileIcon = (type: string, name: string) => {\n    if (type === 'text/plain' || name.endsWith('.txt')) {\n      return <FileText className=\"w-5 h-5\" />;\n    }\n    return <File className=\"w-5 h-5\" />;\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />;\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />;\n      default:\n        return <div className=\"w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\" />;\n    }\n  };\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n          ${isDragActive \n            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' \n            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n          }\n        `}\n      >\n        <input {...getInputProps()} />\n        <Upload className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\n          {isDragActive ? 'Drop files here' : 'Upload your writings'}\n        </h3>\n        <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n          Drag and drop files here, or click to select files\n        </p>\n        <p className=\"text-sm text-gray-400 dark:text-gray-500\">\n          Supports: .txt, .docx files\n        </p>\n      </div>\n\n      {/* Processing Indicator */}\n      {isProcessing && (\n        <div className=\"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n          <div className=\"w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3\" />\n          <span className=\"text-blue-700 dark:text-blue-300\">Processing files...</span>\n        </div>\n      )}\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n            Uploaded Files ({uploadedFiles.length})\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {uploadedFiles.map((file) => (\n              <div\n                key={file.id}\n                className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\"\n              >\n                <div className=\"flex items-center space-x-3 flex-1\">\n                  <div className=\"text-gray-500 dark:text-gray-400\">\n                    {getFileIcon(file.type, file.name)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2\">\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\n                        {file.title || file.name}\n                      </h4>\n                      {getStatusIcon(file.status)}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400\">\n                      <span>{file.name}</span>\n                      <span>•</span>\n                      <span>{formatFileSize(file.size)}</span>\n                      {file.content && (\n                        <>\n                          <span>•</span>\n                          <span>{file.content.split(/\\s+/).length} words</span>\n                        </>\n                      )}\n                    </div>\n                    \n                    {file.error && (\n                      <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                        {file.error}\n                      </p>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2\">\n                  {file.status === 'success' && (\n                    <button\n                      onClick={() => onImport(file)}\n                      className=\"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n                    >\n                      Import\n                    </button>\n                  )}\n                  \n                  <button\n                    onClick={() => removeFile(file.id)}\n                    className=\"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAwBO,SAAS,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;IACxF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB,OAAO;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,QAAQ;YACV;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,UAAU,CAAC;QACpB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,OAAO;gBACrB,IAAI;oBACF,MAAM,cAAc,EAAE,MAAM,EAAE;oBAC9B,MAAM,SAAS,MAAM,uIAAA,CAAA,UAAO,CAAC,cAAc,CAAC;wBAAE;oBAAY;oBAC1D,QAAQ,OAAO,KAAK;gBACtB,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF;YACA,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACxC,OAAO,iBAAiB,CAAC;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC,SAAiB;QACrC,uCAAuC;QACvC,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,MAAM,YAAY,KAAK,CAAC,EAAE,EAAE;QAE5B,IAAI,aAAa,UAAU,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,KAAK;YAC/D,8BAA8B;YAC9B,MAAM,aAAa,UAChB,OAAO,CAAC,UAAU,IAAI,0BAA0B;aAChD,OAAO,CAAC,eAAe,IAAI,yBAAyB;aACpD,OAAO,CAAC,iBAAiB,IAAI,2BAA2B;aACxD,IAAI;YAEP,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,OAAO;YACT;QACF;QAEA,yCAAyC;QACzC,OAAO,SAAS,OAAO,CAAC,aAAa;IACvC;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,KAAK,OAAO,UAAU;QAC5B,MAAM,eAA6B;YACjC;YACA,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QAEA,IAAI;YACF,IAAI;YAEJ,IAAI,KAAK,IAAI,KAAK,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;gBAC5D,UAAU,MAAM,gBAAgB;YAClC,OAAO,IACL,KAAK,IAAI,KAAK,6EACd,KAAK,IAAI,CAAC,QAAQ,CAAC,UACnB;gBACA,UAAU,MAAM,gBAAgB;YAClC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,QAAQ,aAAa,SAAS,KAAK,IAAI;YAE7C,OAAO;gBACL,GAAG,YAAY;gBACf,SAAS,QAAQ,IAAI;gBACrB;gBACA,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ;gBACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,gBAAgB;QAEhB,MAAM,iBAAiC,EAAE;QAEzC,KAAK,MAAM,QAAQ,cAAe;YAChC,MAAM,YAAY,MAAM,YAAY;YACpC,eAAe,IAAI,CAAC;YAEpB,6BAA6B;YAC7B,iBAAiB,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAC/C;QAEA,gBAAgB;QAChB,iBAAiB;IACnB,GAAG;QAAC;KAAiB;IAErB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,cAAc;gBAAC;aAAO;YACtB,2EAA2E;gBAAC;aAAQ;QACtF;QACA,UAAU;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC3D;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,cAAc,CAAC,MAAc;QACjC,IAAI,SAAS,gBAAgB,KAAK,QAAQ,CAAC,SAAS;YAClD,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC7B;QACA,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC;oBAAI,WAAU;;;;;;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eACE,mDACA,wFACH;QACH,CAAC;;kCAED,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCACX,eAAe,oBAAoB;;;;;;kCAEtC,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCAGrD,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;YAMzD,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;YAKtD,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAuD;4BAClD,cAAc,MAAM;4BAAC;;;;;;;kCAGxC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,YAAY,KAAK,IAAI,EAAE,KAAK,IAAI;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,KAAK,KAAK,IAAI,KAAK,IAAI;;;;;;4DAEzB,cAAc,KAAK,MAAM;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,KAAK,IAAI;;;;;;0EAChB,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,eAAe,KAAK,IAAI;;;;;;4DAC9B,KAAK,OAAO,kBACX;;kFACE,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAM,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM;4EAAC;;;;;;;;;;;;;;;oDAK7C,KAAK,KAAK,kBACT,8OAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;kDAMnB,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,MAAM,KAAK,2BACf,8OAAC;gDACC,SAAS,IAAM,SAAS;gDACxB,WAAU;0DACX;;;;;;0DAKH,8OAAC;gDACC,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BAlDZ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA4D5B", "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/Settings.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Settings as SettingsIcon, \n  Sun, \n  Moon, \n  Monitor, \n  Type, \n  Save,\n  RotateCcw,\n  Palette,\n  Zap,\n  CheckSquare\n} from 'lucide-react';\nimport { useTheme } from './ThemeProvider';\nimport { getLocalStorage, UserSettings } from '@/lib/localStorage';\n\ninterface SettingsProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Settings({ isOpen, onClose }: SettingsProps) {\n  const { theme, setTheme } = useTheme();\n  const [settings, setSettings] = useState<UserSettings | null>(null);\n  const [hasChanges, setHasChanges] = useState(false);\n  const storage = getLocalStorage();\n\n  // Load settings on mount\n  useEffect(() => {\n    const currentSettings = storage.getSettings();\n    setSettings(currentSettings);\n  }, [storage]);\n\n  // Track changes\n  useEffect(() => {\n    if (settings) {\n      const currentSettings = storage.getSettings();\n      const changed = JSON.stringify(settings) !== JSON.stringify(currentSettings);\n      setHasChanges(changed);\n    }\n  }, [settings, storage]);\n\n  const updateSetting = <K extends keyof UserSettings>(\n    key: K, \n    value: UserSettings[K]\n  ) => {\n    if (settings) {\n      setSettings(prev => prev ? { ...prev, [key]: value } : null);\n    }\n  };\n\n  const saveSettings = () => {\n    if (settings) {\n      storage.saveSettings(settings);\n      setHasChanges(false);\n    }\n  };\n\n  const resetSettings = () => {\n    const defaultSettings = storage.getSettings();\n    // Clear localStorage settings to get defaults\n    localStorage.removeItem('journal_settings');\n    const freshDefaults = storage.getSettings();\n    setSettings(freshDefaults);\n    setTheme(freshDefaults.theme);\n  };\n\n  const fontOptions = [\n    { value: 'Inter, system-ui, sans-serif', label: 'Inter (Default)' },\n    { value: 'Georgia, serif', label: 'Georgia' },\n    { value: 'Times New Roman, serif', label: 'Times New Roman' },\n    { value: 'Arial, sans-serif', label: 'Arial' },\n    { value: 'Helvetica, sans-serif', label: 'Helvetica' },\n    { value: 'Courier New, monospace', label: 'Courier New' },\n    { value: 'Roboto, sans-serif', label: 'Roboto' },\n    { value: 'Open Sans, sans-serif', label: 'Open Sans' },\n  ];\n\n  if (!isOpen || !settings) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-2\">\n            <SettingsIcon className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n              Settings\n            </h2>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            ×\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 space-y-8\">\n          {/* Theme Settings */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Palette className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                Appearance\n              </h3>\n            </div>\n            \n            <div className=\"space-y-3\">\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                Theme\n              </label>\n              <div className=\"grid grid-cols-3 gap-3\">\n                {[\n                  { value: 'light', label: 'Light', icon: Sun },\n                  { value: 'dark', label: 'Dark', icon: Moon },\n                  { value: 'system', label: 'System', icon: Monitor },\n                ].map(({ value, label, icon: Icon }) => (\n                  <button\n                    key={value}\n                    onClick={() => {\n                      setTheme(value as any);\n                      updateSetting('theme', value as any);\n                    }}\n                    className={`\n                      flex flex-col items-center p-3 rounded-lg border-2 transition-colors\n                      ${theme === value\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                      }\n                    `}\n                  >\n                    <Icon className=\"w-5 h-5 mb-1\" />\n                    <span className=\"text-sm\">{label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Typography Settings */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Type className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                Typography\n              </h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {/* Font Size */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Font Size: {settings.fontSize}px\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"12\"\n                  max=\"24\"\n                  value={settings.fontSize}\n                  onChange={(e) => updateSetting('fontSize', parseInt(e.target.value))}\n                  className=\"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                />\n                <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n                  <span>12px</span>\n                  <span>24px</span>\n                </div>\n              </div>\n\n              {/* Font Family */}\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Font Family\n                </label>\n                <select\n                  value={settings.fontFamily}\n                  onChange={(e) => updateSetting('fontFamily', e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                >\n                  {fontOptions.map((font) => (\n                    <option key={font.value} value={font.value}>\n                      {font.label}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            {/* Preview */}\n            <div className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">Preview:</p>\n              <p \n                style={{ \n                  fontSize: `${settings.fontSize}px`,\n                  fontFamily: settings.fontFamily \n                }}\n                className=\"text-gray-900 dark:text-gray-100\"\n              >\n                The quick brown fox jumps over the lazy dog. This is how your journal text will appear.\n              </p>\n            </div>\n          </div>\n\n          {/* Auto-save Settings */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                Auto-save\n              </h3>\n            </div>\n\n            <div className=\"space-y-4\">\n              {/* Auto-save Toggle */}\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Enable Auto-save\n                  </label>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Automatically save your work as you type\n                  </p>\n                </div>\n                <button\n                  onClick={() => updateSetting('autoSave', !settings.autoSave)}\n                  className={`\n                    relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                    ${settings.autoSave ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                  `}\n                >\n                  <span\n                    className={`\n                      inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                      ${settings.autoSave ? 'translate-x-6' : 'translate-x-1'}\n                    `}\n                  />\n                </button>\n              </div>\n\n              {/* Auto-save Interval */}\n              {settings.autoSave && (\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Auto-save Interval: {settings.autoSaveInterval / 1000}s\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"1000\"\n                    max=\"10000\"\n                    step=\"1000\"\n                    value={settings.autoSaveInterval}\n                    onChange={(e) => updateSetting('autoSaveInterval', parseInt(e.target.value))}\n                    className=\"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                  />\n                  <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400\">\n                    <span>1s</span>\n                    <span>10s</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Spell Check */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <CheckSquare className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n                Writing Assistance\n              </h3>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Spell Check\n                </label>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Enable browser spell checking\n                </p>\n              </div>\n              <button\n                onClick={() => updateSetting('spellCheck', !settings.spellCheck)}\n                className={`\n                  relative inline-flex h-6 w-11 items-center rounded-full transition-colors\n                  ${settings.spellCheck ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'}\n                `}\n              >\n                <span\n                  className={`\n                    inline-block h-4 w-4 transform rounded-full bg-white transition-transform\n                    ${settings.spellCheck ? 'translate-x-6' : 'translate-x-1'}\n                  `}\n                />\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700\">\n          <button\n            onClick={resetSettings}\n            className=\"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n          >\n            <RotateCcw className=\"w-4 h-4\" />\n            <span>Reset to Defaults</span>\n          </button>\n\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={() => {\n                saveSettings();\n                onClose();\n              }}\n              disabled={!hasChanges}\n              className={`\n                flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors\n                ${hasChanges\n                  ? 'bg-blue-600 text-white hover:bg-blue-700'\n                  : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'\n                }\n              `}\n            >\n              <Save className=\"w-4 h-4\" />\n              <span>Save Changes</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAhBA;;;;;;AAuBO,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAiB;IACzD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;IAE9B,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,QAAQ,WAAW;QAC3C,YAAY;IACd,GAAG;QAAC;KAAQ;IAEZ,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,kBAAkB,QAAQ,WAAW;YAC3C,MAAM,UAAU,KAAK,SAAS,CAAC,cAAc,KAAK,SAAS,CAAC;YAC5D,cAAc;QAChB;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,gBAAgB,CACpB,KACA;QAEA,IAAI,UAAU;YACZ,YAAY,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,CAAC,IAAI,EAAE;gBAAM,IAAI;QACzD;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,QAAQ,YAAY,CAAC;YACrB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,kBAAkB,QAAQ,WAAW;QAC3C,8CAA8C;QAC9C,aAAa,UAAU,CAAC;QACxB,MAAM,gBAAgB,QAAQ,WAAW;QACzC,YAAY;QACZ,SAAS,cAAc,KAAK;IAC9B;IAEA,MAAM,cAAc;QAClB;YAAE,OAAO;YAAgC,OAAO;QAAkB;QAClE;YAAE,OAAO;YAAkB,OAAO;QAAU;QAC5C;YAAE,OAAO;YAA0B,OAAO;QAAkB;QAC5D;YAAE,OAAO;YAAqB,OAAO;QAAQ;QAC7C;YAAE,OAAO;YAAyB,OAAO;QAAY;QACrD;YAAE,OAAO;YAA0B,OAAO;QAAc;QACxD;YAAE,OAAO;YAAsB,OAAO;QAAS;QAC/C;YAAE,OAAO;YAAyB,OAAO;QAAY;KACtD;IAED,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO;IAEjC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;;;;;;;sCAIzE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;;;;;;;8CAKvE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAuD;;;;;;sDAGxE,8OAAC;4CAAI,WAAU;sDACZ;gDACC;oDAAE,OAAO;oDAAS,OAAO;oDAAS,MAAM,gMAAA,CAAA,MAAG;gDAAC;gDAC5C;oDAAE,OAAO;oDAAQ,OAAO;oDAAQ,MAAM,kMAAA,CAAA,OAAI;gDAAC;gDAC3C;oDAAE,OAAO;oDAAU,OAAO;oDAAU,MAAM,wMAAA,CAAA,UAAO;gDAAC;6CACnD,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBACjC,8OAAC;oDAEC,SAAS;wDACP,SAAS;wDACT,cAAc,SAAS;oDACzB;oDACA,WAAW,CAAC;;sBAEV,EAAE,UAAU,QACR,mDACA,wFACH;oBACH,CAAC;;sEAED,8OAAC;4DAAK,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAW;;;;;;;mDAdtB;;;;;;;;;;;;;;;;;;;;;;sCAsBf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;;;;;;;8CAKvE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;wDAAuD;wDAC1D,SAAS,QAAQ;wDAAC;;;;;;;8DAEhC,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,cAAc,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAKV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAuD;;;;;;8DAGxE,8OAAC;oDACC,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,cAAc,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC3D,WAAU;8DAET,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;4DAAwB,OAAO,KAAK,KAAK;sEACvC,KAAK,KAAK;2DADA,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;8CAS/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAgD;;;;;;sDAC7D,8OAAC;4CACC,OAAO;gDACL,UAAU,GAAG,SAAS,QAAQ,CAAC,EAAE,CAAC;gDAClC,YAAY,SAAS,UAAU;4CACjC;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;;;;;;;8CAKvE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAuD;;;;;;sEAGxE,8OAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;8DAI1D,8OAAC;oDACC,SAAS,IAAM,cAAc,YAAY,CAAC,SAAS,QAAQ;oDAC3D,WAAW,CAAC;;oBAEV,EAAE,SAAS,QAAQ,GAAG,gBAAgB,+BAA+B;kBACvE,CAAC;8DAED,cAAA,8OAAC;wDACC,WAAW,CAAC;;sBAEV,EAAE,SAAS,QAAQ,GAAG,kBAAkB,gBAAgB;oBAC1D,CAAC;;;;;;;;;;;;;;;;;wCAMN,SAAS,QAAQ,kBAChB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;;wDAAuD;wDACjD,SAAS,gBAAgB,GAAG;wDAAK;;;;;;;8DAExD,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU,CAAC,IAAM,cAAc,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1E,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;;;;;;;8CAKvE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAuD;;;;;;8DAGxE,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAI1D,8OAAC;4CACC,SAAS,IAAM,cAAc,cAAc,CAAC,SAAS,UAAU;4CAC/D,WAAW,CAAC;;kBAEV,EAAE,SAAS,UAAU,GAAG,gBAAgB,+BAA+B;gBACzE,CAAC;sDAED,cAAA,8OAAC;gDACC,WAAW,CAAC;;oBAEV,EAAE,SAAS,UAAU,GAAG,kBAAkB,gBAAgB;kBAC5D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAK;;;;;;;;;;;;sCAGR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP;wCACA;oCACF;oCACA,UAAU,CAAC;oCACX,WAAW,CAAC;;gBAEV,EAAE,aACE,6CACA,mFACH;cACH,CAAC;;sDAED,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AllJournal/journal-app/src/components/JournalApp.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  PlusCircle, \n  Search, \n  Settings as SettingsIcon, \n  Upload, \n  BookOpen,\n  Menu,\n  X,\n  ZoomIn,\n  ZoomOut\n} from 'lucide-react';\nimport { TextEditor } from './TextEditor';\nimport { TagManager } from './TagManager';\nimport { FileUpload } from './FileUpload';\nimport { Settings } from './Settings';\nimport { getLocalStorage } from '@/lib/localStorage';\nimport { JournalEntry } from '@/lib/database';\n\ninterface JournalAppProps {\n  className?: string;\n}\n\nexport function JournalApp({ className = '' }: JournalAppProps) {\n  const [currentEntry, setCurrentEntry] = useState<JournalEntry | null>(null);\n  const [entries, setEntries] = useState<JournalEntry[]>([]);\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showUpload, setShowUpload] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n  const [zoom, setZoom] = useState(100);\n  \n  const storage = getLocalStorage();\n  const settings = storage.getSettings();\n\n  // Load cached entries on mount\n  useEffect(() => {\n    const cached = storage.getCachedEntries();\n    if (cached) {\n      setEntries(cached.entries);\n    }\n  }, [storage]);\n\n  // Filter entries based on search and tags\n  const filteredEntries = entries.filter(entry => {\n    const matchesSearch = !searchQuery || \n      entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      entry.content.toLowerCase().includes(searchQuery.toLowerCase());\n    \n    const matchesTags = selectedTags.length === 0 || \n      selectedTags.every(tag => entry.tags.includes(tag));\n    \n    return matchesSearch && matchesTags;\n  });\n\n  const createNewEntry = () => {\n    const newEntry: JournalEntry = {\n      id: crypto.randomUUID(),\n      title: 'Untitled Entry',\n      content: '',\n      tags: [],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      word_count: 0,\n    };\n    \n    setCurrentEntry(newEntry);\n    setEntries(prev => [newEntry, ...prev]);\n    storage.cacheEntry(newEntry);\n  };\n\n  const saveEntry = (data: { title: string; content: string; tags: string[] }) => {\n    if (!currentEntry) return;\n\n    const updatedEntry: JournalEntry = {\n      ...currentEntry,\n      title: data.title || 'Untitled Entry',\n      content: data.content,\n      tags: data.tags,\n      updated_at: new Date().toISOString(),\n      word_count: data.content.trim().split(/\\s+/).filter(word => word.length > 0).length,\n    };\n\n    setCurrentEntry(updatedEntry);\n    setEntries(prev => prev.map(entry => \n      entry.id === updatedEntry.id ? updatedEntry : entry\n    ));\n    storage.cacheEntry(updatedEntry);\n  };\n\n  const selectEntry = (entry: JournalEntry) => {\n    setCurrentEntry(entry);\n  };\n\n  const deleteEntry = (entryId: string) => {\n    setEntries(prev => prev.filter(entry => entry.id !== entryId));\n    storage.removeCachedEntry(entryId);\n    \n    if (currentEntry?.id === entryId) {\n      setCurrentEntry(null);\n    }\n  };\n\n  const handleSearch = (query: string, tags: string[]) => {\n    setSearchQuery(query);\n    setSelectedTags(tags);\n  };\n\n  const handleFileImport = (file: any) => {\n    const newEntry: JournalEntry = {\n      id: crypto.randomUUID(),\n      title: file.title,\n      content: file.content,\n      tags: [],\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      word_count: file.content.split(/\\s+/).filter((word: string) => word.length > 0).length,\n    };\n    \n    setEntries(prev => [newEntry, ...prev]);\n    storage.cacheEntry(newEntry);\n    setCurrentEntry(newEntry);\n    setShowUpload(false);\n  };\n\n  const adjustZoom = (delta: number) => {\n    const newZoom = Math.max(50, Math.min(200, zoom + delta));\n    setZoom(newZoom);\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric',\n    });\n  };\n\n  return (\n    <div className={`flex h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>\n      {/* Sidebar */}\n      <div className={`\n        ${sidebarOpen ? 'w-80' : 'w-0'} \n        transition-all duration-300 overflow-hidden\n        bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\n        flex flex-col\n      `}>\n        {/* Sidebar Header */}\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h1 className=\"text-xl font-bold text-gray-900 dark:text-gray-100\">\n              AllJournal\n            </h1>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => adjustZoom(-10)}\n                className=\"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\"\n                title=\"Zoom Out\"\n              >\n                <ZoomOut className=\"w-4 h-4\" />\n              </button>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {zoom}%\n              </span>\n              <button\n                onClick={() => adjustZoom(10)}\n                className=\"p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300\"\n                title=\"Zoom In\"\n              >\n                <ZoomIn className=\"w-4 h-4\" />\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-2\">\n            <button\n              onClick={createNewEntry}\n              className=\"flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <PlusCircle className=\"w-4 h-4\" />\n              <span>New Entry</span>\n            </button>\n            \n            <button\n              onClick={() => setShowUpload(true)}\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              title=\"Upload Files\"\n            >\n              <Upload className=\"w-4 h-4\" />\n            </button>\n            \n            <button\n              onClick={() => setShowSettings(true)}\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              title=\"Settings\"\n            >\n              <SettingsIcon className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Tag Manager */}\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n          <TagManager\n            selectedTags={selectedTags}\n            onTagsChange={setSelectedTags}\n            onSearch={handleSearch}\n          />\n        </div>\n\n        {/* Entries List */}\n        <div className=\"flex-1 overflow-y-auto\">\n          <div className=\"p-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                Entries ({filteredEntries.length})\n              </h3>\n            </div>\n            \n            <div className=\"space-y-2\">\n              {filteredEntries.map((entry) => (\n                <div\n                  key={entry.id}\n                  onClick={() => selectEntry(entry)}\n                  className={`\n                    p-3 rounded-lg cursor-pointer transition-colors\n                    ${currentEntry?.id === entry.id\n                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'\n                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'\n                    }\n                  `}\n                >\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100 truncate\">\n                    {entry.title}\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\">\n                    {entry.content || 'No content'}\n                  </p>\n                  <div className=\"flex items-center justify-between mt-2\">\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {formatDate(entry.updated_at)}\n                    </span>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {entry.word_count} words\n                    </span>\n                  </div>\n                  {entry.tags.length > 0 && (\n                    <div className=\"flex flex-wrap gap-1 mt-2\">\n                      {entry.tags.slice(0, 3).map((tag) => (\n                        <span\n                          key={tag}\n                          className=\"text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded\"\n                        >\n                          #{tag}\n                        </span>\n                      ))}\n                      {entry.tags.length > 3 && (\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          +{entry.tags.length - 3}\n                        </span>\n                      )}\n                    </div>\n                  )}\n                </div>\n              ))}\n              \n              {filteredEntries.length === 0 && (\n                <div className=\"text-center py-8\">\n                  <BookOpen className=\"w-12 h-12 mx-auto text-gray-400 mb-3\" />\n                  <p className=\"text-gray-500 dark:text-gray-400\">\n                    {entries.length === 0 ? 'No entries yet' : 'No entries match your search'}\n                  </p>\n                  {entries.length === 0 && (\n                    <button\n                      onClick={createNewEntry}\n                      className=\"mt-2 text-blue-600 dark:text-blue-400 hover:underline\"\n                    >\n                      Create your first entry\n                    </button>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Top Bar */}\n        <div className=\"flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n          <button\n            onClick={() => setSidebarOpen(!sidebarOpen)}\n            className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n          >\n            {sidebarOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n          </button>\n          \n          {currentEntry && (\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                Last saved: {formatDate(currentEntry.updated_at)}\n              </span>\n              <button\n                onClick={() => deleteEntry(currentEntry.id)}\n                className=\"text-sm text-red-600 dark:text-red-400 hover:underline\"\n              >\n                Delete Entry\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Editor */}\n        <div className=\"flex-1\" style={{ zoom: `${zoom}%` }}>\n          {currentEntry ? (\n            <TextEditor\n              entryId={currentEntry.id}\n              initialTitle={currentEntry.title}\n              initialContent={currentEntry.content}\n              initialTags={currentEntry.tags}\n              onSave={saveEntry}\n              onAutoSave={saveEntry}\n              className=\"h-full\"\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <BookOpen className=\"w-16 h-16 mx-auto text-gray-400 mb-4\" />\n                <h2 className=\"text-xl font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Welcome to AllJournal\n                </h2>\n                <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                  Select an entry from the sidebar or create a new one to start writing.\n                </p>\n                <button\n                  onClick={createNewEntry}\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto\"\n                >\n                  <PlusCircle className=\"w-4 h-4\" />\n                  <span>Create New Entry</span>\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Modals */}\n      {showSettings && (\n        <Settings\n          isOpen={showSettings}\n          onClose={() => setShowSettings(false)}\n        />\n      )}\n\n      {showUpload && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n                Import Files\n              </h2>\n              <button\n                onClick={() => setShowUpload(false)}\n                className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n              >\n                <X className=\"w-5 h-5\" />\n              </button>\n            </div>\n            <div className=\"p-6\">\n              <FileUpload\n                onFilesProcessed={() => {}}\n                onImport={handleFileImport}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;AAyBO,SAAS,WAAW,EAAE,YAAY,EAAE,EAAmB;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD;IAC9B,MAAM,WAAW,QAAQ,WAAW;IAEpC,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,QAAQ,gBAAgB;QACvC,IAAI,QAAQ;YACV,WAAW,OAAO,OAAO;QAC3B;IACF,GAAG;QAAC;KAAQ;IAEZ,0CAA0C;IAC1C,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,CAAC,eACrB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC1D,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE9D,MAAM,cAAc,aAAa,MAAM,KAAK,KAC1C,aAAa,KAAK,CAAC,CAAA,MAAO,MAAM,IAAI,CAAC,QAAQ,CAAC;QAEhD,OAAO,iBAAiB;IAC1B;IAEA,MAAM,iBAAiB;QACrB,MAAM,WAAyB;YAC7B,IAAI,OAAO,UAAU;YACrB,OAAO;YACP,SAAS;YACT,MAAM,EAAE;YACR,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY;QACd;QAEA,gBAAgB;QAChB,WAAW,CAAA,OAAQ;gBAAC;mBAAa;aAAK;QACtC,QAAQ,UAAU,CAAC;IACrB;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,cAAc;QAEnB,MAAM,eAA6B;YACjC,GAAG,YAAY;YACf,OAAO,KAAK,KAAK,IAAI;YACrB,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;YACf,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,KAAK,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;QACrF;QAEA,gBAAgB;QAChB,WAAW,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAC1B,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG,eAAe;QAEhD,QAAQ,UAAU,CAAC;IACrB;IAEA,MAAM,cAAc,CAAC;QACnB,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,QAAQ,iBAAiB,CAAC;QAE1B,IAAI,cAAc,OAAO,SAAS;YAChC,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,eAAe;QACf,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAyB;YAC7B,IAAI,OAAO,UAAU;YACrB,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,OAAO;YACrB,MAAM,EAAE;YACR,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,CAAC,OAAiB,KAAK,MAAM,GAAG,GAAG,MAAM;QACxF;QAEA,WAAW,CAAA,OAAQ;gBAAC;mBAAa;aAAK;QACtC,QAAQ,UAAU,CAAC;QACnB,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,OAAO;QAClD,QAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BAEtE,8OAAC;gBAAI,WAAW,CAAC;QACf,EAAE,cAAc,SAAS,MAAM;;;;MAIjC,CAAC;;kCAEC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,WAAW,CAAC;gDAC3B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDAAK,WAAU;;oDACb;oDAAK;;;;;;;0DAER,8OAAC;gDACC,SAAS,IAAM,WAAW;gDAC1B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAGpB,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;4BACT,cAAc;4BACd,cAAc;4BACd,UAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;4CAAuD;4CACzD,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;8CAIrC,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,GAAG,CAAC,CAAC,sBACpB,8OAAC;gDAEC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC;;oBAEV,EAAE,cAAc,OAAO,MAAM,EAAE,GAC3B,+EACA,0CACH;kBACH,CAAC;;kEAED,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,8OAAC;wDAAE,WAAU;kEACV,MAAM,OAAO,IAAI;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,WAAW,MAAM,UAAU;;;;;;0EAE9B,8OAAC;gEAAK,WAAU;;oEACb,MAAM,UAAU;oEAAC;;;;;;;;;;;;;oDAGrB,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC3B,8OAAC;oEAEC,WAAU;;wEACX;wEACG;;mEAHG;;;;;4DAMR,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC;gEAAK,WAAU;;oEAA2C;oEACvD,MAAM,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;+CApCzB,MAAM,EAAE;;;;;wCA4ChB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,MAAM,KAAK,IAAI,mBAAmB;;;;;;gDAE5C,QAAQ,MAAM,KAAK,mBAClB,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;0CAET,4BAAc,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;4BAG5D,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAA2C;4CAC5C,WAAW,aAAa,UAAU;;;;;;;kDAEjD,8OAAC;wCACC,SAAS,IAAM,YAAY,aAAa,EAAE;wCAC1C,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;wBAAS,OAAO;4BAAE,MAAM,GAAG,KAAK,CAAC,CAAC;wBAAC;kCAC/C,6BACC,8OAAC,gIAAA,CAAA,aAAU;4BACT,SAAS,aAAa,EAAE;4BACxB,cAAc,aAAa,KAAK;4BAChC,gBAAgB,aAAa,OAAO;4BACpC,aAAa,aAAa,IAAI;4BAC9B,QAAQ;4BACR,YAAY;4BACZ,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASjB,8BACC,8OAAC,8HAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,gBAAgB;;;;;;YAIlC,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,8OAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;gCACT,kBAAkB,KAAO;gCACzB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B", "debugId": null}}]}