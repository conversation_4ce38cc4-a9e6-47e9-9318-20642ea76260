import { JournalEntry } from './database';

export const sampleEntries: JournalEntry[] = [
  {
    id: 'sample-1',
    title: 'My First Journal Entry',
    content: 'Today was a great day! I learned about React and TypeScript. The weather was beautiful and I went for a walk in the park.',
    tags: ['personal', 'learning', 'react', 'typescript'],
    created_at: '2024-01-15T10:00:00.000Z',
    updated_at: '2024-01-15T10:30:00.000Z',
    word_count: 25,
  },
  {
    id: 'sample-2',
    title: 'Work Progress Update',
    content: 'Made significant progress on the journal app today. Implemented the tagging system and search functionality. Need to test the mobile responsiveness next.',
    tags: ['work', 'programming', 'journal-app', 'mobile'],
    created_at: '2024-01-16T14:00:00.000Z',
    updated_at: '2024-01-16T14:45:00.000Z',
    word_count: 28,
  },
  {
    id: 'sample-3',
    title: 'Weekend Plans',
    content: 'Planning to visit the museum this weekend. Also want to try that new restaurant downtown. Should be a relaxing weekend after a busy week.',
    tags: ['weekend', 'plans', 'museum', 'restaurant'],
    created_at: '2024-01-17T09:00:00.000Z',
    updated_at: '2024-01-17T09:15:00.000Z',
    word_count: 26,
  },
];

export function loadSampleData() {
  if (typeof window !== 'undefined') {
    const storage = localStorage.getItem('journal_entries');
    if (!storage) {
      // Only load sample data if no entries exist
      const sampleData = {
        entries: sampleEntries,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem('journal_entries', JSON.stringify(sampleData));
      
      // Also cache the tags
      const allTags = new Set<string>();
      sampleEntries.forEach(entry => {
        entry.tags.forEach(tag => allTags.add(tag));
      });
      const tagsData = {
        tags: [...allTags],
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem('journal_tags', JSON.stringify(tagsData));
      
      return sampleEntries;
    }
  }
  return [];
}
