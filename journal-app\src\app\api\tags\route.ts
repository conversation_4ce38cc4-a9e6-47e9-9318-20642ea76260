import { NextRequest, NextResponse } from 'next/server';
import { Tag, ApiResponse } from '@/lib/types';
import JournalDatabase from '@/lib/database';

// Initialize database (in production, this would be a singleton)
let db: JournalDatabase;

function getDatabase() {
  if (!db) {
    db = new JournalDatabase();
  }
  return db;
}

// GET /api/tags - Get all tags
export async function GET(request: NextRequest) {
  try {
    const database = getDatabase();
    const tags = database.getAllTags();
    
    return NextResponse.json({
      success: true,
      data: tags
    } as ApiResponse<Tag[]>);
    
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch tags'
    } as ApiResponse<never>, { status: 500 });
  }
}

// POST /api/tags - Create a new tag
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, color } = body;
    
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Tag name is required'
      } as ApiResponse<never>, { status: 400 });
    }
    
    const database = getDatabase();
    
    // Check if tag already exists
    const existingTags = database.getAllTags();
    const existingTag = existingTags.find(tag => 
      tag.name.toLowerCase() === name.trim().toLowerCase()
    );
    
    if (existingTag) {
      return NextResponse.json({
        success: false,
        error: 'Tag already exists'
      } as ApiResponse<never>, { status: 409 });
    }
    
    const newTag = database.createTag(name.trim(), color);
    
    return NextResponse.json({
      success: true,
      data: newTag
    } as ApiResponse<Tag>, { status: 201 });
    
  } catch (error) {
    console.error('Error creating tag:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create tag'
    } as ApiResponse<never>, { status: 500 });
  }
}
